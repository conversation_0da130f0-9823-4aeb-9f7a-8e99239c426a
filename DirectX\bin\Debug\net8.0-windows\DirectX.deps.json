{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"DirectX/1.0.0": {"dependencies": {"ComputeSharp": "3.2.0", "ComputeSharp.D2D1": "3.2.0", "ComputeSharp.Dxc": "3.2.0", "ConcurrencyVisualizer": "3.0.0", "EditeurAudioVideoCpp": "1.0.0", "HelixToolkit": "2.27.0", "HelixToolkit.SharpDX.Assimp": "2.27.0", "HelixToolkit.SharpDX.Core": "2.27.0", "HelixToolkit.SharpDX.Core.Wpf": "2.27.0", "MediaFoundation.NetCore": "2024.5.10", "Microsoft.VisualStudio.Threading": "17.13.61", "PrecisionTimer.NET": "*******", "Quamotion.TurboJpegWrapper": "2.0.32", "SharpGen.Runtime.COM": "2.2.0-beta", "System.Net.Http": "4.3.4", "System.Text.RegularExpressions": "4.3.1", "Utils": "1.0.0", "Vanara.PInvoke.Kernel32": "4.1.2", "Vanara.PInvoke.Ole": "4.1.2", "Vanara.PInvoke.User32": "4.1.2", "Vortice.D3DCompiler": "3.6.2", "Vortice.DXGI": "3.6.2", "Vortice.Direct2D1": "3.6.2", "Vortice.Direct3D11": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectML": "3.6.2", "Vortice.DirectX": "3.6.2", "Vortice.Dxc": "3.6.2", "Vortice.Mathematics": "1.9.3", "Vortice.XAudio2": "3.6.2"}, "runtime": {"DirectX.dll": {}}}, "AssimpNet/5.0.0-beta1": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.3/AssimpNet.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}, "runtimeTargets": {"runtimes/linux-x64/native/libassimp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libassimp.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/assimp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "5.0.0.0"}, "runtimes/win-x86/native/assimp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "5.0.0.0"}}}, "Ceras/4.1.7": {"dependencies": {"System.Buffers": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "runtime": {"lib/netstandard2.0/Ceras.dll": {"assemblyVersion": "4.1.7.0", "fileVersion": "4.1.7.0"}}}, "ComputeSharp/3.2.0": {"dependencies": {"ComputeSharp.Core": "3.2.0"}, "runtime": {"lib/net8.0/ComputeSharp.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ComputeSharp.Core/3.2.0": {"runtime": {"lib/net8.0/ComputeSharp.Core.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ComputeSharp.D2D1/3.2.0": {"dependencies": {"ComputeSharp.Core": "3.2.0"}, "runtime": {"lib/net8.0/ComputeSharp.D2D1.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ComputeSharp.Dxc/3.2.0": {"dependencies": {"ComputeSharp": "3.2.0"}, "runtime": {"lib/net8.0/ComputeSharp.Dxc.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ConcurrencyVisualizer/3.0.0": {"dependencies": {"System.Security.Permissions": "4.7.0"}, "runtime": {"lib/.NETStandard20/Microsoft.ConcurrencyVisualizer.Markers.dll": {"assemblyVersion": "16.0.0.0", "fileVersion": "16.0.15122.3"}}}, "Cyotek.Drawing.BitmapFont/2.0.0": {"runtime": {"lib/netcoreapp3.1/Cyotek.Drawing.BitmapFont.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "2.0.0.0"}}}, "HelixToolkit/2.27.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/HelixToolkit.dll": {"assemblyVersion": "2.27.0.0", "fileVersion": "2.27.0.0"}}}, "HelixToolkit.SharpDX.Assimp/2.27.0": {"dependencies": {"AssimpNet": "5.0.0-beta1", "HelixToolkit.SharpDX.Core": "2.27.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "SharpDX": "4.2.0", "SharpDX.Direct3D11": "4.2.0", "SharpDX.Mathematics": "4.2.0"}, "runtime": {"lib/netstandard2.0/HelixToolkit.SharpDX.Core.Assimp.dll": {"assemblyVersion": "2.27.0.0", "fileVersion": "2.27.0.0"}}}, "HelixToolkit.SharpDX.Core/2.27.0": {"dependencies": {"Cyotek.Drawing.BitmapFont": "2.0.0", "HelixToolkit": "2.27.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "SharpDX": "4.2.0", "SharpDX.D3DCompiler": "4.2.0", "SharpDX.DXGI": "4.2.0", "SharpDX.Direct2D1": "4.2.0", "SharpDX.Direct3D11": "4.2.0", "SharpDX.Mathematics": "4.2.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Drawing.Primitives": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "runtime": {"lib/netstandard2.0/HelixToolkit.SharpDX.Core.dll": {"assemblyVersion": "2.27.0.0", "fileVersion": "2.27.0.0"}}}, "HelixToolkit.SharpDX.Core.Wpf/2.27.0": {"dependencies": {"Cyotek.Drawing.BitmapFont": "2.0.0", "HelixToolkit": "2.27.0", "HelixToolkit.SharpDX.Core": "2.27.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "SharpDX": "4.2.0", "SharpDX.D3DCompiler": "4.2.0", "SharpDX.DXGI": "4.2.0", "SharpDX.Direct2D1": "4.2.0", "SharpDX.Direct3D11": "4.2.0", "SharpDX.Direct3D9": "4.2.0", "SharpDX.Mathematics": "4.2.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Drawing.Primitives": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "runtime": {"lib/netcoreapp3.1/HelixToolkit.SharpDX.Core.Wpf.dll": {"assemblyVersion": "2.27.0.0", "fileVersion": "2.27.0.0"}}}, "MediaFoundation.NetCore/2024.5.10": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/net8.0-windows7.0/MediaFoundation.dll": {"assemblyVersion": "2024.5.10.0", "fileVersion": "2024.5.10.0"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.3": {}, "Microsoft.VisualStudio.Threading/17.13.61": {"dependencies": {"Microsoft.VisualStudio.Threading.Analyzers": "17.13.61", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8"}}, "Microsoft.VisualStudio.Threading.Analyzers/17.13.61": {}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "17.13.0.0", "fileVersion": "17.13.61.36374"}}, "resources": {"lib/net8.0-windows7.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net8.0-windows7.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net8.0-windows7.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net8.0-windows7.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net8.0-windows7.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net8.0-windows7.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net8.0-windows7.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net8.0-windows7.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net8.0-windows7.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net8.0-windows7.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net8.0-windows7.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net8.0-windows7.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0-windows7.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.8.0.0", "fileVersion": "17.8.8.15457"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.4", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "PrecisionTimer.NET/*******": {"runtime": {"lib/netstandard2.0/PrecisionTimer.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Quamotion.TurboJpegWrapper/2.0.32": {"dependencies": {"Validation": "2.5.42"}, "runtime": {"lib/net5.0/Quamotion.TurboJpegWrapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.32.48778"}}, "runtimeTargets": {"runtimes/osx-x64/native/libturbojpeg.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/turbojpeg.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.2.0.0"}, "runtimes/win-x64/native/vcruntime140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.0.24245.0"}, "runtimes/win-x86/native/turbojpeg.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.2.0.0"}, "runtimes/win-x86/native/vcruntime140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.0.24245.0"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SharpDX/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.1/SharpDX.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpDX.D3DCompiler/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "runtime": {"lib/netstandard1.1/SharpDX.D3DCompiler.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpDX.Direct2D1/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0", "SharpDX.DXGI": "4.2.0"}, "runtime": {"lib/netstandard1.1/SharpDX.Direct2D1.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpDX.Direct3D11/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0", "SharpDX.DXGI": "4.2.0"}, "runtime": {"lib/netstandard1.1/SharpDX.Direct3D11.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpDX.Direct3D9/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "runtime": {"lib/netstandard1.3/SharpDX.Direct3D9.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpDX.DXGI/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "runtime": {"lib/netstandard1.1/SharpDX.DXGI.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpDX.Mathematics/4.2.0": {"dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "runtime": {"lib/netstandard1.1/SharpDX.Mathematics.dll": {"assemblyVersion": "4.2.0.0", "fileVersion": "4.2.0.0"}}}, "SharpGen.Runtime/2.2.0-beta": {"runtime": {"lib/net8.0/SharpGen.Runtime.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "SharpGen.Runtime.COM/2.2.0-beta": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta"}, "runtime": {"lib/net8.0/SharpGen.Runtime.COM.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.DiagnosticSource/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Drawing.Common/9.0.4": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.4"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16312"}, "lib/net8.0/System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16312"}}}, "System.Drawing.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Net.Http/4.3.4": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0"}}, "System.Private.DataContractSerialization/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime/4.3.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3"}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1"}}, "System.Runtime.Serialization.Xml/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Windows.Extensions": "4.7.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1", "System.Text.Encoding": "4.3.0"}}, "System.Text.RegularExpressions/4.3.1": {"dependencies": {"System.Runtime": "4.3.1"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Threading.Tasks.Extensions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.1", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.3", "System.Runtime": "4.3.1"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "9.0.4"}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlSerializer/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.1", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.1", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}}, "Validation/2.5.42": {"dependencies": {"System.Runtime.InteropServices": "4.3.0"}, "runtime": {"lib/netstandard2.0/Validation.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.42.18049"}}}, "Vanara.Core/4.1.2": {"runtime": {"lib/net8.0-windows7.0/Vanara.Core.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}, "resources": {"lib/net8.0-windows7.0/fr/Vanara.Core.resources.dll": {"locale": "fr"}}}, "Vanara.PInvoke.Cryptography/4.1.2": {"dependencies": {"Vanara.PInvoke.Shared": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Cryptography.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.Gdi32/4.1.2": {"dependencies": {"Vanara.PInvoke.Shared": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.Kernel32/4.1.2": {"dependencies": {"Vanara.PInvoke.Shared": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.Ole/4.1.2": {"dependencies": {"Vanara.PInvoke.Rpc": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Ole.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.Rpc/4.1.2": {"dependencies": {"Vanara.PInvoke.Security": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Rpc.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.Security/4.1.2": {"dependencies": {"Vanara.PInvoke.Cryptography": "4.1.2", "Vanara.PInvoke.Kernel32": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Security.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.Shared/4.1.2": {"dependencies": {"Vanara.Core": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Shared.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vanara.PInvoke.User32/4.1.2": {"dependencies": {"Vanara.PInvoke.Gdi32": "4.1.2", "Vanara.PInvoke.Kernel32": "4.1.2"}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.User32.dll": {"assemblyVersion": "4.1.2.0", "fileVersion": "4.1.2.0"}}}, "Vortice.D3DCompiler/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.D3DCompiler.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Direct2D1/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.Direct2D1.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Direct3D11/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.Direct3D11.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Direct3D12/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.Direct3D12.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.DirectML/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.DirectML.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.DirectX/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "SharpGen.Runtime.COM": "2.2.0-beta", "Vortice.Mathematics": "1.9.3"}, "runtime": {"lib/net8.0/Vortice.DirectX.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Dxc/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "SharpGen.Runtime.COM": "2.2.0-beta", "Vortice.Dxc.Native": "1.0.2"}, "runtime": {"lib/net8.0/Vortice.Dxc.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Dxc.Native/1.0.2": {"runtimeTargets": {"runtimes/linux-x64/native/libdxcompiler.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libdxil.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/dxcompiler.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.8.2407.7"}, "runtimes/win-arm64/native/dxil.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "101.8.2407.12"}, "runtimes/win-x64/native/dxcompiler.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.8.2407.7"}, "runtimes/win-x64/native/dxil.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "101.8.2407.12"}}}, "Vortice.DXGI/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.DXGI.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Mathematics/1.9.3": {"runtime": {"lib/net8.0/Vortice.Mathematics.dll": {"assemblyVersion": "1.9.3.0", "fileVersion": "1.9.3.0"}}}, "Vortice.XAudio2/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.XAudio2.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "EditeurAudioVideoCpp/1.0.0": {"runtime": {"EditeurAudioVideoCpp.dll": {"assemblyVersion": "1.0.9310.35450", "fileVersion": "0.0.0.0"}}}, "Utils/1.0.0": {"dependencies": {"Ceras": "4.1.7", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "9.0.4"}, "runtime": {"Utils.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"DirectX/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AssimpNet/5.0.0-beta1": {"type": "package", "serviceable": true, "sha512": "sha512-oiUmn2KNaLThdbr6oT461uyCwclsqfZ0UB36vqkAUm8ttBWjKOCBmtp9V+X6xDGZmQaJe9YknW5eoyATTikUNQ==", "path": "assimpnet/5.0.0-beta1", "hashPath": "assimpnet.5.0.0-beta1.nupkg.sha512"}, "Ceras/4.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-5hKNZAySyylTKELWfnLAFDG9A23Osp0Yqonpnmv2/MPqcVJjMC6/N/5EKJHYmMaw2xnLL8kiQaCe6Fu+tjNG/w==", "path": "ceras/4.1.7", "hashPath": "ceras.4.1.7.nupkg.sha512"}, "ComputeSharp/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaFaqJS2cp6pCmjXcqG4KxE8KAy+x7W9SLxF1UnvPr+V29nj0a+0dMp0qSVNu49ePhYNBvSv8ZzuHZr2ac23rA==", "path": "computesharp/3.2.0", "hashPath": "computesharp.3.2.0.nupkg.sha512"}, "ComputeSharp.Core/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ImIKT+x8IpWG+i/D8bIBtxeZ7FwlzcpKtoOgLzrwgJu0XopoVLsh3Xu6uzgZQIgtegFZK15PYqpgZKhMu2UTcg==", "path": "computesharp.core/3.2.0", "hashPath": "computesharp.core.3.2.0.nupkg.sha512"}, "ComputeSharp.D2D1/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-wvBSCHuJ6OXC5F1FsrlxAnUeOTyULR0+bidV+Elouzw9u7drPjfrfqu5jVvjiubaa4GjW1aPi28iD37vIfijmA==", "path": "computesharp.d2d1/3.2.0", "hashPath": "computesharp.d2d1.3.2.0.nupkg.sha512"}, "ComputeSharp.Dxc/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQ06P4B4crPQIJ/JBWxLPtUIAar34Y5sMXpjfPx5aVge4wto/4lL1tLS7tqqBFx4GhgQrloCsQO3XJVnIdpOrQ==", "path": "computesharp.dxc/3.2.0", "hashPath": "computesharp.dxc.3.2.0.nupkg.sha512"}, "ConcurrencyVisualizer/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3pCe8zt7mLtBiFJbyNA6XeU6jT10l683WkPipaNj1ktjEXur3HSF8/w3P8dyeImxz+EFgNHrEzY+58Xqj1M8cA==", "path": "concurrencyvisualizer/3.0.0", "hashPath": "concurrencyvisualizer.3.0.0.nupkg.sha512"}, "Cyotek.Drawing.BitmapFont/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t6vYF0sXgNb6dP9QNbJxJWw3RdCEWC6Llv47oPYpobYAhmKcgmAZJv6HXK03DEUmo7ff6yPvsyEWTkia+UAONw==", "path": "cyotek.drawing.bitmapfont/2.0.0", "hashPath": "cyotek.drawing.bitmapfont.2.0.0.nupkg.sha512"}, "HelixToolkit/2.27.0": {"type": "package", "serviceable": true, "sha512": "sha512-sXq9gXWIA/QYlx1Po9ac36hGwPT8BkpzSpGO7RgpGIuCOdJGXRASA9mJa6GJeTLfs5BYPzVwjKmyghw5ivM0bQ==", "path": "helixtoolkit/2.27.0", "hashPath": "helixtoolkit.2.27.0.nupkg.sha512"}, "HelixToolkit.SharpDX.Assimp/2.27.0": {"type": "package", "serviceable": true, "sha512": "sha512-Sybp+f2YxPnqA1SNsRaPhuGLK0DYAv1J7ZoHFJUABRt68v1+oT+GzP/MC6jjOAD84MmRv3hlHv43H+3x9w8/1A==", "path": "helixtoolkit.sharpdx.assimp/2.27.0", "hashPath": "helixtoolkit.sharpdx.assimp.2.27.0.nupkg.sha512"}, "HelixToolkit.SharpDX.Core/2.27.0": {"type": "package", "serviceable": true, "sha512": "sha512-IkivjYXuM2YusPrJDCQQf9qsZgPnD0FLO33wZsch9m1BmoRTDOoGTgs6W6gdRu1PpP39Uo7BUwsmLgOIusSXBA==", "path": "helixtoolkit.sharpdx.core/2.27.0", "hashPath": "helixtoolkit.sharpdx.core.2.27.0.nupkg.sha512"}, "HelixToolkit.SharpDX.Core.Wpf/2.27.0": {"type": "package", "serviceable": true, "sha512": "sha512-r6k8Jhw+gqCk4K57F25omnO8eQrIu6exMdiO5piZv6zXpUH9Yu4GZdEsymhe4rUE0k2wH0VN0aAOSA0fOytBIw==", "path": "helixtoolkit.sharpdx.core.wpf/2.27.0", "hashPath": "helixtoolkit.sharpdx.core.wpf.2.27.0.nupkg.sha512"}, "MediaFoundation.NetCore/2024.5.10": {"type": "package", "serviceable": true, "sha512": "sha512-q+tZLvg++T3ZAOEIW1IkNHjDqjI9B8nGyg3GOz6uYl1R4c5U3OdSw5/JndDqlQs94v5Tc6PuUIeTtOBV/lbPxw==", "path": "mediafoundation.netcore/2024.5.10", "hashPath": "mediafoundation.netcore.2024.5.10.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "path": "microsoft.netcore.targets/1.1.3", "hashPath": "microsoft.netcore.targets.1.1.3.nupkg.sha512"}, "Microsoft.VisualStudio.Threading/17.13.61": {"type": "package", "serviceable": true, "sha512": "sha512-R4iFsidiSB4P/zS+8BBz0fPFmgb64PQRQABuuIOjwgWgHBF0IHMcNz9wjG9CfN76BzVqgQU6/AJbM1NrJPE6zA==", "path": "microsoft.visualstudio.threading/17.13.61", "hashPath": "microsoft.visualstudio.threading.17.13.61.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Analyzers/17.13.61": {"type": "package", "serviceable": true, "sha512": "sha512-9ysRZR7xkVFsbwWjk0dx5tefySxhNMMwxmIr3G6UaFeugYE+TEghaTsmGiwT39maa2WFvH4zvjnEfDaCLq+Dtw==", "path": "microsoft.visualstudio.threading.analyzers/17.13.61", "hashPath": "microsoft.visualstudio.threading.analyzers.17.13.61.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"type": "package", "serviceable": true, "sha512": "sha512-vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "path": "microsoft.visualstudio.threading.only/17.13.61", "hashPath": "microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "path": "microsoft.visualstudio.validation/17.8.8", "hashPath": "microsoft.visualstudio.validation.17.8.8.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-kHgtAkXhNEP8oGuAVe3Q5admxsdMlSdWE2rXcA9FfeGDZJQawPccmZgnOswgW3ugUPSJt7VH+TMQPz65mnhGSQ==", "path": "microsoft.win32.systemevents/9.0.4", "hashPath": "microsoft.win32.systemevents.9.0.4.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "PrecisionTimer.NET/*******": {"type": "package", "serviceable": true, "sha512": "sha512-gFOgq5SfGgkmR7GVqv9YM7oWhMXQS5R3XO3oD0Rzp0hZmkpJkufuR4KowtgosrXOz56gD6fXA2SRc0Vr/h7O/w==", "path": "precisiontimer.net/*******", "hashPath": "precisiontimer.net.*******.nupkg.sha512"}, "Quamotion.TurboJpegWrapper/2.0.32": {"type": "package", "serviceable": true, "sha512": "sha512-tVRoNDchXwyHdHJv5tiB70I8OzJXb+heNseOsFc9cgBTDF/pbcD+Tsefu6PJPVUsMJ8NowKB0Vw5CRbni8t/zw==", "path": "quamotion.turbojpegwrapper/2.0.32", "hashPath": "quamotion.turbojpegwrapper.2.0.32.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SharpDX/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-3pv0LFMvfK/dv1qISJnn8xBeeT6R/FRvr0EV4KI2DGsL84Qlv6P7isWqxGyU0LCwlSVCJN3jgHJ4Bl0KI2PJww==", "path": "sharpdx/4.2.0", "hashPath": "sharpdx.4.2.0.nupkg.sha512"}, "SharpDX.D3DCompiler/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rnsd6Ilp127xbXqhTit8WKFQUrXwWxqVGpglyWDNkIBCk0tWXNQEjrJpsl0KAObzyZaa33+EXAikLVt5fnd3GA==", "path": "sharpdx.d3dcompiler/4.2.0", "hashPath": "sharpdx.d3dcompiler.4.2.0.nupkg.sha512"}, "SharpDX.Direct2D1/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qs8LzDMaQf1u3KB8ArHu9pDv6itZ++QXs99a/bVAG+nKr0Hx5NG4mcN5vsfE0mVR2TkeHfeUm4PksRah6VUPtA==", "path": "sharpdx.direct2d1/4.2.0", "hashPath": "sharpdx.direct2d1.4.2.0.nupkg.sha512"}, "SharpDX.Direct3D11/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oTm/iT5X/IIuJ8kNYP+DTC/MhBhqtRF5dbgPPFgLBdQv0BKzNTzXQQXd7SveBFjQg6hXEAJ2jGCAzNYvGFc9LA==", "path": "sharpdx.direct3d11/4.2.0", "hashPath": "sharpdx.direct3d11.4.2.0.nupkg.sha512"}, "SharpDX.Direct3D9/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1KgqXvPj/j5hLYxfcOQoJdHskamu7EWg60n9LBAYhun7dR+sKS0ZDNb7NuKwFP1bM0HZqvokjXQIf3oA2NpdqA==", "path": "sharpdx.direct3d9/4.2.0", "hashPath": "sharpdx.direct3d9.4.2.0.nupkg.sha512"}, "SharpDX.DXGI/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-UjKqkgWc8U+SP+j3LBzFP6OB6Ntapjih7Xo+g1rLcsGbIb5KwewBrBChaUu7sil8rWoeVU/k0EJd3SMN4VqNZw==", "path": "sharpdx.dxgi/4.2.0", "hashPath": "sharpdx.dxgi.4.2.0.nupkg.sha512"}, "SharpDX.Mathematics/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-R2pcKLgdsP9p5WyTjHmGOZ0ka0zASAZYc6P4L6rSvjYhf6klGYbent7MiVwbkwkt9dD44p5brjy5IwAnVONWGw==", "path": "sharpdx.mathematics/4.2.0", "hashPath": "sharpdx.mathematics.4.2.0.nupkg.sha512"}, "SharpGen.Runtime/2.2.0-beta": {"type": "package", "serviceable": true, "sha512": "sha512-pqf/lAf4jy1iWqkm37JmhoQhBMPVudI/F9qp2zVvzjWAPeSggRIuxGMVEZQ4UQiqtJ1Rf/+j3MVAONGYyCEDzQ==", "path": "sharpgen.runtime/2.2.0-beta", "hashPath": "sharpgen.runtime.2.2.0-beta.nupkg.sha512"}, "SharpGen.Runtime.COM/2.2.0-beta": {"type": "package", "serviceable": true, "sha512": "sha512-4vsXC8ohyVslcUDVBoVXLDkjKprqujh3GWy+DqqULjyZ3GCx7nwRAV5DdrXZxX70iEiKyI3TxW3Qhf/oOXeC1Q==", "path": "sharpgen.runtime.com/2.2.0-beta", "hashPath": "sharpgen.runtime.com.2.2.0-beta.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-tD6kosZnTAGdrEa0tZSuFyunMbt/5KYDnHdndJYGqZoNy00XVXyACd5d6KnE1YgYv3ne2CjtAfNXo/fwEhnKUA==", "path": "system.diagnostics.diagnosticsource/4.3.0", "hashPath": "system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SbtusMUT1bCxZ14904ZPo2GZyelze0rwUni9wXrp8KX9Zlsda8idqpxra1RBvOA85WM0wW+fCI4GLrlCTYiE6A==", "path": "system.drawing.common/9.0.4", "hashPath": "system.drawing.common.9.0.4.nupkg.sha512"}, "System.Drawing.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1QU/c35gwdhvj77fkScXQQbjiVAqIL3fEYn/19NE0CV/ic5TN5PyWAft8HsrbRd4SBLEoErNCkWSzMDc0MmbRw==", "path": "system.drawing.primitives/4.3.0", "hashPath": "system.drawing.primitives.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Net.Http/4.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "path": "system.net.http/4.3.4", "hashPath": "system.net.http.4.3.4.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "path": "system.private.datacontractserialization/4.3.0", "hashPath": "system.private.datacontractserialization.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "path": "system.runtime/4.3.1", "hashPath": "system.runtime.4.3.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "path": "system.runtime.serialization.xml/4.3.0", "hashPath": "system.runtime.serialization.xml.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "path": "system.text.regularexpressions/4.3.1", "hashPath": "system.text.regularexpressions.4.3.1.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "path": "system.threading.tasks.extensions/4.3.0", "hashPath": "system.threading.tasks.extensions.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "path": "system.xml.xmlserializer/4.3.0", "hashPath": "system.xml.xmlserializer.4.3.0.nupkg.sha512"}, "Validation/2.5.42": {"type": "package", "serviceable": true, "sha512": "sha512-lzd+CAUssDzD1A3R+iqqgzRkkNtLpNNsund1aUXZP/sKD2nxnex6nQ55C8uWKEQjNYKe8/LpBp0HLRNuBsxKQQ==", "path": "validation/2.5.42", "hashPath": "validation.2.5.42.nupkg.sha512"}, "Vanara.Core/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6WPBAY7JLL8Lev8BNMnnzhu22ANBaxr3yvres3Vcjz2Whmn4ND3v4o2ZwXte5MdmgBKjm6g6gh0yoeFAZxuzLQ==", "path": "vanara.core/4.1.2", "hashPath": "vanara.core.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Cryptography/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-hIxXBA/lEU5CsVKAnc80LZhNOsMX37yhnHRh6J9+4BYvIReDWpaqz5WoYwXF/KEYwMjldykAf65sgbQLL9HocA==", "path": "vanara.pinvoke.cryptography/4.1.2", "hashPath": "vanara.pinvoke.cryptography.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Gdi32/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-O4LCYVvOogGBx8JZCPcH8l97Iy8qgQjPyX+UJVEv193aoKHeuXYxOxfMZD41TD08nkYmTOK04KnWKs8uOUI/DQ==", "path": "vanara.pinvoke.gdi32/4.1.2", "hashPath": "vanara.pinvoke.gdi32.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Kernel32/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-anuovG4HCfRM449lr3p8xVfAOIHb4Spj4ThFsm/psyTGWYPlYa4/By61vzu41zhd8mnZ/ELzdCuj/VfZZmD5xQ==", "path": "vanara.pinvoke.kernel32/4.1.2", "hashPath": "vanara.pinvoke.kernel32.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Ole/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-w4m9+VzxnRqTuyXGpxo/RTNMr34ekM/B21a+J35xRoN326PSghx1L7H78fEzdSoNtK5Jx1NvFe71wc0Y0GsXgw==", "path": "vanara.pinvoke.ole/4.1.2", "hashPath": "vanara.pinvoke.ole.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Rpc/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-QHhj3bk5AfkZ/35SgJMvXIy3+CCwNHIV6zAQWIJH1UVHfHX2PSF8mONHTmQ/ixaJam933lykFw9cDPCQd9txZQ==", "path": "vanara.pinvoke.rpc/4.1.2", "hashPath": "vanara.pinvoke.rpc.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Security/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-dF8RT6hbCbkDQGTWB9114m9UvfzPZhH6kmwgxYbzaH7vEjkAlolHddqFrc9HTHlFTahbRcp50Iw3noEE6pPSmw==", "path": "vanara.pinvoke.security/4.1.2", "hashPath": "vanara.pinvoke.security.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.Shared/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Q8RWA+mR8XOnFTj/a+HKgmMbw5y8+awy3QjqA2y8EhlIrposRE3jrR51a7f7uJpdhVAVJEKuW4gVXiq+U5v2Jg==", "path": "vanara.pinvoke.shared/4.1.2", "hashPath": "vanara.pinvoke.shared.4.1.2.nupkg.sha512"}, "Vanara.PInvoke.User32/4.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-T46a4s74TnHVFfDndtgrBU2R62fo3+3YoUDmEjVgIlLixCBMyFWK7cCl+kMl0ekn0Jye/KjQbFgM1wXqjxPWvQ==", "path": "vanara.pinvoke.user32/4.1.2", "hashPath": "vanara.pinvoke.user32.4.1.2.nupkg.sha512"}, "Vortice.D3DCompiler/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-h5PSVwQpgUs5+4QP5bJEuVyEUd0liz7pVcSayZ5JRlT8veGiaosK3EdFwGqZ+vnj+dkjlyS7+sA+18w+hB9fcA==", "path": "vortice.d3dcompiler/3.6.2", "hashPath": "vortice.d3dcompiler.3.6.2.nupkg.sha512"}, "Vortice.Direct2D1/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-axuDhGNPkdLwSzlcUAWr/Ju6s2tvnVKxM4MSl9zYcPePiUOTsKF39oByBZSjXllkQY01WG2o4+p00TZz/dL57Q==", "path": "vortice.direct2d1/3.6.2", "hashPath": "vortice.direct2d1.3.6.2.nupkg.sha512"}, "Vortice.Direct3D11/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-+tXkwV4YVFgox3NjLGBOPmEHK8Jd/8ElI3X8aX7xq4JhsC3J9BqpUhpI+4LOKeCGN+XCORhyFq0ow+pB44YQTg==", "path": "vortice.direct3d11/3.6.2", "hashPath": "vortice.direct3d11.3.6.2.nupkg.sha512"}, "Vortice.Direct3D12/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-fr9VpWkTfH1Jio5jvP0WBGcoZo+2hy2DUVHTYQMLL95KsCTc9PsG0rdtToolaQvorKFbF6hY2npZRiTy5wnxMA==", "path": "vortice.direct3d12/3.6.2", "hashPath": "vortice.direct3d12.3.6.2.nupkg.sha512"}, "Vortice.DirectML/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-VKKEW2V4lbkVPcaV2xnuaHLmvIrHToshi89kNkA0htSc46suADMU2Jo6SFlgqzM/byWgRGmoalZDrS6hcMOHkQ==", "path": "vortice.directml/3.6.2", "hashPath": "vortice.directml.3.6.2.nupkg.sha512"}, "Vortice.DirectX/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-pa/97U5IHascS/UJzMWFSKIs3wxh36zc8JpI+fzi/JLQk4wfTZur0n/Y9Lcp8i7hsf613Vu/6n6IImWSc9wupw==", "path": "vortice.directx/3.6.2", "hashPath": "vortice.directx.3.6.2.nupkg.sha512"}, "Vortice.Dxc/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-266EVwYCj1FU9eDRRnq05Ovu/32/V3ipbStYkmnS0rvVMUPio0okJ9DhfswVoFPJi1OLjAoP5Y9icQ9vmy3nzQ==", "path": "vortice.dxc/3.6.2", "hashPath": "vortice.dxc.3.6.2.nupkg.sha512"}, "Vortice.Dxc.Native/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-8deBQXEgEhbHvo6eQJyzHVKNja+yndSug6oFkf83aaBRr4GqbrlutgwyVsoUEDGCamvc6rEPDagkGeySuf2XfA==", "path": "vortice.dxc.native/1.0.2", "hashPath": "vortice.dxc.native.1.0.2.nupkg.sha512"}, "Vortice.DXGI/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPnBb1x3+CEqq7L5SEejElD2tMjkdE+bqN4c/eSNHj9WEpRPvAO0QFSEN8d9LiRDt9i9geD8m7E4s8sLMgz2qQ==", "path": "vortice.dxgi/3.6.2", "hashPath": "vortice.dxgi.3.6.2.nupkg.sha512"}, "Vortice.Mathematics/1.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-VzXWjzM4F5pK3gP3m0wv9LqhRrcmQebf+lK/gIPLZ+TPXTptMal74iGkQbivIJm9OwdMZdGOuk1dvwc2iJHUGQ==", "path": "vortice.mathematics/1.9.3", "hashPath": "vortice.mathematics.1.9.3.nupkg.sha512"}, "Vortice.XAudio2/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-i0ZNEolel3NdvgpSRvpBuJSiwAaMqqyRdvBK2GSv4r8wQuWDyBC7qwgCj2d1ae7d6Xytzqb2RNbEX3DTe/acJg==", "path": "vortice.xaudio2/3.6.2", "hashPath": "vortice.xaudio2.3.6.2.nupkg.sha512"}, "EditeurAudioVideoCpp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Utils/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
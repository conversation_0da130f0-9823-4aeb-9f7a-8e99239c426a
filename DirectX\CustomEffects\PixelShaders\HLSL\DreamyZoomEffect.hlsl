#include "Header1.hlsli"
cbuffer constants : register(b0)
{
    CONSTANTBUFFER_STANDARD_FIELDS
    
    float rotation;
    float scale;
}

#define COMPLEXINPUTS
#include "Header2.hlsli"

#define DEG2RAD 0.03926990816987241548078304229099 // 1/180*PI

float4 effet(float2 uv)
{
  // Massage parameters
    float phase = progress < 0.5 ? progress * 2.0 : (progress - 0.5) * 2.0;
    float angleOffset = progress < 0.5 ? mix(0.0, rotation * DEG2RAD, phase) : mix(-rotation * DEG2RAD, 0.0, phase);
    float newScale = progress < 0.5 ? mix(1.0, scale, phase) : mix(scale, 1.0, phase);
  
    float2 center = float2(0, 0);

  // Calculate the source point
    float2 assumedCenter = float2(0.5, 0.5);
    float2 p = (uv.xy - float2(0.5, 0.5)) / newScale * float2(ratio, 1.0);

  // This can probably be optimized (with distance())
    float angle = atan2(p.y, p.x) + angleOffset;
    float dist = distance(center, p);
    p.x = cos(angle) * dist / ratio + 0.5;
    p.y = sin(angle) * dist + 0.5;
    float4 c = progress < 0.5 ? getFromColor(p) : getToColor(p);

  // Finally, apply the color
    return c + (progress < 0.5 ? mix(0.0, 1.0, phase) : mix(1.0, 0.0, phase));
}

endCode

{"version": 3, "targets": {"net8.0": {"Ceras/4.1.7": {"type": "package", "dependencies": {"System.Buffers": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/Ceras.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Ceras.dll": {"related": ".pdb;.xml"}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.VisualStudio.Threading/17.10.48": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Threading.Analyzers": "17.10.48", "Microsoft.VisualStudio.Validation": "17.8.8"}, "compile": {"lib/net6.0/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Threading.Analyzers/17.10.48": {"type": "package", "build": {"build/Microsoft.VisualStudio.Threading.Analyzers.targets": {}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "compile": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "PrecisionTimer.NET/*******": {"type": "package", "compile": {"lib/netstandard2.0/PrecisionTimer.NET.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/PrecisionTimer.NET.dll": {"related": ".xml"}}}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "Vanara.Core/4.0.3": {"type": "package", "compile": {"lib/net7.0/Vanara.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Vanara.Core.dll": {"related": ".xml"}}, "resource": {"lib/net7.0/fr/Vanara.Core.resources.dll": {"locale": "fr"}}}, "Vanara.PInvoke.Gdi32/4.0.3": {"type": "package", "dependencies": {"Vanara.Core": "4.0.3", "Vanara.PInvoke.Shared": "4.0.3"}, "compile": {"lib/net7.0/Vanara.PInvoke.Gdi32.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Vanara.PInvoke.Gdi32.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Kernel32/4.0.3": {"type": "package", "dependencies": {"Vanara.Core": "4.0.3", "Vanara.PInvoke.Shared": "4.0.3"}, "compile": {"lib/net7.0/Vanara.PInvoke.Kernel32.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Vanara.PInvoke.Kernel32.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Shared/4.0.3": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "Vanara.Core": "4.0.3"}, "compile": {"lib/net7.0/Vanara.PInvoke.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Vanara.PInvoke.Shared.dll": {"related": ".xml"}}}, "Vanara.PInvoke.User32/4.0.3": {"type": "package", "dependencies": {"Vanara.Core": "4.0.3", "Vanara.PInvoke.Gdi32": "4.0.3", "Vanara.PInvoke.Kernel32": "4.0.3", "Vanara.PInvoke.Shared": "4.0.3"}, "compile": {"lib/net7.0/Vanara.PInvoke.User32.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Vanara.PInvoke.User32.dll": {"related": ".xml"}}}}}, "libraries": {"Ceras/4.1.7": {"sha512": "5hKNZAySyylTKELWfnLAFDG9A23Osp0Yqonpnmv2/MPqcVJjMC6/N/5EKJHYmMaw2xnLL8kiQaCe6Fu+tjNG/w==", "type": "package", "path": "ceras/4.1.7", "files": [".nupkg.metadata", ".signature.p7s", "ceras.4.1.7.nupkg.sha512", "ceras.nuspec", "lib/net45/Ceras.dll", "lib/net45/Ceras.pdb", "lib/net45/Ceras.xml", "lib/net47/Ceras.dll", "lib/net47/Ceras.pdb", "lib/net47/Ceras.xml", "lib/netstandard2.0/Ceras.dll", "lib/netstandard2.0/Ceras.pdb", "lib/netstandard2.0/Ceras.xml"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.VisualStudio.Threading/17.10.48": {"sha512": "7onkbbE0AOAhxKe+ZAa2NMzo4R5G4qypZmNIE0GhBohT/tl6e5aLnLx4Gg6trf6SUn3DfLRowMtNe5Q+PmhKgQ==", "type": "package", "path": "microsoft.visualstudio.threading/17.10.48", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "lib/net472/Microsoft.VisualStudio.Threading.dll", "lib/net472/Microsoft.VisualStudio.Threading.xml", "lib/net472/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/manifest.spdx.json", "lib/net472/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/Microsoft.VisualStudio.Threading.dll", "lib/net6.0-windows7.0/Microsoft.VisualStudio.Threading.xml", "lib/net6.0-windows7.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/manifest.spdx.json", "lib/net6.0-windows7.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0-windows7.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/Microsoft.VisualStudio.Threading.dll", "lib/net6.0/Microsoft.VisualStudio.Threading.xml", "lib/net6.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/manifest.spdx.json", "lib/net6.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/manifest.spdx.json", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "microsoft.visualstudio.threading.17.10.48.nupkg.sha512", "microsoft.visualstudio.threading.nuspec"]}, "Microsoft.VisualStudio.Threading.Analyzers/17.10.48": {"sha512": "xwvwT91oqFjLgQykUp6y/JPYxz8LchbfJKrLVatfczWddXKng8DAo8RiiIodt+pRdsVXP9Ud02GtJoY7ifdXPQ==", "type": "package", "path": "microsoft.visualstudio.threading.analyzers/17.10.48", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.CSharp.dll", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.dll", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.dll", "analyzers/cs/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/de/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/es/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/fr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/it/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ja/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ko/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/pl/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/pt-BR/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ru/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/tr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/zh-<PERSON>/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/zh-Hant/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.VisualBasic.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.dll", "analyzers/vb/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/de/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/es/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/fr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/it/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ja/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ko/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/pl/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/pt-BR/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ru/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/tr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/zh-<PERSON>/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/zh-Hant/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "build/AdditionalFiles/vs-threading.LegacyThreadSwitchingMembers.txt", "build/AdditionalFiles/vs-threading.MainThreadAssertingMethods.txt", "build/AdditionalFiles/vs-threading.MainThreadSwitchingMethods.txt", "build/AdditionalFiles/vs-threading.MembersRequiringMainThread.txt", "build/Microsoft.VisualStudio.Threading.Analyzers.targets", "microsoft.visualstudio.threading.analyzers.17.10.48.nupkg.sha512", "microsoft.visualstudio.threading.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.VisualStudio.Validation/17.8.8": {"sha512": "rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "type": "package", "path": "microsoft.visualstudio.validation/17.8.8", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "lib/net6.0/Microsoft.VisualStudio.Validation.dll", "lib/net6.0/Microsoft.VisualStudio.Validation.xml", "lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "microsoft.visualstudio.validation.nuspec"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "PrecisionTimer.NET/*******": {"sha512": "gFOgq5SfGgkmR7GVqv9YM7oWhMXQS5R3XO3oD0Rzp0hZmkpJkufuR4KowtgosrXOz56gD6fXA2SRc0Vr/h7O/w==", "type": "package", "path": "precisiontimer.net/*******", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/PrecisionTimer.NET.dll", "lib/netstandard2.0/PrecisionTimer.NET.xml", "package.png", "precisiontimer.net.*******.nupkg.sha512", "precisiontimer.net.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"sha512": "wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Vanara.Core/4.0.3": {"sha512": "yPCYBz5qlT0TcwzJt/mmjGlYLJg4KXKUpWlt7dQt46QOMPGXyMj3GBcY0R1oyC9lq5iCR8xdmKx3HfKt7n13Nw==", "type": "package", "path": "vanara.core/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net45/Vanara.Core.dll", "lib/net45/Vanara.Core.xml", "lib/net45/fr/Vanara.Core.resources.dll", "lib/net48/Vanara.Core.dll", "lib/net48/Vanara.Core.xml", "lib/net48/fr/Vanara.Core.resources.dll", "lib/net5.0/Vanara.Core.dll", "lib/net5.0/Vanara.Core.xml", "lib/net5.0/fr/Vanara.Core.resources.dll", "lib/net6.0/Vanara.Core.dll", "lib/net6.0/Vanara.Core.xml", "lib/net6.0/fr/Vanara.Core.resources.dll", "lib/net7.0/Vanara.Core.dll", "lib/net7.0/Vanara.Core.xml", "lib/net7.0/fr/Vanara.Core.resources.dll", "lib/net8.0-windows7.0/Vanara.Core.dll", "lib/net8.0-windows7.0/Vanara.Core.xml", "lib/net8.0-windows7.0/fr/Vanara.Core.resources.dll", "lib/netcoreapp3.1/Vanara.Core.dll", "lib/netcoreapp3.1/Vanara.Core.xml", "lib/netcoreapp3.1/fr/Vanara.Core.resources.dll", "lib/netstandard2.0/Vanara.Core.dll", "lib/netstandard2.0/Vanara.Core.xml", "lib/netstandard2.0/fr/Vanara.Core.resources.dll", "pkgreadme.md", "vanara.core.4.0.3.nupkg.sha512", "vanara.core.nuspec"]}, "Vanara.PInvoke.Gdi32/4.0.3": {"sha512": "t5nCJn+KymOTMgI9F0lr38lcmTPK2uJkt0UqxvcO7L/zm+z+hP4xo6XDWzraQX8ANcPy+yOmkjpvQyzZBx5WOQ==", "type": "package", "path": "vanara.pinvoke.gdi32/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net45/Vanara.PInvoke.Gdi32.dll", "lib/net45/Vanara.PInvoke.Gdi32.xml", "lib/net48/Vanara.PInvoke.Gdi32.dll", "lib/net48/Vanara.PInvoke.Gdi32.xml", "lib/net5.0/Vanara.PInvoke.Gdi32.dll", "lib/net5.0/Vanara.PInvoke.Gdi32.xml", "lib/net6.0/Vanara.PInvoke.Gdi32.dll", "lib/net6.0/Vanara.PInvoke.Gdi32.xml", "lib/net7.0/Vanara.PInvoke.Gdi32.dll", "lib/net7.0/Vanara.PInvoke.Gdi32.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Gdi32.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Gdi32.xml", "lib/netstandard2.0/Vanara.PInvoke.Gdi32.dll", "lib/netstandard2.0/Vanara.PInvoke.Gdi32.xml", "pkgreadme.md", "vanara.pinvoke.gdi32.4.0.3.nupkg.sha512", "vanara.pinvoke.gdi32.nuspec"]}, "Vanara.PInvoke.Kernel32/4.0.3": {"sha512": "W3v4EnfsfLIGHiZ0yfppA12rkKal7K6KFlsxsKKCPGb8R2ivh4IsgIUBaGtssdPCaDfqwRZugExBBUgI/uABsw==", "type": "package", "path": "vanara.pinvoke.kernel32/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net45/Vanara.PInvoke.Kernel32.dll", "lib/net45/Vanara.PInvoke.Kernel32.xml", "lib/net48/Vanara.PInvoke.Kernel32.dll", "lib/net48/Vanara.PInvoke.Kernel32.xml", "lib/net5.0/Vanara.PInvoke.Kernel32.dll", "lib/net5.0/Vanara.PInvoke.Kernel32.xml", "lib/net6.0/Vanara.PInvoke.Kernel32.dll", "lib/net6.0/Vanara.PInvoke.Kernel32.xml", "lib/net7.0/Vanara.PInvoke.Kernel32.dll", "lib/net7.0/Vanara.PInvoke.Kernel32.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Kernel32.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Kernel32.xml", "lib/netstandard2.0/Vanara.PInvoke.Kernel32.dll", "lib/netstandard2.0/Vanara.PInvoke.Kernel32.xml", "pkgreadme.md", "vanara.pinvoke.kernel32.4.0.3.nupkg.sha512", "vanara.pinvoke.kernel32.nuspec"]}, "Vanara.PInvoke.Shared/4.0.3": {"sha512": "2rm9grMSC+H0jnpldL9Btcm2T/QwOfQGYgms3+K45CnIqT7h4oC7sSBReYpO/X2Oo5GLxBXn+QW7bE991hdnkw==", "type": "package", "path": "vanara.pinvoke.shared/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net45/Vanara.PInvoke.Shared.dll", "lib/net45/Vanara.PInvoke.Shared.xml", "lib/net48/Vanara.PInvoke.Shared.dll", "lib/net48/Vanara.PInvoke.Shared.xml", "lib/net5.0/Vanara.PInvoke.Shared.dll", "lib/net5.0/Vanara.PInvoke.Shared.xml", "lib/net6.0/Vanara.PInvoke.Shared.dll", "lib/net6.0/Vanara.PInvoke.Shared.xml", "lib/net7.0/Vanara.PInvoke.Shared.dll", "lib/net7.0/Vanara.PInvoke.Shared.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Shared.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Shared.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Shared.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Shared.xml", "lib/netstandard2.0/Vanara.PInvoke.Shared.dll", "lib/netstandard2.0/Vanara.PInvoke.Shared.xml", "pkgreadme.md", "vanara.pinvoke.shared.4.0.3.nupkg.sha512", "vanara.pinvoke.shared.nuspec"]}, "Vanara.PInvoke.User32/4.0.3": {"sha512": "ESMwjZ5Ym/reI9z5W1ntgElsyEORGp53OKyN/yVMj1IRv4npaxdzRX442hbLsCocWyO3OAe2la1I1YJrW67Fhg==", "type": "package", "path": "vanara.pinvoke.user32/4.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net45/Vanara.PInvoke.User32.dll", "lib/net45/Vanara.PInvoke.User32.xml", "lib/net48/Vanara.PInvoke.User32.dll", "lib/net48/Vanara.PInvoke.User32.xml", "lib/net5.0/Vanara.PInvoke.User32.dll", "lib/net5.0/Vanara.PInvoke.User32.xml", "lib/net6.0/Vanara.PInvoke.User32.dll", "lib/net6.0/Vanara.PInvoke.User32.xml", "lib/net7.0/Vanara.PInvoke.User32.dll", "lib/net7.0/Vanara.PInvoke.User32.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.User32.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.User32.xml", "lib/netcoreapp3.1/Vanara.PInvoke.User32.dll", "lib/netcoreapp3.1/Vanara.PInvoke.User32.xml", "lib/netstandard2.0/Vanara.PInvoke.User32.dll", "lib/netstandard2.0/Vanara.PInvoke.User32.xml", "pkgreadme.md", "vanara.pinvoke.user32.4.0.3.nupkg.sha512", "vanara.pinvoke.user32.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["Ceras >= 4.1.7", "Microsoft.VisualStudio.Threading >= 17.10.48", "PrecisionTimer.NET >= *******", "Vanara.PInvoke.Kernel32 >= 4.0.3", "Vanara.PInvoke.User32 >= 4.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj", "projectName": "AndroidWebCamClient", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Ceras": {"target": "Package", "version": "[4.1.7, )"}, "Microsoft.VisualStudio.Threading": {"target": "Package", "version": "[17.10.48, )"}, "PrecisionTimer.NET": {"target": "Package", "version": "[*******, )"}, "Vanara.PInvoke.Kernel32": {"target": "Package", "version": "[4.0.3, )"}, "Vanara.PInvoke.User32": {"target": "Package", "version": "[4.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.304/PortableRuntimeIdentifierGraph.json"}}}}
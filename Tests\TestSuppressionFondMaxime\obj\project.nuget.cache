{"version": 2, "dgSpecHash": "Ms+yGGdteE0=", "success": false, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "expectedPackageFiles": ["F:\\NugetPackages\\assimpnet\\5.0.0-beta1\\assimpnet.5.0.0-beta1.nupkg.sha512", "F:\\NugetPackages\\avalonedit\\6.3.1.120\\avalonedit.6.3.1.120.nupkg.sha512", "F:\\NugetPackages\\ceras\\4.1.7\\ceras.4.1.7.nupkg.sha512", "F:\\NugetPackages\\computesharp.core\\3.2.0\\computesharp.core.3.2.0.nupkg.sha512", "F:\\NugetPackages\\computesharp.d2d1\\3.2.0\\computesharp.d2d1.3.2.0.nupkg.sha512", "F:\\NugetPackages\\computesharp.dxc\\3.2.0\\computesharp.dxc.3.2.0.nupkg.sha512", "F:\\NugetPackages\\concurrencyvisualizer\\3.0.0\\concurrencyvisualizer.3.0.0.nupkg.sha512", "F:\\NugetPackages\\cyotek.drawing.bitmapfont\\2.0.0\\cyotek.drawing.bitmapfont.2.0.0.nupkg.sha512", "F:\\NugetPackages\\excss\\4.3.0\\excss.4.3.0.nupkg.sha512", "F:\\NugetPackages\\fizzler\\1.3.1\\fizzler.1.3.1.nupkg.sha512", "F:\\NugetPackages\\helixtoolkit\\2.26.0\\helixtoolkit.2.26.0.nupkg.sha512", "F:\\NugetPackages\\helixtoolkit.sharpdx.assimp\\2.27.0\\helixtoolkit.sharpdx.assimp.2.27.0.nupkg.sha512", "F:\\NugetPackages\\helixtoolkit.sharpdx.core.wpf\\2.26.0\\helixtoolkit.sharpdx.core.wpf.2.26.0.nupkg.sha512", "F:\\NugetPackages\\mediafoundation.netcore\\2024.5.10\\mediafoundation.netcore.2024.5.10.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.logging.abstractions\\6.0.0\\microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.threading\\17.13.61\\microsoft.visualstudio.threading.17.13.61.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.threading.analyzers\\17.13.61\\microsoft.visualstudio.threading.analyzers.17.13.61.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.threading.only\\17.13.61\\microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.validation\\17.8.8\\microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.systemevents\\9.0.4\\microsoft.win32.systemevents.9.0.4.nupkg.sha512", "F:\\NugetPackages\\modernwpfui\\0.9.6\\modernwpfui.0.9.6.nupkg.sha512", "F:\\NugetPackages\\netoctree\\2.1.0\\netoctree.2.1.0.nupkg.sha512", "F:\\NugetPackages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "F:\\NugetPackages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "F:\\NugetPackages\\precisiontimer.net\\2.4.0.4\\precisiontimer.net.2.4.0.4.nupkg.sha512", "F:\\NugetPackages\\propertytools\\3.1.0\\propertytools.3.1.0.nupkg.sha512", "F:\\NugetPackages\\propertytools.wpf\\3.1.0\\propertytools.wpf.3.1.0.nupkg.sha512", "F:\\NugetPackages\\quamotion.turbojpegwrapper\\2.0.32\\quamotion.turbojpegwrapper.2.0.32.nupkg.sha512", "F:\\NugetPackages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\sharpdx\\4.2.0\\sharpdx.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.d3dcompiler\\4.2.0\\sharpdx.d3dcompiler.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.direct2d1\\4.2.0\\sharpdx.direct2d1.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.direct3d11\\4.2.0\\sharpdx.direct3d11.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.direct3d9\\4.2.0\\sharpdx.direct3d9.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.dxgi\\4.2.0\\sharpdx.dxgi.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.mathematics\\4.2.0\\sharpdx.mathematics.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime\\2.2.0-beta\\sharpgen.runtime.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime.com\\2.2.0-beta\\sharpgen.runtime.com.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\supercluster.kdtree.standard\\1.0.5\\supercluster.kdtree.standard.1.0.5.nupkg.sha512", "F:\\NugetPackages\\svg\\3.4.7\\svg.3.4.7.nupkg.sha512", "F:\\NugetPackages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "F:\\NugetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel.annotations\\4.6.0\\system.componentmodel.annotations.4.6.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.diagnosticsource\\4.3.0\\system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.drawing.common\\9.0.4\\system.drawing.common.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.drawing.primitives\\4.3.0\\system.drawing.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "F:\\NugetPackages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.typeextensions\\4.7.0\\system.reflection.typeextensions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "F:\\NugetPackages\\system.runtime.compilerservices.unsafe\\4.5.2\\system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "F:\\NugetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.serialization.xml\\4.3.0\\system.runtime.serialization.xml.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "F:\\NugetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks.parallel\\4.3.0\\system.threading.tasks.parallel.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "F:\\NugetPackages\\validation\\2.5.42\\validation.2.5.42.nupkg.sha512", "F:\\NugetPackages\\vanara.core\\4.1.2\\vanara.core.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.cryptography\\4.1.2\\vanara.pinvoke.cryptography.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.gdi32\\4.1.2\\vanara.pinvoke.gdi32.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.kernel32\\4.1.2\\vanara.pinvoke.kernel32.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.ole\\4.1.2\\vanara.pinvoke.ole.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.rpc\\4.1.2\\vanara.pinvoke.rpc.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.security\\4.1.2\\vanara.pinvoke.security.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.shared\\4.1.2\\vanara.pinvoke.shared.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.user32\\4.1.2\\vanara.pinvoke.user32.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vortice.d3dcompiler\\3.6.2\\vortice.d3dcompiler.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct2d1\\3.6.2\\vortice.direct2d1.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d11\\3.6.2\\vortice.direct3d11.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d12\\3.6.2\\vortice.direct3d12.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directml\\3.6.2\\vortice.directml.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directx\\3.6.2\\vortice.directx.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc\\3.6.2\\vortice.dxc.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc.native\\1.0.2\\vortice.dxc.native.1.0.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxgi\\3.6.2\\vortice.dxgi.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.mathematics\\1.9.3\\vortice.mathematics.1.9.3.nupkg.sha512", "F:\\NugetPackages\\vortice.xaudio2\\3.6.2\\vortice.xaudio2.3.6.2.nupkg.sha512"], "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.Core.Wpf' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.core.wpf/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.core.wpf'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E7A7-F8B5-46A7-87CA-00ED1FF5560D)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "HelixToolkit.Core.Wpf", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BC2-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "HelixToolkit", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'GeometRi' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/geometri/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'geometri'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 2A11D4EA-5B55-4083-AFA8-EF67C67AB17B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "GeometRi", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core.Wpf' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core.wpf/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core.wpf'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10F92-DD9F-47F3-95B1-0A83CD8EB88B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "HelixToolkit.SharpDX.Core.Wpf", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F2693F-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "ComputeSharp", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CCBA5-1DD4-486C-B451-DD20F799B176)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "HelixToolkit.SharpDX.Core", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CCBA5-1DD4-486C-B451-DD20F799B176)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "HelixToolkit.SharpDX.Core", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'SharpGen.Runtime' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/sharpgen.runtime/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'sharpgen.runtime'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 2A11D32D-5B55-4083-AFA8-EF67C67AB17B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "SharpGen.Runtime", "targetGraphs": []}, {"code": "NU1107", "level": "Error", "message": "Conflit de version détecté pour HelixToolkit.SharpDX.Core. Installez/référencez HelixToolkit.SharpDX.Core 2.27.0 directement au projet TestSuppressionFondMaxime pour résoudre ce problème. \r\n TestSuppressionFondMaxime -> DirectX -> HelixToolkit.SharpDX.Core (>= 2.27.0) \r\n TestSuppressionFondMaxime -> HelixToolkit.SharpDX.Core.Wpf 2.26.0 -> HelixToolkit.SharpDX.Core (= 2.26.0).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "libraryId": "HelixToolkit.SharpDX.Core", "targetGraphs": ["net8.0-windows7.0"]}]}
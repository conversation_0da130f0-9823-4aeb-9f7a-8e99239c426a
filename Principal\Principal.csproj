﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>disable</Nullable>
        <UseWPF>true</UseWPF>
        <Platform>windows</Platform>
        <PlatformTarget>x64</PlatformTarget>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <ImplicitUsings>disable</ImplicitUsings>
        <ApplicationManifest>app.manifest</ApplicationManifest>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DefineConstants>$(DefineConstants)TRACE</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DefineConstants>$(DefineConstants)TRACE</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Tests\**" />
        <EmbeddedResource Remove="Tests\**" />
        <None Remove="Tests\**" />
        <Page Remove="Tests\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="C:\Users\<USER>\.nuget\packages\vortice.directml\3.6.2\build\..\runtimes\win-x64\DirectML.Debug.dll" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Ceras" Version="4.1.7" />
        <PackageReference Include="DirectShowLib.Standard" Version="2.1.0" />
        <PackageReference Include="Dirkster.AvalonDock" Version="4.72.1" />
        <PackageReference Include="Dirkster.AvalonDock.Themes.VS2013" Version="4.72.1" />
        <PackageReference Include="DotNext.Unsafe" Version="5.21.0" />
        <PackageReference Include="FasterKv.Cache.Core" Version="1.0.2" />
        <PackageReference Include="Fluent.Ribbon" Version="11.0.0" />
        <PackageReference Include="HelixToolkit" Version="2.27.0" />
        <PackageReference Include="HelixToolkit.SharpDX.Assimp" Version="2.27.0" />
        <PackageReference Include="HelixToolkit.SharpDX.Core" Version="2.27.0" />
        <PackageReference Include="HelixToolkit.SharpDX.Core.Wpf" Version="2.27.0" />
        <PackageReference Include="HG.NWaves" Version="0.9.6" />
        <PackageReference Include="HoloTrack.DirectShow" Version="1.0.0" />
        <PackageReference Include="JetBrains.Annotations" Version="2024.3.0" />
        <PackageReference Include="MediaFoundation.NetCore" Version="2024.5.10" />
        <PackageReference Include="Microsoft-WindowsAPICodePack-Shell" Version="1.1.5" />
        <PackageReference Include="Microsoft.ML.OnnxRuntime.DirectML" Version="1.21.1" />
        <PackageReference Include="Microsoft.VisualStudio.Threading" Version="17.13.61" />
        <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="17.13.61">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="ModernWpfUI" Version="0.9.6" />
        <PackageReference Include="NAudio" Version="2.2.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Nito.AsyncEx.Context" Version="5.1.2" />
        <PackageReference Include="ShapeCrawler" Version="0.66.0" />
        <PackageReference Include="SharpDX" Version="4.2.0" />
        <PackageReference Include="Spire.Presentation" Version="10.4.2" />
        <PackageReference Include="Svg" Version="3.4.7" />
        <PackageReference Include="System.Formats.Asn1" Version="9.0.4" />
        <PackageReference Include="System.Management" Version="9.0.4" />
        <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.1.2" />
        <PackageReference Include="System.Security.Cryptography.Pkcs" Version="9.0.4" />
        <PackageReference Include="System.Security.Cryptography.Xml" Version="9.0.4" />
        <PackageReference Include="Vanara.PInvoke.User32" Version="4.1.2" />
        <PackageReference Include="WpfScreenHelper" Version="2.1.1" />
        <ProjectReference Include="..\AlphaCodec\AlphaCodec.csproj" />
        <ProjectReference Include="..\DirectX\DirectX.csproj" />
        <ProjectReference Include="..\SharpSvg\SharpSvg.csproj" />
        <ProjectReference Include="..\UserControls\UserControls.csproj" />
        <ProjectReference Include="..\Utils\Utils.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="ffmpeg\ffmpeg.exe">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
        <Resource Include="Images\Icons32\Aide.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Ajouter.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Annuler.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\ApplicationMenu.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Arrêter.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Audio.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Capturer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\ChargerPowerPoint.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Clique.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Coller.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Configuration.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Copier.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Couper.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Diapositive.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Développement.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Effacer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Encoder.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Enregistrer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\FermerProjet.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Formes.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Image.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Importer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Information.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Jouer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\KeyA.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\KeyAdd.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\KeyDelete.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\KeyRight.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\KeySubtract.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Left.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Manuel.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Média.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Nettoyer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\NouveauProjet.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Ouvrir.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Pause.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Phone.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\PropriétésProjet.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Quitter.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Recharger.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\RechargerDiapositive.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Redo.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\RéinitialiserFenêtres.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Sauvegarder.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Séquence.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Star.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Undo.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Up.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\VidéoPrincipale.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\ÀPropos.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons32\Éditer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Aide.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Ajouter.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Annuler.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\ApplicationMenu.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Arrêter.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Audio.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Capturer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\ChargerPowerPoint.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Clique.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Coller.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Configuration.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Copier.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Couper.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Diapositive.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Down.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Développement.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Effacer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Encoder.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Enregistrer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\FermerProjet.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Formes.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Image.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Importer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Information.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Jouer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\KeyA.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\KeyAdd.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\KeyDelete.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\KeyRight.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\KeySubtract.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Left.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Manuel.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Média.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Nettoyer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\NouveauProjet.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Ouvrir.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Pause.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Phone.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\PropriétésProjet.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Quitter.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Recharger.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\RechargerDiapositive.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Redo.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\RéinitialiserFenêtres.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Sauvegarder.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Séquence.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Star.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Undo.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Up.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\ÀPropos.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\Icons64\Éditer.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Images\IconsOriginaux\Formes.png" />
        <Resource Include="Svg\SvgEditor\Images\Icons32\Beautify.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons32\Copy to drawing.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons32\Import.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons32\Paste from drawing.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons32\Redo.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons32\Undo.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons64\Beautify.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons64\Copy to drawing.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons64\Import.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons64\Paste from drawing.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons64\Redo.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
        <Resource Include="Svg\SvgEditor\Images\Icons64\Undo.png">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Resource>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Properties\Settings.Designer.cs">
            <DesignTimeSharedInput>True</DesignTimeSharedInput>
            <AutoGen>True</AutoGen>
            <DependentUpon>Settings.settings</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <None Update="AudioVideo\SuppressionFond\Models\MODNET.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVideo\SuppressionFond\Models\sghm_optimized.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVidéo\SuppressionFond\Models\Modnet1.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVideo\SuppressionFond\Models\Modnet2.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVidéo\SuppressionFond\Models\pphuman.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVidéo\SuppressionFond\Models\rvm_mobilenetv3_fp16.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVidéo\SuppressionFond\Models\rvm_mobilenetv3_fp32.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVidéo\SuppressionFond\Models\rvm_resnet50_fp32.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="AudioVideo\SuppressionFond\Models\sghm.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Conteneur\Filtres\FiltresAudio\Models\voicefixer.onnx">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="ffmpeg
              .exe">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Images\AucuneDiapositive.svg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Images\Audio.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Images\Diapositive.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Images\Image.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Images\Séquence.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Images\VidéoPrincipale.jpg">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Properties\Settings.settings">
            <Generator>SettingsSingleFileGenerator</Generator>
            <LastGenOutput>Settings.Designer.cs</LastGenOutput>
        </None>
    </ItemGroup>

    <Import Project="..\SharedProjectFile.xml" />

    <Import Project="..\MfEav\MfEav.projitems" Label="Shared" />

    <ItemGroup>
        <EditorConfigFiles Remove="F:\Catechese\EditeurAudioVideo\EditeurAudioVideo\Principal\.editorconfig" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Images\Icons32\Audio.png" />
      <None Remove="Images\Icons32\Clique.png" />
      <None Remove="Images\Icons32\Diapositive.png" />
      <None Remove="Images\Icons32\Image.png" />
      <None Remove="Images\Icons32\Séquence.png" />
      <None Remove="Images\Icons32\VidéoPrincipale.png" />
      <None Remove="Images\Icons64\Audio.png" />
      <None Remove="Images\Icons64\Clique.png" />
      <None Remove="Images\Icons64\Diapositive.png" />
      <None Remove="Images\Icons64\Formes.png" />
      <None Remove="Images\Icons64\Image.png" />
      <None Remove="Images\Icons64\Séquence.png" />
      <None Remove="Images\Icons64\VidéoPrincipale.png" />
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Images\Icons64\VidéoPrincipale.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
        <EmbeddedResource Include="Svg\SvgEditor\XMLSchema.xshd">
            <SubType>Designer</SubType>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Images\Icons32\Down.png">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Resource>
    </ItemGroup>

    <ItemGroup>
        <None Include="F:\Catechese\EditeurAudioVideo\EditeurAudioVideo\Principal\.editorconfig" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="ComputeSharp" Version="3.2.0" />
        <PackageReference Include="ComputeSharp.D2D1" Version="3.2.0" />
        <PackageReference Include="ComputeSharp.Dxc" Version="3.2.0" />
    </ItemGroup>

</Project>

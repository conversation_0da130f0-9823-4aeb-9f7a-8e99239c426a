﻿#if true
using System;
using System.Diagnostics;
using System.IO;
using static DirectX.DxUtils;

namespace DirectX;

public class D2DShaderCompiler
{
    [System.Runtime.InteropServices.DllImportAttribute("d3dcompiler_47.dll", EntryPoint = "D3DCompileFromFile", CallingConvention = System.Runtime.InteropServices.CallingConvention.StdCall)]
    private unsafe static extern int D3DCompileFromFile_(void* _fileName, void* _defines, void* _include, void* _entrypoint, void* _target, int _flags1, int _flags2, void* _code, void* _errorMsgs);

    public static void D2dCompilePixelShader(string ShaderName)
    {
        // fxc /T <shadermodel> <MyShaderFile>.hlsl /D D2D_FUNCTION /D D2D_ENTRY=<entry> /Fl <MyShaderFile>.fxlib
        string sourceFile = Path.Combine(HlslPixelShaderDirectory, ShaderName + ".hlsl");
        if (!Directory.Exists(AssetsDirectory)) Directory.CreateDirectory(AssetsDirectory);
        string exportFunction = Path.Combine(AssetsDirectory, ShaderName + ".fxlib");
        string finalFile = Path.Combine(AssetsDirectory, ShaderName + ".cso");
        string[] systemDirectories = [
            @"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um",
            @"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared",
            @"C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\winrt",
            @"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\Include\um" ];

        string arguments = $@"/T ""lib_5_0"" {sourceFile} /D D2D_FUNCTION /D D2D_ENTRY=""main"" /Fl ""{exportFunction}""";
#if DEBUG
        arguments += " /Zi /Od";
#endif
        foreach (string dir in systemDirectories)
            arguments += $@" /I ""{dir}""";

        try
        {
            using Process compiler = new();
            compiler.StartInfo.FileName = @"C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64\fxc.exe";
            compiler.StartInfo.Arguments = arguments;
            compiler.StartInfo.UseShellExecute = false;
            compiler.StartInfo.RedirectStandardOutput = true;
            compiler.StartInfo.RedirectStandardError = true;
            compiler.Start();
            Debug.WriteLine(compiler.StandardOutput.ReadToEnd());
            string error = compiler.StandardError.ReadToEnd();
            if (error != "")
            {
                Debug.WriteLine($"Error in shader : {ShaderName} : {error}.");
                Debugger.Break();
            }
            compiler.WaitForExit();
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex.ToString());
            Debugger.Break();
        }

        arguments = $@"/T ""ps_5_0"" {sourceFile} /D D2D_FULL_SHADER /D D2D_ENTRY=""main"" /E ""main"" /setprivate ""{exportFunction}""";
        arguments += $@" /Fo ""{finalFile}""";
#if DEBUG
        arguments += " /Zi /Od";
#endif
        foreach (string dir in systemDirectories)
            arguments += $@" /I ""{dir}""";

        try
        {
            using Process compiler = new();
            compiler.StartInfo.FileName = @"C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64\fxc.exe";
            compiler.StartInfo.Arguments = arguments;
            compiler.StartInfo.UseShellExecute = false;
            compiler.StartInfo.RedirectStandardOutput = true;
            compiler.StartInfo.RedirectStandardError = true;
            compiler.Start();
            Debug.WriteLine(compiler.StandardOutput.ReadToEnd());
            string error = compiler.StandardError.ReadToEnd();
            if (error != "")
            {
                Debug.WriteLine(error);
                Debugger.Break();
            }
            compiler.WaitForExit();
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex.ToString());
            Debugger.Break();
        }
    }
    public static void CompileAllChangedPixelShaders()
    {
        DirectoryInfo directoryInfo = new(HlslPixelShaderDirectory);
        DateTime headerFileLastWriteTime1 = File.GetLastWriteTime(Path.Combine(HlslPixelShaderDirectory, "Header1.hlsli"));
        DateTime headerFileLastWriteTime2 = File.GetLastWriteTime(Path.Combine(HlslPixelShaderDirectory, "Header2.hlsli"));
        foreach (FileInfo file in directoryInfo.EnumerateFiles("*.hlsl"))
        {
            // if (file.Name != "Nv12ToBgraEffect.hlsl") continue;
            if (file.Extension == ".hlsli") continue;
            string csoFilePath = Path.Combine(AssetsDirectory, Path.GetFileNameWithoutExtension(file.Name) + ".cso");
            DateTime lastFileWriteTime = File.GetLastWriteTime(Path.Combine(AssetsDirectory, csoFilePath));
            if (!File.Exists(csoFilePath) || file.LastWriteTime > lastFileWriteTime ||
                headerFileLastWriteTime1 > lastFileWriteTime || headerFileLastWriteTime2 > lastFileWriteTime)
            {
                D2dCompilePixelShader(Path.GetFileNameWithoutExtension(file.Name));
            }
        }
    }

    public static void D2dCompileComputeShader(string ShaderName)
    {
        // fxc /T <shadermodel> <MyShaderFile>.hlsl /D D2D_FUNCTION /D D2D_ENTRY=<entry> /Fl <MyShaderFile>.fxlib
        string sourceFile = Path.Combine(HlslComputeShaderDirectory, ShaderName + ".hlsl");
        if (!Directory.Exists(AssetsDirectory)) Directory.CreateDirectory(AssetsDirectory);
        string finalFile = Path.Combine(AssetsDirectory, ShaderName + ".cso");

        string arguments = $@"/T ""cs_5_0"" /E main /Fo {finalFile} {sourceFile}";
#if DEBUG
        arguments += " /Zi /Od";
#endif

        try
        {
            using Process compiler = new();
            compiler.StartInfo.FileName = @"C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64\fxc.exe";
            compiler.StartInfo.Arguments = arguments;
            compiler.StartInfo.UseShellExecute = false;
            compiler.StartInfo.RedirectStandardOutput = true;
            compiler.StartInfo.RedirectStandardError = true;
            compiler.Start();
            Debug.WriteLine(compiler.StandardOutput.ReadToEnd());
            string error = compiler.StandardError.ReadToEnd();
            if (error != "")
            {
                Debug.WriteLine($"Error in shader : {ShaderName} : {error}.");
                Debugger.Break();
            }
            compiler.WaitForExit();
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex.ToString());
            Debugger.Break();
        }
    }
    public static void CompileAllChangedComputeShaders()
    {
        DirectoryInfo directoryInfo = new(HlslComputeShaderDirectory);
        foreach (FileInfo file in directoryInfo.EnumerateFiles("*.hlsl"))
        {
            // if (file.Name != "Nv12ToBgraEffect.hlsl") continue;
            if (file.Extension == ".hlsli") continue;
            string csoFilePath = Path.Combine(AssetsDirectory, Path.GetFileNameWithoutExtension(file.Name) + ".cso");
            DateTime lastFileWriteTime = File.GetLastWriteTime(Path.Combine(AssetsDirectory, csoFilePath));
            if (!File.Exists(csoFilePath) || file.LastWriteTime > lastFileWriteTime)
            {
                D2dCompileComputeShader(Path.GetFileNameWithoutExtension(file.Name));
            }
        }
    }
}
#endif

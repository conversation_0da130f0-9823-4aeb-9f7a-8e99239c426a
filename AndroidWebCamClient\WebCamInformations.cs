﻿using System.Text.Json;

namespace AndroidWebCamClient
{
    public struct CameraResolution
    {
        public int Width { get; set; }
        public int Height { get; set; }
        public long MinimumFrameDuration { get; set; } // In nanoseconds.

        public CameraResolution(int width, int height, long minimumFrameDuration)
        {
            Width = width;
            Height = height;
            MinimumFrameDuration = minimumFrameDuration;
        }
    }

    public struct SensorDescription
    {
        public string Name { get; set; }
        public string CameraId { get; set; }
        public string Orientation { get; set; }
        public int Format { get; set; }
        public List<CameraResolution> CameraResolutions { get; set; }

        public float[] AvailableFocalLengths { get; set; }
        public float MaxDigitalZoom { get; set; }

        public SensorDescription(string name, string cameraId, string orientation, int format, List<CameraResolution> cameraResolutions, float[] availableFocalLengths, float maxDigitalZoom)
        {
            Name = name;
            CameraId = cameraId;
            Orientation = orientation;
            Format = format;
            CameraResolutions = cameraResolutions;
            AvailableFocalLengths = availableFocalLengths;
            MaxDigitalZoom = maxDigitalZoom;
        }
    }
    public class WebCamInformations
    {
        public List<SensorDescription> SensorDescriptions { get; set; }

        public WebCamInformations(List<SensorDescription> sensorDescriptions)
        {
            SensorDescriptions = sensorDescriptions;
        }

        public byte[] SerializeToBytes()
        {
            return JsonSerializer.SerializeToUtf8Bytes(this);
        }
        public static WebCamInformations Deserialize(byte[] json)
        {
            return JsonSerializer.Deserialize<WebCamInformations>(json);
        }
    }
}

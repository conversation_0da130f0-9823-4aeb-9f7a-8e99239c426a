﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0-Windows7.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<PlatformTarget>x64</PlatformTarget>
		<AllowUnsafeBlocks>True</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="AlphaDecompressor.hlsl" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="AlphaDecompressor.hlsl">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="ComputeSharp" Version="3.2.0" />
		<PackageReference Include="ComputeSharp.D2D1" Version="3.2.0" />
		<PackageReference Include="ComputeSharp.Dxc" Version="3.2.0" />
		<PackageReference Include="IronCompress" Version="1.6.3" />
		<PackageReference Include="System.Drawing.Common" Version="9.0.4" />
		<ProjectReference Include="..\DirectX\DirectX.csproj" />
	</ItemGroup>

	<Import Project="..\SharedProjectFile.xml" />

	<ItemGroup>
	  <PackageReference Update="Vortice.Mathematics" Version="1.9.3" />
	</ItemGroup>

</Project>

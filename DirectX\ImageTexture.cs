using EditeurAudioVideoCpp;
using MediaFoundation;
using System;
using System.Diagnostics;
using System.Numerics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Threading;
using Vortice;
using Vortice.Direct2D1;
using Vortice.Direct2D1.Effects;
using Vortice.Direct3D11;
using Vortice.DXGI;
using Vortice.Mathematics;
using MapFlags = Vortice.DXGI.MapFlags;
using SharedResourceFlags = Vortice.DXGI.SharedResourceFlags;


namespace DirectX
{
    public partial class ImageTexture : ImageDirectX
    {
        ID3D11Device1 _sourceDevice;
        public ID3D11Device1 SourceDevice => _sourceDevice;
        Format _format;
        public Format Format => _format;
        public BindFlags BindFlags;
        public CpuAccessFlags CpuAccessFlags;
        public ResourceOptionFlags ResourceOptionFlags;
        public ResourceUsage ResourceUsage;
        public BitmapProperties1 BitmapProperties;
        public ID3D11Texture2D _texture;
        IDXGISurface _surface;
        public IDXGISurface Surface => _surface;
        IntPtr _sharedHandle;
        public IntPtr SharedHandle => _sharedHandle;
        ID3D11Texture2D _sharedTexture;
        volatile IDXGIKeyedMutex _sharedImageKeyedMutex;
        DirectXParameters _sharedParameters;
        string _fichierSourceSharedImage;
        string _fonctionSourceSharedImage;
        int _ligneSourceSharedImage;
        bool _flipHorizontally;
        bool _flipVertically;
        volatile int _accessCount;
        public int AccessCount => _accessCount;

        public ImageTexture(ID3D11Device1 device, Format format, uint width, uint height, BindFlags bindFlags = BindFlags.None, CpuAccessFlags cpuAccessFlags = CpuAccessFlags.None, ResourceOptionFlags resourceOptionFlags = ResourceOptionFlags.SharedNTHandle | ResourceOptionFlags.SharedKeyedMutex, ResourceUsage resourceUsage = ResourceUsage.Default, BitmapProperties1 bitmapProperties = default) : base()
        {
            Debug.Assert(_refCount < 1000);
            _sourceDevice = device;
            _format = format;
            BindFlags = bindFlags;
            CpuAccessFlags = cpuAccessFlags;
            ResourceOptionFlags = resourceOptionFlags;
            ResourceUsage = resourceUsage;
            BitmapProperties = bitmapProperties;
            Debug.Assert(bitmapProperties.CompareTo(default) || format == Format.B8G8R8A8_UNorm); // Bitmaps can only be in BGRA format.
            Width = width; Height = height;
            try
            {
                Texture2DDescription desc = new()
                {
                    ArraySize = 1,
                    BindFlags = bindFlags,
                    CPUAccessFlags = cpuAccessFlags,
                    Format = format,
                    MipLevels = 1,
                    MiscFlags = resourceOptionFlags,
                    Usage = resourceUsage,
                    Width = width,
                    Height = height,
                    SampleDescription = new SampleDescription(1, 0)
                };
                _texture = device.CreateTexture2D(desc);
                _surface = _texture.QueryInterface<IDXGISurface>();
                using var dXGIResource = _texture.QueryInterface<IDXGIResource1>();
                if ((resourceOptionFlags & ResourceOptionFlags.SharedNTHandle) != 0)
                    _sharedHandle = dXGIResource.CreateSharedHandle(null, SharedResourceFlags.Read | SharedResourceFlags.Write, null);
                //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} ImageTexture - Created texture {width}x{height} with format {format}. Ptr: {_surface.NativePointer.ToInt64():X}.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
                Debugger.Break();
            }
        }
        ID2D1Image FlipImage(ID2D1DeviceContext context, ID2D1Image image)
        {
            if (!_flipHorizontally && !_flipVertically) return image;
            // Create the transform effect
            using var transformEffect = new AffineTransform2D(context);

            // Set the input image
            transformEffect.SetInput(0, image, true);

            // Define the transformation matrix
            var matrix = Matrix3x2.Identity;
            if (_flipHorizontally)
            {
                matrix.M11 = -1; // Flip horizontally
                matrix.M31 = Width; // Adjust the translation
            }
            if (_flipVertically)
            {
                matrix.M22 = -1; // Flip vertically
                matrix.M32 = Height; // Adjust the translation
            }

            // Apply the transformation
            transformEffect.TransformMatrix = matrix;

            image.Dispose();
            return transformEffect.Output;
        }
        /// <summary>
        /// NB: This function takes a reference!
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="fichierSource"></param>
        /// <param name="fonctionSource"></param>
        /// <param name="ligneSource"></param>
        /// <returns></returns>
        public override ID3D11Texture2D GetTexture(DirectXParameters parameters, [CallerFilePath] string fichierSource = "Inconnu",
            [CallerMemberName] string fonctionSource = "Inconnu", [CallerLineNumber] int ligneSource = 0)
        {
            Debug.Assert(_sharedHandle != default || parameters.D3D11Device == _sourceDevice);
            //if (_sharedHandle == default || parameters.D3D11Device == _sourceDevice) return _texture; // NB: Même la device qui a alloué une texture doit l'utiliser avec le shared handle, ou du moins avec le mutex.
            if (_sharedHandle == default)
            {
                Interlocked.Increment(ref _refCount);
                return _texture;
            }
            while (Interlocked.Increment(ref _accessCount) > 1 || (_sharedParameters != null && _sharedParameters != parameters))
            {
                Interlocked.Decrement(ref _accessCount);
                Thread.Sleep(1);
            }
            Interlocked.Increment(ref _refCount);
            _fichierSourceSharedImage = fichierSource;
            _fonctionSourceSharedImage = fonctionSource;
            _ligneSourceSharedImage = ligneSource;

            _sharedTexture = parameters.D3D11Device.OpenSharedResource1<ID3D11Texture2D>(_sharedHandle);
            _sharedImageKeyedMutex = _sharedTexture.QueryInterface<IDXGIKeyedMutex>();
            _sharedImageKeyedMutex.AcquireSync(0, 100);
            _sharedParameters = parameters;
            //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} ImageTexture - GetTexture. with refcount = {_refCount}.  Surface Ptr: {_surface.NativePointer.ToInt64():X}. StackTrace: {new StackTrace(true).ToString()}");
            Interlocked.Decrement(ref _accessCount);
            return _sharedTexture;
        }

        /// <summary>
        /// NB: This function takes a reference!
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="fichierSource"></param>
        /// <param name="fonctionSource"></param>
        /// <param name="ligneSource"></param>
        /// <returns></returns>
        public override ID2D1Image GetImage(DirectXParameters parameters, [CallerFilePath] string fichierSource = "Inconnu",
            [CallerMemberName] string fonctionSource = "Inconnu", [CallerLineNumber] int ligneSource = 0)
        {
#if DEBUG
            // We must make sure we are not running on the main thread since we could then be blocked when a function on the main thread is awaiting the Barrure to be released.
            //Debug.Assert(!System.Windows.Application.Current.Dispatcher.CheckAccess());
#endif
            // NB: Même la device qui a alloué une texture doit l'utiliser avec le shared handle, ou du moins avec le mutex.
            if (_sharedHandle == default)
            {
                if (_d2D1Image != null)
                {
                    Interlocked.Increment(ref _refCount);
                    return _d2D1Image;
                }
                DateTime temp = DateTime.Now;
                if (BitmapProperties.CompareTo(default))
                    _d2D1Image = parameters.D2D1DeviceContext.CreateImageSourceFromDxgi(new IDXGISurface[] { _surface }, 1, _format == Format.NV12 ? ColorSpaceType.YcbcrFullG22NoneP709X601 : ColorSpaceType.RgbFullG22NoneP709, ImageSourceFromDxgiOptions.None);
                else _d2D1Image = parameters.D2D1DeviceContext.CreateBitmapFromDxgiSurface(_surface, BitmapProperties);
                if (_flipHorizontally || _flipVertically) _d2D1Image = FlipImage(parameters.D2D1DeviceContext, _d2D1Image);
                Interlocked.Increment(ref _refCount);
                return _d2D1Image;
            }
            while (Interlocked.Increment(ref _accessCount) > 1 || (_sharedParameters != null && _sharedParameters != parameters))
            {
                Interlocked.Decrement(ref _accessCount);
                Thread.Sleep(1);
            }
            Interlocked.Increment(ref _refCount);
            if (_sharedParameters == parameters)
            {
                Interlocked.Decrement(ref _accessCount);
                return _d2D1Image;
            }

            _fichierSourceSharedImage = fichierSource;
            _fonctionSourceSharedImage = fonctionSource;
            _ligneSourceSharedImage = ligneSource;

            _sharedTexture = parameters.D3D11Device.OpenSharedResource1<ID3D11Texture2D>(_sharedHandle);
            using var sharedSurface = _sharedTexture.QueryInterface<IDXGISurface>();
            _d2D1Image = parameters.D2D1DeviceContext.CreateImageSourceFromDxgi(new IDXGISurface[] { sharedSurface }, 1, _format == Format.NV12 ? ColorSpaceType.YcbcrFullG22NoneP709X601 : ColorSpaceType.RgbFullG22NoneP709, ImageSourceFromDxgiOptions.None);
            _sharedImageKeyedMutex = _sharedTexture.QueryInterface<IDXGIKeyedMutex>();
            _sharedImageKeyedMutex.AcquireSync(0, 100);
            _sharedParameters = parameters;
            if (_flipHorizontally || _flipVertically) _d2D1Image = FlipImage(parameters.D2D1DeviceContext, _d2D1Image);
            //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} - ImageTexture - GetImage. with refcount = {_refCount}.  Surface Ptr: {_surface.NativePointer.ToInt64():X}. StackTrace: {new StackTrace(true).ToString()}");
            Interlocked.Decrement(ref _accessCount);
            return _d2D1Image;
        }
        public bool IsCurrentlyShared => _sharedParameters != null;
        public void UpdateFromBitmap(DirectXParameters dxParams, ID2D1Bitmap1 bitmap)
        {
            Debug.Assert(_sharedImageKeyedMutex == null);
            try
            {
                    using IDXGISurface sampleSurface = bitmap.Surface;
                    using var sharedTexture = dxParams.D3D11Device.OpenSharedResource1<ID3D11Texture2D>(_sharedHandle);
                    using var keyedMutexMF = sharedTexture.QueryInterface<IDXGIKeyedMutex>();
                    keyedMutexMF.AcquireSync(0, 500);
                    Box box = new(0, 0, 0, (int)WidthInt, (int)HeightInt, 1);
                    using var srcResource = sampleSurface.QueryInterface<ID3D11Resource>();
                    using var dstResource = sharedTexture.QueryInterface<ID3D11Resource>();
                    dxParams.D3D11Multithread.Enter(); // NB: Le immediate context n'est pas thread-safe!
                    dxParams.D3D11DeviceContext.CopySubresourceRegion(dstResource, 0, 0, 0, 0, srcResource, 0, box);
                    dxParams.D3D11DeviceContext.Flush();
                    keyedMutexMF.ReleaseSync(0);
                    dxParams.D3D11Multithread.Leave();
                _flipHorizontally = false;
                _flipVertically = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.Message);
                Debugger.Break();
            }
        }
        public void UpdateFromImfSample(DirectXParameters dxParams, IMFSample sample, bool flipHorizontally = false, bool flipVertically = false)
        {
            Debug.Assert(_sharedImageKeyedMutex == null);
            // Voir "D3D11TextureMediaSink code source" pour savoir comment gérer les samples interlaced si cela devient nécessaire...
            sample.GetBufferCount(out int bufferCount).CheckHRResult();
            IMFMediaBuffer mediaBuffer;
            if (bufferCount == 1)
                sample.GetBufferByIndex(0, out mediaBuffer).CheckHRResult();
            else sample.ConvertToContiguousBuffer(out mediaBuffer).CheckHRResult();
            try
            {
                dxParams.D2D1Multithread.Enter();

                //IMFDXGIBuffer mFDXGIBufferT = (IMFDXGIBuffer)mediaBuffer;
                Guid guid = typeof(IMFDXGIBuffer).GUID;
                IntPtr pUnknownMediaBuffer = Marshal.GetIUnknownForObject(mediaBuffer);
                var hr = Marshal.QueryInterface(pUnknownMediaBuffer, ref guid, out IntPtr pUnknownBuffer);
                if (hr == 0 && pUnknownBuffer != IntPtr.Zero)
                {
                    IMFDXGIBuffer mFDXGIBuffer = (IMFDXGIBuffer)Marshal.GetTypedObjectForIUnknown(pUnknownBuffer, typeof(IMFDXGIBuffer));
                    CSharpUtils.ConvertDXGIBufferToSurface(pUnknownBuffer, out IntPtr pDXGISurface);
                    using IDXGISurface2 sampleSurface = new(pDXGISurface);
                    mFDXGIBuffer.GetSubresourceIndex(out int subResourceIndex);
                    Marshal.Release(pUnknownMediaBuffer);
                    Marshal.ReleaseComObject(mFDXGIBuffer);
                    Marshal.Release(pUnknownBuffer);

                    // Create shared image because the MediaFoundation ID3D11Device is different than that of Parameters.
                    using var sharedTexture = dxParams.D3D11Device.OpenSharedResource1<ID3D11Texture2D>(_sharedHandle);
                    using var keyedMutexMF = sharedTexture.QueryInterface<IDXGIKeyedMutex>();
                    keyedMutexMF.AcquireSync(0, 500);
                    Box box = new(0, 0, 0, (int)WidthInt, (int)HeightInt, 1);
                    using var srcResource = sampleSurface.QueryInterface<ID3D11Resource>();
                    using var dstResource = sharedTexture.QueryInterface<ID3D11Resource>();
                    dxParams.D3D11Multithread.Enter(); // NB: Le immediate context n'est pas thread-safe!
                    dxParams.D3D11DeviceContext.CopySubresourceRegion(dstResource, 0, 0, 0, 0, srcResource, (uint)subResourceIndex, box);
                    dxParams.D3D11DeviceContext.Flush();
                    keyedMutexMF.ReleaseSync(0);
                    dxParams.D3D11Multithread.Leave();
                    _flipHorizontally = flipHorizontally;
                    _flipVertically = flipVertically;
                }
                else Marshal.Release(pUnknownMediaBuffer);
                }
            catch (Exception ex)
            {
                Trace.WriteLine(ex.Message);
                Debugger.Break();
            }
            finally
            {
                dxParams.D2D1Multithread.Leave();
                mediaBuffer.Unlock();
                Marshal.ReleaseComObject(mediaBuffer);
            }
        }

        public void UpdateFromD3D11Texture2D(DirectXParameters dxParams, ID3D11Texture2D sourceTexture, bool flipHorizontally = false, bool flipVertically = false)
        {
            Debug.Assert(_sharedImageKeyedMutex == null);
            try
            {
                dxParams.D2D1Multithread.Enter();

                // Create shared image because the source texture might be from a different device
                using var sharedTexture = dxParams.D3D11Device.OpenSharedResource1<ID3D11Texture2D>(_sharedHandle);
                using var keyedMutex = sharedTexture.QueryInterface<IDXGIKeyedMutex>();
                keyedMutex.AcquireSync(0, 500);

                Box box = new(0, 0, 0, (int)WidthInt, (int)HeightInt, 1);
                using var srcResource = sourceTexture.QueryInterface<ID3D11Resource>();
                using var dstResource = sharedTexture.QueryInterface<ID3D11Resource>();

                dxParams.D3D11Multithread.Enter(); // NB: Le immediate context n'est pas thread-safe!
                dxParams.D3D11DeviceContext.CopySubresourceRegion(dstResource, 0, 0, 0, 0, srcResource, 0, box);
                dxParams.D3D11DeviceContext.Flush();
                keyedMutex.ReleaseSync(0);
                dxParams.D3D11Multithread.Leave();

                _flipHorizontally = flipHorizontally;
                _flipVertically = flipVertically;
            }
            catch (Exception ex)
            {
                Trace.WriteLine(ex.Message);
                Debugger.Break();
            }
            finally
            {
                dxParams.D2D1Multithread.Leave();
            }
        }

        /// <summary>
        /// Requires a Cpu writeable texture (maybe also dynamic).
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="pData"></param>
        public void Update(DirectXParameters parameters, IntPtr pData, int stride)
        {
            try
            {
                parameters.D2D1Multithread.Enter();

                DataRectangle mappedRect = _surface.Map(_texture.Description.Usage == ResourceUsage.Dynamic ? MapFlags.Discard | MapFlags.Write : MapFlags.Write);
                unsafe 
                {
                    byte* yPlane = (byte*)mappedRect.DataPointer;
                    uint adjustedHeight = _format == Format.NV12 ? HeightInt * 3 / 2 : HeightInt;
                    for (int y = 0; y < adjustedHeight; y++)
                    {
                        byte* destPtr = (byte*)mappedRect.DataPointer.ToPointer() + y * mappedRect.Pitch;
                        byte* srcPtr = (byte*)pData.ToPointer() + y * stride;
                        Buffer.MemoryCopy(srcPtr, destPtr, mappedRect.Pitch * (adjustedHeight - y), WidthInt);
                    }
                }
                _surface.Unmap();
            }
            finally
            {
                parameters.D2D1Multithread.Leave();
            }
        }
        /// <summary>
        /// Requires a Cpu writeable texture (maybe also dynamic).
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="data"></param>
        public void Update(DirectXParameters parameters, byte[] data, int stride)
        {
            unsafe
            {
                fixed (byte* dataPtr = data)
                    Update(parameters, new IntPtr(dataPtr), stride);
            }
        }
        /// <summary>
        /// Requires a Cpu writeable texture (maybe also dynamic).
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="data"></param>
        public void Update(DirectXParameters parameters, ArraySegment<byte> data, int stride)
        {
            unsafe
            {
                fixed (byte* dataPtr = data.Array)
                    Update(parameters, new IntPtr(dataPtr + data.Offset), stride);
            }
        }
        public override void SaveToDiskPng(DirectXParameters parameters, string path)
        {
            bool isShared = _sharedHandle != IntPtr.Zero;
            var image = GetImage(parameters);
            image.SaveToDiskPng(parameters, path);
            image.Release();
        }
        public override void CalculerMémoireRequise(out long cpu, out long gpu)
        {
            cpu = 0;
            gpu = _format == Format.NV12 ? WidthInt * HeightInt * 3 / 2 : WidthInt * HeightInt * 4;
        }
        public override void ReleaseReference()
        {
            //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} - ImageTexture - Releasing with refcount = {_refCount}. Surface ptr: {_surface.NativePointer.ToInt64():X}. Texture ptr: {_texture.NativePointer.ToInt64():X}. StackTrace: {new StackTrace(true).ToString()}");
            Debug.Assert(_refCount > 0);
            while (Interlocked.Increment(ref _accessCount) > 1)
            {
                Interlocked.Decrement(ref _accessCount);
                Thread.Sleep(1);
            }
            if (Interlocked.Decrement(ref _refCount) == 0)
            {
                if (_sharedTexture != null)
                {
                    _sharedTexture?.Dispose();
                    _sharedTexture = null;
                    _sharedImageKeyedMutex.ReleaseSync(0);
                    _sharedImageKeyedMutex.Dispose();
                    _sharedImageKeyedMutex = null;
                    _sharedParameters = null;
                    _fichierSourceSharedImage = null;
                    _fonctionSourceSharedImage = null;
                    _ligneSourceSharedImage = 0;
                    //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} - ImageTexture - Released with refcount = {_refCount}. Surface ptr: {_surface.NativePointer.ToInt64():X}. Texture ptr: {_texture.NativePointer.ToInt64():X}. StackTrace: {new StackTrace(true).ToString()}");
                }
                _d2D1Image?.Dispose();
                _d2D1Image = null;
            }
            Interlocked.Decrement(ref _accessCount);
        }

        #region IDisposable Support
        protected virtual void Dispose(bool disposing)
        {
            Debug.Assert(_refCount == 0);
            if (disposing)
            {
                Debug.Assert(_sharedImageKeyedMutex == null);
                //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} - ImageTexture - Disposing Surface ptr: {_surface.NativePointer.ToInt64():X}. Texture ptr: {_texture.NativePointer.ToInt64():X}. StackTrace: {new StackTrace(true).ToString()}");
                if (_sharedTexture != null)
                {
                    _sharedTexture?.Dispose();
                    _sharedTexture = null;
                    _sharedImageKeyedMutex.ReleaseSync(0);
                    _sharedImageKeyedMutex.Dispose();
                    _sharedImageKeyedMutex = null;
                    _sharedParameters = null;
                    _fichierSourceSharedImage = null;
                    _fonctionSourceSharedImage = null;
                    _ligneSourceSharedImage = 0;
                    //DébogageGlobal.Add($"{DateTime.Now:hh:mm:ss:fff} - ImageTexture - Disposing Surface ptr: {_surface.NativePointer.ToInt64():X}. Texture ptr: {_texture.NativePointer.ToInt64():X}. StackTrace: {new StackTrace(true).ToString()}");
                }
                _d2D1Image?.Dispose();
                _d2D1Image = null;

                _texture?.Dispose();
                _texture = null;
                _surface?.Dispose();
                _surface = null;
            }
            if (_sharedHandle != IntPtr.Zero)
            {
                DxUtils.CloseHandle(_sharedHandle);
                _sharedHandle = IntPtr.Zero;
            }
            base.Dispose();
        }
        public override void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}

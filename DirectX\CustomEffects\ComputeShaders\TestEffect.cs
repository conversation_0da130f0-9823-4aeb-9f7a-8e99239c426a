﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Vortice;
using Vortice.Direct2D1;

namespace DirectX
{
    [CustomEffect(1)]
    public class TestEffect : ComputeShaderBase
    {
        public static uint DimensionX = 1, DimensionY = 1;

        static readonly Guid sGuid = Guid.NewGuid();
        public TestEffect() : base(sGuid)
        {
            InputCount = 1;
        }
        public override void CalculateThreadgroups(RawRect outputRect, out uint dimensionX, out uint dimensionY, out uint dimensionZ)
        {
            dimensionX = DimensionX;
            dimensionY = DimensionY;
            dimensionZ = 1;
        }

    }
}

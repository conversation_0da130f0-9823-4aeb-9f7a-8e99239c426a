﻿using DirectXControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using Vortice.Direct2D1;

namespace Commun;

public class TamponImagesBgra : IDisposable
{
    private int _nextId; // NB: This field is saved and must always increase.
    public Dictionary<int, EntréeTamponImageBgra> EntréesTamponBitmaps = new();

    [DllImport("msvcrt.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int memcmp(IntPtr a1, IntPtr a2, uint count);
    private unsafe bool Memcmp(byte[] data1, byte[] data2)
    {
        fixed (byte* p1 = data1, p2 = data2)
            return memcmp((IntPtr)p1, (IntPtr)p2, (uint)data1.Length) == 0;
    }

    public KeyValuePair<int, EntréeTamponImageBgra> TrouverEntrée(ID2D1DeviceContext6 dc, byte[] données)
    {
        foreach (KeyValuePair<int, EntréeTamponImageBgra> entréeTampon in LeProjet.TamponImagesBgra.EntréesTamponBitmaps)
        {
            if (entréeTampon.Value.LongueurDonnées != données.Length) continue;
            if (!Memcmp(entréeTampon.Value.ImageBgra.GetImageData(dc), données)) continue;
            return entréeTampon;
        }
        EntréeTamponImageBgra entréeTampon2 = new(dc, données);
        EntréesTamponBitmaps.Add(_nextId++, entréeTampon2);
        return EntréesTamponBitmaps.Last();
    }

    public string TrouverHref(ID2D1DeviceContext6 dc, string href, out int key)
    {
        int i = href.IndexOf(",", StringComparison.Ordinal);
        byte[] bytes = Convert.FromBase64String(href[(i + 1)..]);
        KeyValuePair<int, EntréeTamponImageBgra> entrée = TrouverEntrée(dc, bytes);
        key = entrée.Key;
        return $@"image:{key:D4}";
    }
    public ImageBgra TrouverBitmap(int id)
    {
        foreach (KeyValuePair<int, EntréeTamponImageBgra> entréeTampon in EntréesTamponBitmaps)
        {
            if (entréeTampon.Key != id) continue;
            return entréeTampon.Value.ImageBgra;
        }
        return null;
    }
    public SortedDictionary<int, ImageBgra> GetImageBgraArray()
    {
        SortedDictionary<int, ImageBgra> imageArray = new();
        foreach (KeyValuePair<int, EntréeTamponImageBgra> entréeTampon in EntréesTamponBitmaps)
        {
            imageArray.Add(entréeTampon.Key, entréeTampon.Value.ImageBgra);
        }
        return imageArray;
    }
    /// <summary>
    /// Crée un nouveau tampon qui contient toutes les images du tampon actuel, sauf celles qui ne sont pas utilisées actuellement.
    /// </summary>
    public TamponImagesBgra Purger()
    {
        TamponImagesBgra nouveauTampon = new();
        foreach (KeyValuePair<int, EntréeTamponImageBgra> entréeTampon in EntréesTamponBitmaps)
        {
            bool utilisée = false;
            foreach (var mvm in LeWorkspace.MédiaViewModels)
            {
                foreach (Élément élément in mvm.Conteneur.Éléments)
                {
                    if (élément is not ÉlémentSvg) continue;
                    ÉlémentSvg élémentSvg = élément as ÉlémentSvg;
                    if (élémentSvg.ImagesUtilisées.Contains(entréeTampon.Key))
                    {
                        utilisée = true;
                        break;
                    }
                }
            }
            if (utilisée) nouveauTampon.EntréesTamponBitmaps.Add(entréeTampon.Key, entréeTampon.Value);
        }
        return nouveauTampon;
    }
    public void Dispose()
    {
        foreach (KeyValuePair<int, EntréeTamponImageBgra> entréeTampon in EntréesTamponBitmaps)
        {
            entréeTampon.Value?.Dispose();
        }
        EntréesTamponBitmaps.Clear();
        GC.SuppressFinalize(this);
    }
}

﻿using System;
using System.Runtime.InteropServices;
using Vortice.Direct3D11;

namespace ImageMatter.Video
{
    /// <summary>
    /// Error codes returned by D3D11VideoReader operations
    /// </summary>
    public enum D3D11VideoReaderError
    {
        Success = 0,
        InvalidParameter = -1,
        InitializationFailed = -2,
        FileNotFound = -3,
        SeekFailed = -4,
        ReadFailed = -5,
        TextureInvalid = -6,
        CudaFailed = -7,
        Unknown = -99
    }

    /// <summary>
    /// Video properties structure containing metadata about the video
    /// </summary>
    [StructLayout(LayoutKind.Sequential)]
    public struct D3D11VideoReaderProperties
    {
        public int Width;
        public int Height;
        public double Duration;        // in seconds
        public double FrameRate;       // frames per second
        public int TotalFrames;        // estimated total frames
    }

    /// <summary>
    /// Exception thrown by D3D11VideoReader operations
    /// </summary>
    public class D3D11VideoReaderException : Exception
    {
        public D3D11VideoReaderError ErrorCode { get; }

        public D3D11VideoReaderException(D3D11VideoReaderError errorCode)
            : base(GetErrorMessage(errorCode))
        {
            ErrorCode = errorCode;
        }

        public D3D11VideoReaderException(D3D11VideoReaderError errorCode, string message)
            : base(message)
        {
            ErrorCode = errorCode;
        }

        private static string GetErrorMessage(D3D11VideoReaderError errorCode)
        {
            return errorCode switch
            {
                D3D11VideoReaderError.Success => "Operation completed successfully",
                D3D11VideoReaderError.InvalidParameter => "Invalid parameter provided",
                D3D11VideoReaderError.InitializationFailed => "Failed to initialize video reader",
                D3D11VideoReaderError.FileNotFound => "Video file not found",
                D3D11VideoReaderError.SeekFailed => "Failed to seek to specified position",
                D3D11VideoReaderError.ReadFailed => "Failed to read video frame",
                D3D11VideoReaderError.TextureInvalid => "Invalid or incompatible texture",
                D3D11VideoReaderError.CudaFailed => "CUDA operation failed",
                D3D11VideoReaderError.Unknown => "Unknown error occurred",
                _ => $"Unknown error code: {errorCode}"
            };
        }
    }

    /// <summary>
    /// C# wrapper for D3D11VideoReader native library
    /// Provides video decoding capabilities with Direct3D 11 integration
    /// </summary>
    public class D3D11VideoReader : IDisposable
    {
        private const string DllName = "ImageMatterLib.dll";
        private IntPtr _handle;
        private bool _disposed = false;

        #region P/Invoke Declarations

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_Create(
            IntPtr device,
            [MarshalAs(UnmanagedType.LPStr)] string filePath,
            out IntPtr outHandle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_CreateWithMode(
            IntPtr device,
            [MarshalAs(UnmanagedType.LPStr)] string filePath,
            int transparentFrames,
            out IntPtr outHandle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern void D3D11VideoReader_Destroy(IntPtr handle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_GetProperties(
            IntPtr handle,
            out D3D11VideoReaderProperties outProperties);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_GetWidth(IntPtr handle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_GetHeight(IntPtr handle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern double D3D11VideoReader_GetDuration(IntPtr handle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern double D3D11VideoReader_GetFrameRate(IntPtr handle);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_Seek(
            IntPtr handle,
            double timeInSeconds);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_ReadFrame(
            IntPtr handle,
            IntPtr texture,
            out double outTimestamp);

        [DllImport(DllName, CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_ReadFrameWithResize(
            IntPtr handle,
            IntPtr texture,
            int width,
            int height,
            out double outTimestamp);

        #endregion

        #region Constructors

        /// <summary>
        /// Creates a new D3D11VideoReader instance
        /// </summary>
        /// <param name="device">ID3D11Device1 from your D3D11 application</param>
        /// <param name="filePath">Path to the video file</param>
        public D3D11VideoReader(ID3D11Device1 device, string filePath)
        {
            if (device == null)
                throw new ArgumentNullException(nameof(device));
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentNullException(nameof(filePath));

            var result = (D3D11VideoReaderError)D3D11VideoReader_Create(
                device.NativePointer, filePath, out _handle);

            if (result != D3D11VideoReaderError.Success)
                throw new D3D11VideoReaderException(result);
        }

        /// <summary>
        /// Creates a new D3D11VideoReader instance with mode selection
        /// </summary>
        /// <param name="device">ID3D11Device1 from your D3D11 application</param>
        /// <param name="filePath">Path to the video file</param>
        /// <param name="transparentFrames">True for BGRA transparent frames, false for NV12 opaque frames</param>
        public D3D11VideoReader(ID3D11Device1 device, string filePath, bool transparentFrames)
        {
            if (device == null)
                throw new ArgumentNullException(nameof(device));
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentNullException(nameof(filePath));

            var result = (D3D11VideoReaderError)D3D11VideoReader_CreateWithMode(
                device.NativePointer, filePath, transparentFrames ? 1 : 0, out _handle);

            if (result != D3D11VideoReaderError.Success)
                throw new D3D11VideoReaderException(result);
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets all video properties at once
        /// </summary>
        public D3D11VideoReaderProperties Properties
        {
            get
            {
                ThrowIfDisposed();
                var result = (D3D11VideoReaderError)D3D11VideoReader_GetProperties(_handle, out var properties);
                if (result != D3D11VideoReaderError.Success)
                    throw new D3D11VideoReaderException(result);
                return properties;
            }
        }

        /// <summary>
        /// Gets the video width in pixels
        /// </summary>
        public int Width
        {
            get
            {
                ThrowIfDisposed();
                return D3D11VideoReader_GetWidth(_handle);
            }
        }

        /// <summary>
        /// Gets the video height in pixels
        /// </summary>
        public int Height
        {
            get
            {
                ThrowIfDisposed();
                return D3D11VideoReader_GetHeight(_handle);
            }
        }

        /// <summary>
        /// Gets the video duration in seconds
        /// </summary>
        public double Duration
        {
            get
            {
                ThrowIfDisposed();
                return D3D11VideoReader_GetDuration(_handle);
            }
        }

        /// <summary>
        /// Gets the video frame rate (frames per second)
        /// </summary>
        public double FrameRate
        {
            get
            {
                ThrowIfDisposed();
                return D3D11VideoReader_GetFrameRate(_handle);
            }
        }

        /// <summary>
        /// Gets whether this instance has been disposed
        /// </summary>
        public bool IsDisposed => _disposed;

        #endregion

        #region Methods

        /// <summary>
        /// Seeks to a specific time in the video
        /// </summary>
        /// <param name="timeInSeconds">Target time in seconds</param>
        public void Seek(double timeInSeconds)
        {
            ThrowIfDisposed();
            var result = (D3D11VideoReaderError)D3D11VideoReader_Seek(_handle, timeInSeconds);
            if (result != D3D11VideoReaderError.Success)
                throw new D3D11VideoReaderException(result);
        }

        /// <summary>
        /// Reads the next frame into a D3D11 texture
        /// </summary>
        /// <param name="texture">Target texture (must be correct format and size)</param>
        /// <returns>Frame timestamp in seconds</returns>
        public double ReadFrame(Texture2D texture)
        {
            ThrowIfDisposed();
            if (texture == null)
                throw new ArgumentNullException(nameof(texture));

            var result = (D3D11VideoReaderError)D3D11VideoReader_ReadFrame(
                _handle, texture.NativePointer, out var timestamp);

            if (result != D3D11VideoReaderError.Success)
                throw new D3D11VideoReaderException(result);

            return timestamp;
        }

        /// <summary>
        /// Reads the next frame into a D3D11 texture without returning timestamp
        /// </summary>
        /// <param name="texture">Target texture (must be correct format and size)</param>
        public void ReadFrame(Texture2D texture, out double timestamp)
        {
            timestamp = ReadFrame(texture);
        }

        /// <summary>
        /// Reads the next frame with optional resizing into a D3D11 texture
        /// </summary>
        /// <param name="texture">Target texture (must be correct format and size)</param>
        /// <param name="width">Target width for resizing (0 = no resize)</param>
        /// <param name="height">Target height for resizing (0 = no resize)</param>
        /// <returns>Frame timestamp in seconds</returns>
        public double ReadFrameWithResize(Texture2D texture, int width, int height)
        {
            ThrowIfDisposed();
            if (texture == null)
                throw new ArgumentNullException(nameof(texture));

            var result = (D3D11VideoReaderError)D3D11VideoReader_ReadFrameWithResize(
                _handle, texture.NativePointer, width, height, out var timestamp);

            if (result != D3D11VideoReaderError.Success)
                throw new D3D11VideoReaderException(result);

            return timestamp;
        }

        /// <summary>
        /// Reads the next frame with optional resizing into a D3D11 texture
        /// </summary>
        /// <param name="texture">Target texture (must be correct format and size)</param>
        /// <param name="width">Target width for resizing (0 = no resize)</param>
        /// <param name="height">Target height for resizing (0 = no resize)</param>
        /// <param name="timestamp">Frame timestamp in seconds</param>
        public void ReadFrameWithResize(Texture2D texture, int width, int height, out double timestamp)
        {
            timestamp = ReadFrameWithResize(texture, width, height);
        }

        /// <summary>
        /// Tries to read the next frame, returning false if at end of file or error
        /// </summary>
        /// <param name="texture">Target texture</param>
        /// <param name="timestamp">Frame timestamp (valid only if method returns true)</param>
        /// <returns>True if frame was read successfully, false if EOF or error</returns>
        public bool TryReadFrame(ID3D11Texture2D texture, out double timestamp)
        {
            timestamp = 0.0;
            ThrowIfDisposed();
            if (texture == null)
                return false;

            var result = (D3D11VideoReaderError)D3D11VideoReader_ReadFrame(
                _handle, texture.NativePointer, out timestamp);

            return result == D3D11VideoReaderError.Success;
        }

        /// <summary>
        /// Tries to read the next frame with resizing, returning false if at end of file or error
        /// </summary>
        /// <param name="texture">Target texture</param>
        /// <param name="width">Target width for resizing (0 = no resize)</param>
        /// <param name="height">Target height for resizing (0 = no resize)</param>
        /// <param name="timestamp">Frame timestamp (valid only if method returns true)</param>
        /// <returns>True if frame was read successfully, false if EOF or error</returns>
        public bool TryReadFrameWithResize(ID3D11Texture2D texture, int width, int height, out double timestamp)
        {
            timestamp = 0.0;
            ThrowIfDisposed();
            if (texture == null)
                return false;

            var result = (D3D11VideoReaderError)D3D11VideoReader_ReadFrameWithResize(
                _handle, texture.NativePointer, width, height, out timestamp);

            return result == D3D11VideoReaderError.Success;
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Releases all resources used by this D3D11VideoReader instance
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Releases resources used by this D3D11VideoReader instance
        /// </summary>
        /// <param name="disposing">True if called from Dispose(), false if called from finalizer</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && _handle != IntPtr.Zero)
            {
                D3D11VideoReader_Destroy(_handle);
                _handle = IntPtr.Zero;
                _disposed = true;
            }
        }

        /// <summary>
        /// Finalizer - ensures native resources are cleaned up
        /// </summary>
        ~D3D11VideoReader()
        {
            Dispose(false);
        }

        #endregion

        #region Private Methods

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(D3D11VideoReader));
        }

        #endregion
    }
}
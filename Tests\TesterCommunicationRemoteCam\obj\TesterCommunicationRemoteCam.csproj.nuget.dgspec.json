{"format": 1, "restore": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TesterCommunicationRemoteCam\\TesterCommunicationRemoteCam.csproj": {}}, "projects": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TesterCommunicationRemoteCam\\TesterCommunicationRemoteCam.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TesterCommunicationRemoteCam\\TesterCommunicationRemoteCam.csproj", "projectName": "TesterCommunicationRemoteCam", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TesterCommunicationRemoteCam\\TesterCommunicationRemoteCam.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TesterCommunicationRemoteCam\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}
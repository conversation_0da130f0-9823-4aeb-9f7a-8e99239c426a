{"version": 3, "targets": {"net8.0-windows7.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["ConcurrencyVisualizer >= 3.0.0", "FFMpegCore >= 5.1.0", "LiteDB >= 5.0.21", "MediaFoundation.NetCore >= 2024.5.10", "OpenCvSharp4 >= 4.10.0.20241108", "OpenCvSharp4.runtime.win >= 4.10.0.20241108", "Quamotion.TurboJpegWrapper >= 2.0.32", "SharpGen.Runtime.COM >= 2.2.0-beta", "Svg.<PERSON><PERSON> >= 2.0.0.4", "Vortice.D3DCompiler >= 3.6.2", "Vortice.DXGI >= 3.6.2", "Vortice.Direct2D1 >= 3.6.2", "Vortice.Direct3D11 >= 3.6.2", "Vortice.Direct3D12 >= 3.6.2", "Vortice.DirectML >= 3.6.2", "Vortice.DirectX >= 3.6.2", "Vortice.Dxc >= 3.6.2", "Vortice.Mathematics >= 1.9.3"]}, "packageFolders": {"F:\\NugetPackages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "projectName": "Tests", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-Windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-Windows", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-Windows", "dependencies": {"ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "FFMpegCore": {"target": "Package", "version": "[5.1.0, )"}, "LiteDB": {"target": "Package", "version": "[5.0.21, )"}, "MediaFoundation.NetCore": {"target": "Package", "version": "[2024.5.10, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.10.0.20241108, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "Svg.Skia": {"target": "Package", "version": "[2.0.0.4, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "libraryId": "OpenCvSharp4.runtime.win"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "libraryId": "OpenCvSharp4.runtime.win"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Svg.Skia' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/svg.skia/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'svg.skia'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E780-F8B5-46A7-87CA-00ED1FF5560D)).", "libraryId": "Svg.Skia"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Svg.Skia' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/svg.skia/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'svg.skia'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E780-F8B5-46A7-87CA-00ED1FF5560D)).", "libraryId": "Svg.Skia"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'LiteDB' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/litedb/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'litedb'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 06A3E905-3E28-4624-A411-7DADCF5A112F)).", "libraryId": "LiteDB"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'LiteDB' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/litedb/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'litedb'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 06A3E905-3E28-4624-A411-7DADCF5A112F)).", "libraryId": "LiteDB"}, {"code": "Undefined", "level": "Error", "message": "Échec du téléchargement du package 'System.Text.Json.7.0.2' à partir de 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/system.text.json/7.0.2/system.text.json.7.0.2.nupkg'.\r\nResponse status code does not indicate success: 401 (Unauthorized - No local versions of package 'system.text.json'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F884E-FEB8-4D0B-BCE7-1D73B102BF80))."}, {"code": "Undefined", "level": "Error", "message": "Le flux 'ORT-Nightly [https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json]' répertorie le package 'System.Text.Json.7.0.2', mais plusieurs tentatives de téléchargement du fichier nupkg ont échoué. Soit le flux n'est pas valide, soit les packages exigés ont été supprimés pendant l'opération actuelle. Vérifiez que le package existe dans le flux et réessayez."}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "OpenCvSharp4"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "OpenCvSharp4"}]}
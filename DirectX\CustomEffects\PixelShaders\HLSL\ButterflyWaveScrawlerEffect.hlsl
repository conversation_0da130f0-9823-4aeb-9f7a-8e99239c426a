#include "Header1.hlsli"
cbuffer constants : register(b0)
{
    CONSTANTBUFFER_STANDARD_FIELDS
    
    float amplitude;
    float waves;
    float colorSeparation;
}

#define COMPLEXINPUTS
#include "Header2.hlsli"

float PI = 3.14159265358979323846264;
float compute(float2 p, float progress, float2 center)
{
    float2 o = p * sin(progress * amplitude) - center;
// horizontal vector
    float2 h = float2(1., 0.);
// butterfly polar function (don't ask me why this one :))
    float theta = acos(dot(o, h)) * waves;
    return (exp(cos(theta)) - 2. * cos(4. * theta) + pow(sin((2. * theta - PI) / 24.), 5.)) / 10.;
}
float4 effet(float2 uv)
{
    float2 p = uv.xy / float2(1.0, 1.0).xy;
    float inv = 1. - progress;
    float2 dir = p - float2(.5, .5);
    float dist = length(dir);
    float disp = compute(p, progress, float2(0.5, 0.5));
    float4 texTo = getToColor(p + inv * disp);
    float4 texFrom = float4(
    getFromColor(p + progress * disp * (1.0 - colorSeparation)).r,
    getFromColor(p + progress * disp).g,
    getFromColor(p + progress * disp * (1.0 + colorSeparation)).b,
    getFromColor(p + progress * disp).a);
    return texTo * progress + texFrom * inv;
}

endCode

﻿using Microsoft.VisualStudio.Threading;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using Ceras;

namespace AndroidWebCamClient;

enum EnumCommandType { SetCameraParameters = 1, GetInformations = 2, KeepAlive = 3 }
enum EnumResponseType { EncodedFrame = 1, JpegImage = 2, Informations = 3, Parameters = 4 }
public class TcpReceiver : IDisposable
{
    public const int FirstPort = 50000;
    public const int LastPort = FirstPort + 5;
    int port = FirstPort;

    Socket _client;
    bool _stop;
    private bool disposedValue;
    public ConcurrentQueue<WebCamEncodedFrame> EncodedFramesReceived = new();
    public ConcurrentQueue<byte[]> JpegImagesReceived = new();
    public WebCamInformations WebCamInformations { get; private set; }
    WebCamParameters? _webCamParametersToSend;
    public WebCamParameters WebCamParameters { get; private set; }
    DateTime _lastKeepAliveSentTime = default;
    CerasSerializer _ceras = new();


    int _ReceiveNonBlocking(byte[] buffer)
    {
        Stopwatch sw = Stopwatch.StartNew();
        while (_client.Available < buffer.Length && sw.ElapsedMilliseconds < 1000) Thread.Sleep(0);
        if (_client.Available < buffer.Length) return 0;
        return _client.Receive(buffer, SocketFlags.None);
    }
    public TcpReceiver()
    {
        Task.Run(() =>
        {
            while (!_stop)
            {
                try
                {
                    if (_client != null)
                    {
                        _client.Close();
                        _client.Dispose();
                        Thread.Sleep(300);
                        if (++port >= LastPort) port = FirstPort;
                        Debug.WriteLine($"*********** PC: Error. Increasing port to {port}.");
                    }
                    IPEndPoint ipEndPoint = new(new IPAddress([127, 0, 0, 1]), FirstPort);
                    _client = new(ipEndPoint.AddressFamily, SocketType.Stream, ProtocolType.Tcp);
                    _client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                    _client.ReceiveBufferSize = 100000000;
                    _client.Connect(ipEndPoint);
                    Debug.WriteLine($"*********** PC: socket connected.");

                    // We start by removing all the receive garbage on the socket.
                    byte[] buffer = new byte[1];
                    int c = 0;
                    while (_ReceiveNonBlocking(buffer) > 0)
                    {
                        Debug.WriteLine($"*********** PC : Receved garbage byte {buffer[0]}.");
                        c++;
                        Thread.Sleep(0);
                    }
                    Debug.WriteLine($"*********** PC : Removed {c} garbage bytes");

                    // We send as magic number to the phone and then a Guid that it should return to us.
                    if (SendInt(0x71352104) < 4) continue;
                    Debug.WriteLine($"*********** PC : Sent magic number (0x71352104).");
                    Guid Guid = Guid.NewGuid();
                    byte[] bufferGuid = Guid.ToByteArray();
                    if (_client.Send(bufferGuid) < 16) continue;
                    Debug.WriteLine($"*********** PC : Sent guid #1.");

                    // We check if we receive the same Guid.
                    byte[] b = new byte[16];
                    int count = _ReceiveNonBlocking(b);
                    if (count != 16)
                    {
                        Debug.WriteLine($"*********** PC : Error: Not Received guid. Received only: {count} bytes.");
                        continue;
                    }
                    Debug.WriteLine($"*********** PC : Received guid #1.");
                    if (!b.SequenceEqual(bufferGuid)) continue;
                    Debug.WriteLine($"*********** PC : approved guid #1!");
                    Debug.WriteLine($"*********** Resend Guid for device to know that we are alive.");
                    if (_client.Send(bufferGuid) < 16) continue;
                    for (int i = 0; i < 16; i++)
                        Debug.WriteLine($"*********** PC : Sent guid #2 {i}: {bufferGuid[i]}");
                    Debug.WriteLine($"*********** PC: *** Communication established!");
                    bool communicationError = false;
                    DateTime lastReceiveTime = DateTime.Now;
                    while (!_stop && !communicationError)
                    {
                        if (DateTime.Now - lastReceiveTime > TimeSpan.FromSeconds(2)) SetComError();
                        lastReceiveTime = DateTime.Now;
                        void SetComError([CallerLineNumber] int lineNumber = 0)
                        {
                            communicationError = true;
                            Debug.WriteLine($"*********** Communication error, line: {lineNumber}.");
                        }
                        if (_client.Available > 0)
                        {
                            int readInt = ReceiveInt();
                            switch ((EnumResponseType)readInt)
                            {
                                case EnumResponseType.Informations:
                                    var bufferInformations = ReceiveBuffer();
                                    if (bufferInformations == null) SetComError();
                                    else WebCamInformations = JsonSerializer.Deserialize<WebCamInformations>(bufferInformations);
                                    break;
                                case EnumResponseType.Parameters:
                                    var bufferParameters = ReceiveBuffer();
                                    if (bufferParameters == null) SetComError();
                                    else WebCamParameters = JsonSerializer.Deserialize<WebCamParameters>(bufferParameters);
                                    break;
                                case EnumResponseType.EncodedFrame:
                                    var bufferEncodedFrame = ReceiveBuffer();
                                    if (bufferEncodedFrame == null) SetComError();
                                    else
                                    {
                                        while (EncodedFramesReceived.Count > 5)
                                            EncodedFramesReceived.TryDequeue(out var frame);
                                        var encodedFrame = _ceras.Deserialize<WebCamEncodedFrame>(bufferEncodedFrame);
                                        EncodedFramesReceived.Enqueue(encodedFrame);
                                    }
                                    break;
                                case EnumResponseType.JpegImage:
                                    var bufferJpegImage = ReceiveBuffer();
                                    if (bufferJpegImage == null) SetComError();
                                    else
                                    {
                                        while (JpegImagesReceived.Count > 5)
                                            JpegImagesReceived.TryDequeue(out var frame);
                                        JpegImagesReceived.Enqueue(bufferJpegImage);
                                    }
                                    break;
                                default:
                                    SetComError();
                                    break;
                            }
                        }
                        if (_webCamParametersToSend != null)
                        {
                            if (SendInt((int)EnumCommandType.SetCameraParameters) != 4) SetComError();
                            else
                            {
                                var parameters = _webCamParametersToSend.Value.SerializeToBytes();
                                if (SendInt(parameters.Length) != 4 || _client.Send(parameters) != parameters.Length) SetComError();
                                WebCamParameters = _webCamParametersToSend.Value;
                            }
                            _webCamParametersToSend = null;
                        }
                        if (DateTime.Now > _lastKeepAliveSentTime + TimeSpan.FromSeconds(10))
                        {
                            if (SendInt((int)EnumCommandType.KeepAlive) != 4) SetComError();
                            _lastKeepAliveSentTime = DateTime.Now;
                        }
                        Thread.Sleep(1);
                    }
                }
                catch (Exception ex) 
                {
                    Debug.WriteLine(ex.Message);
                }
            }
            _stop = true;
            Thread.Sleep(100);
            _client?.Close();
            _client?.Dispose();
            _stop = false;
        }).Forget();
    }

    int SendInt(int value)
    {
        byte[] buffer = new byte[4];
        BitConverter.GetBytes(value).CopyTo(buffer, 0);
        if (_client.Send(buffer) != 4) return 0;
        else return 4;
    }
    public void SendWebCamParameters(WebCamParameters webCamParameters)
    {
        _webCamParametersToSend = webCamParameters;
    }
    int ReceiveInt()
    {

        byte[] buffer = new byte[4];
        var received = _ReceiveNonBlocking(buffer);
        if (received != 4) return -1;
        return BitConverter.ToInt32(buffer, 0);
    }

    byte[] ReceiveBuffer()
    {
        int length = ReceiveInt();
        if (length < 0) return null;
        byte[] buffer = new byte[length];
        var received = _ReceiveNonBlocking(buffer);
        if (received != length) return null;
        return buffer;
    }

    public void Dispose()
    {
        _stop = true;
        Thread.Sleep(50);
        GC.SuppressFinalize(this);
    }
}


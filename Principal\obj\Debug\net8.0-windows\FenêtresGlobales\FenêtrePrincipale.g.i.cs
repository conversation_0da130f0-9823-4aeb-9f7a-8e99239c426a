﻿#pragma checksum "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0956E9823F502041B884D67CC25085A165DEEF49"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using DirectX;
using Fluent;
using Fluent.Converters;
using Fluent.Metro.Behaviours;
using Fluent.TemplateSelectors;
using Fluent.Theming;
using HelixToolkit.Wpf.SharpDX;
using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using Principal;
using PropertyTools.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using UserControls;


namespace Principal {
    
    
    /// <summary>
    /// FenêtrePrincipale
    /// </summary>
    public partial class FenêtrePrincipale : Fluent.RibbonWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LayoutRoot;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.Ribbon Ruban;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonTabItem TabPrincipal;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupProjet;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupÉdition;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupProjetSéquence;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.Gallery RibbonGalleryTypeJouer;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.Gallery RibbonGalleryTypeEnregistrer;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupSélection;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupIndicateurs;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupCorrections;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupDéphaserVidéo;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupProjetCourant;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonTabItem TabAutres;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupParamètres;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Fluent.RibbonGroupBox GroupAide;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TextBlockAvertissements;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockManipulations;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvalonDock.DockingManager DockManager;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvalonDock.Layout.LayoutAnchorable LayoutAnchorableCaptureMédia;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Principal.CaptureMédia LesCaptureMédia;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvalonDock.Layout.LayoutAnchorable LayoutAnchorableListeTransitions;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Principal.ContrôleListeTransitions ListeTransitions;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvalonDock.Layout.LayoutAnchorablePane LayoutAnchorablePaneVue;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvalonDock.Layout.LayoutAnchorable LayoutAnchorableVue;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Principal.Vue LaVue;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal AvalonDock.Layout.LayoutAnchorable LayoutAnchorablePropriétés;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal PropertyTools.Wpf.PropertyGrid PropertyGridPropriétés;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Principal;component/fen%c3%aatresglobales/fen%c3%aatreprincipale.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 14 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
            ((Principal.FenêtrePrincipale)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.Window_Closing);
            
            #line default
            #line hidden
            
            #line 15 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
            ((Principal.FenêtrePrincipale)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.RibbonWindow_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LayoutRoot = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.Ruban = ((Fluent.Ribbon)(target));
            
            #line 46 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
            this.Ruban.SelectedTabChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Ribbon_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TabPrincipal = ((Fluent.RibbonTabItem)(target));
            return;
            case 5:
            this.GroupProjet = ((Fluent.RibbonGroupBox)(target));
            return;
            case 6:
            this.GroupÉdition = ((Fluent.RibbonGroupBox)(target));
            return;
            case 7:
            this.GroupProjetSéquence = ((Fluent.RibbonGroupBox)(target));
            return;
            case 8:
            this.RibbonGalleryTypeJouer = ((Fluent.Gallery)(target));
            return;
            case 9:
            this.RibbonGalleryTypeEnregistrer = ((Fluent.Gallery)(target));
            return;
            case 10:
            this.GroupSélection = ((Fluent.RibbonGroupBox)(target));
            return;
            case 11:
            this.GroupIndicateurs = ((Fluent.RibbonGroupBox)(target));
            return;
            case 12:
            this.GroupCorrections = ((Fluent.RibbonGroupBox)(target));
            return;
            case 13:
            this.GroupDéphaserVidéo = ((Fluent.RibbonGroupBox)(target));
            return;
            case 14:
            this.GroupProjetCourant = ((Fluent.RibbonGroupBox)(target));
            return;
            case 15:
            this.TabAutres = ((Fluent.RibbonTabItem)(target));
            return;
            case 16:
            this.GroupParamètres = ((Fluent.RibbonGroupBox)(target));
            return;
            case 17:
            this.GroupAide = ((Fluent.RibbonGroupBox)(target));
            return;
            case 18:
            this.TextBlockAvertissements = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\FenêtresGlobales\FenêtrePrincipale.xaml"
            this.TextBlockAvertissements.Click += new System.Windows.RoutedEventHandler(this.TextBlockAvertissements_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TextBlockManipulations = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.DockManager = ((AvalonDock.DockingManager)(target));
            return;
            case 21:
            this.LayoutAnchorableCaptureMédia = ((AvalonDock.Layout.LayoutAnchorable)(target));
            return;
            case 22:
            this.LesCaptureMédia = ((Principal.CaptureMédia)(target));
            return;
            case 23:
            this.LayoutAnchorableListeTransitions = ((AvalonDock.Layout.LayoutAnchorable)(target));
            return;
            case 24:
            this.ListeTransitions = ((Principal.ContrôleListeTransitions)(target));
            return;
            case 25:
            this.LayoutAnchorablePaneVue = ((AvalonDock.Layout.LayoutAnchorablePane)(target));
            return;
            case 26:
            this.LayoutAnchorableVue = ((AvalonDock.Layout.LayoutAnchorable)(target));
            return;
            case 27:
            this.LaVue = ((Principal.Vue)(target));
            return;
            case 28:
            this.LayoutAnchorablePropriétés = ((AvalonDock.Layout.LayoutAnchorable)(target));
            return;
            case 29:
            this.PropertyGridPropriétés = ((PropertyTools.Wpf.PropertyGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}


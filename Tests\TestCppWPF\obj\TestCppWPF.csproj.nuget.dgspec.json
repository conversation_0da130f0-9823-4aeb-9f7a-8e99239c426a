{"format": 1, "restore": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\TestCppWPF.csproj": {}}, "projects": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\TestCppWPF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\TestCppWPF.csproj", "projectName": "TestCppWPF", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\TestCppWPF.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4.Windows": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4.WpfExtensions": {"target": "Package", "version": "[4.10.0.20241108, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}}
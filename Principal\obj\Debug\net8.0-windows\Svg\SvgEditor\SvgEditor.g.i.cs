﻿#pragma checksum "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1C23AADA08BCDEBE4D74227FAB75FBDF3EFBDA1D"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Editing;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Rendering;
using ICSharpCode.AvalonEdit.Search;
using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using Principal;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Principal {
    
    
    /// <summary>
    /// SvgEditor
    /// </summary>
    public partial class SvgEditor : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonImporter;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label ClésRepèreActuel;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonCollerDessin;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonCopierDessin;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonAnnulerChangements;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonRefaireChangements;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonEmbellir;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RetourLigne;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox Errors;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LengthError;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SvgBaseWarning;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ICSharpCode.AvalonEdit.TextEditor TheTextEditor;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Principal;component/svg/svgeditor/svgeditor.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 7 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
            ((Principal.SvgEditor)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SvgEditor_Loaded);
            
            #line default
            #line hidden
            
            #line 7 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
            ((Principal.SvgEditor)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.SvgEditor_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.ButtonImporter = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.ClésRepèreActuel = ((System.Windows.Controls.Label)(target));
            return;
            case 5:
            this.ButtonCollerDessin = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.ButtonCopierDessin = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.ButtonAnnulerChangements = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.ButtonRefaireChangements = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.ButtonEmbellir = ((System.Windows.Controls.Button)(target));
            return;
            case 10:
            this.RetourLigne = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 61 "..\..\..\..\..\Svg\SvgEditor\SvgEditor.xaml"
            this.RetourLigne.Click += new System.Windows.RoutedEventHandler(this.RetourLigne_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.Errors = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.LengthError = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.SvgBaseWarning = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TheTextEditor = ((ICSharpCode.AvalonEdit.TextEditor)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}


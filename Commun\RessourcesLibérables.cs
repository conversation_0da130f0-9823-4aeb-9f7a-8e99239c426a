﻿using System;
using System.Collections.Generic;

namespace Commun
{
    public class RessourcesLibérables
    {
        private static readonly List<RessourceLibérable> s_lesRessourcesLibérables = new();
        private static readonly object s_objetLock = new();
        static DateTime s_tempsProchainNettoyage = DateTime.MinValue;

        /// <summary>
        /// Crée une nouvelle ressource libérable.
        /// </summary>
        /// <param name="délaisExpiration"></param>
        /// <param name="libérerRessourceLibérable">Doit pouvoir être appelé de n'importe quel thread.</param>
        /// <param name="calculerMémoireRequiseRessourceLibérable">Doit pouvoir être appelé de n'importe quel thread.</param>
        /// <returns></returns>
        public static RessourceLibérable Nouvelle(int délaisExpiration,
                       LibérerRessourceLibérable libérerRessourceLibérable,
                       CalculerMémoireRequiseRessourceLibérable calculerMémoireRequiseRessourceLibérable)
        {
            RessourceLibérable ressourceLibérable = new(
                délaisExpiration,
                libérerRessourceLibérable,
                calculerMémoireRequiseRessourceLibérable);

            lock (s_objetLock)
            {
                if (DateTime.Now > s_tempsProchainNettoyage)
                {
                    while (s_lesRessourcesLibérables.Count > 0)
                    {
                        RessourceLibérable ressource = s_lesRessourcesLibérables[0];
                        if (ressource._tempsExpiration <= DateTime.Now)
                        {
                            Libérer(ressource);
                            s_lesRessourcesLibérables.Remove(ressource);
                        }
                        else break;
                    }
                    s_tempsProchainNettoyage = DateTime.Now + TimeSpan.FromMilliseconds(500);
                }
                // Méthode très rapide pour conserver l'ordre:
                int index = s_lesRessourcesLibérables.BinarySearch(ressourceLibérable);
                if (index < 0) index = ~index;
                s_lesRessourcesLibérables.Insert(index, ressourceLibérable);
            }
            return ressourceLibérable;
        }
        /// <summary>
        /// Ajourne le temps d'échéange de la ressource libérable.
        /// Une fois cette fonction appelée, on est certain que la ressource est disponible si retourne vrai
        /// pour une durée de DuréeConservationSupplémentaire
        /// </summary>
        /// <param name="ressourceLibérable">Peut être null ou devenir null durant l'appel.</param>
        /// <returns>Faux si la ressource est déjà libérée ou si ressourceLibérable est null.</returns>
        public static bool Ajourner(RessourceLibérable ressourceLibérable)
        {
            if (ressourceLibérable == null) return false;
            lock (s_objetLock)
            {
                if (ressourceLibérable._libérée) return false;
                ressourceLibérable._tempsExpiration = DateTime.Now + TimeSpan.FromMilliseconds(ressourceLibérable._délaisExpiration);
                return true;
            }
        }
        /// <summary>
        /// Libère la ressource libérable et l'efface aussi de la liste des ressources libérables.
        /// </summary>
        /// <param name="ressourceLibérable">La ressource à libérer. Peut être null ou devenir null durant lappel.</param>
        public static void Libérer(RessourceLibérable ressourceLibérable)
        {
            if (ressourceLibérable == null) return;
            lock (s_objetLock)
            {
                if (s_lesRessourcesLibérables.Contains(ressourceLibérable))
                {
                    s_lesRessourcesLibérables.Remove(ressourceLibérable);
                    ressourceLibérable._libérerRessourceLibérable();
                    ressourceLibérable._libérée = true;
                }
            }
        }
        /// <summary>
        /// Efface la ressource libérable de la liste des ressources libérables sans libérer la ressource qui y est associée.
        /// </summary>
        /// <param name="ressourceLibérable">La ressource à libérer. Peut être null ou devenir null durant lappel.</param>
        public static void Effacer(RessourceLibérable ressourceLibérable)
        {
            if (ressourceLibérable == null) return;
            lock (s_objetLock)
            {
                s_lesRessourcesLibérables.Remove(ressourceLibérable);
                ressourceLibérable._libérée = true;
            }
        }
        /// <summary>
        /// Ne doit pas être appelé, car certaines de ces ressources pourraient être en cours d'utilisation.
        /// </summary>
        public static void LibérerTout()
        {
            lock (s_objetLock) // Un autre thread peut ajouter des ressourceslibérables...
            {
                while (s_lesRessourcesLibérables.Count > 0)
                {
                    RessourceLibérable ressourceLibérable = s_lesRessourcesLibérables[0];
                    Libérer(ressourceLibérable);
                }
            }
        }
        /// <summary>
        /// Calcule la mémoire requise par le tampon de toutes les ressources libérables.
        /// </summary>
        /// <returns>Mémoire requise en bytes</returns>
        public static long CalculerMémoireRequiseToutes()
        {
            long total = 0;
            lock (s_objetLock) // Un autre thread peut ajouter des ressourceslibérables...
            {
                foreach (RessourceLibérable ressourceLibérable in s_lesRessourcesLibérables)
                    total += ressourceLibérable.CalculerMémoireRequiseRessourceLibérable();
            }
            return total;
        }
    }
}

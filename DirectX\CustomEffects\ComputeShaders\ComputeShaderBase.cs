﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;
using Vortice;
using Vortice.Direct2D1;
/// <summary>
/// See: https://github.com/MWstudios/Ensoftener/wiki
/// </summary>

namespace DirectX
{
    /// <summary>A compute shader effect inherited from <seealso cref="CustomEffectBase"/>. You can modify its input count, constant buffer and its nodes.</summary>
    /// PSB: The problem with D"D compute shaders is that it is very complicated, if at all possible, to supply to it buffers that are not input textures. Better use D3D11 compute shaders if that is needed.
    /// PSB: This code has not been fully tested!
    [CustomEffect(["Source"], null, "A shader that inherits from ComputeShaderBase", "CSB inheritors", "Ensoftener")]
    public class ComputeShaderBase : CustomEffectBase, ID2D1ComputeTransform
    {
        public static string ShaderFilePath { get; set; }
        public string ShaderFile => Path.Combine(ShaderFilePath, this.GetType().Name + ".cso");

        public Guid GUID; 
        public ID2D1ComputeInfo cInfo; 
        public ID2D1TransformGraph transformGraph;
        string _psPath;
        public byte[] psFile;
        public string ComputeShaderFilePath { get => _psPath; set { _psPath = value; psFile = File.ReadAllBytes(value); } }

        public SamplingFilter ScaleDownSampling { get; set; } = SamplingFilter.Point;
        public SamplingFilter ScaleUpSampling { get; set; } = SamplingFilter.Bilinear;
        public SamplingFilter MipmapSampling { get; set; } = SamplingFilter.Point;
        public bool AnisotropicFiltering { get; set; } = true;

        /// <summary>Initializes a new custom effect.</summary>
        /// <param name="guid">The GUID of the effect. Each GUID has one compute shader assigned to it.</param>
        public ComputeShaderBase(Guid guid) { GUID = guid; }
        public override void Initialize(ID2D1EffectContext effectContext, ID2D1TransformGraph tg)
        {
            ComputeShaderFilePath = ShaderFile;
            effectContext.LoadComputeShader(GUID, psFile, (uint)psFile.Length); 
            SetGraph(tg);
        }
        public override void SetGraph(ID2D1TransformGraph tg) { transformGraph?.Dispose(); transformGraph = tg; InputCount = tg.InputCount; tg.SetSingleTransformNode(this); }
        public void SetComputeInfo(ID2D1ComputeInfo computeInfo)
        {
            cInfo?.Dispose();
            cInfo = computeInfo; cInfo.SetComputeShader(GUID);
            cInfo.SetOutputBuffer(BufferPrecision.PerChannel8Unorm, ChannelDepth.Four);
            InputDescription inputDescription = new()
            {
                Filter = GetSampling,
                LevelOfDetailCount = 0
            };
            for (uint i = 0; i < InputCount; i++)
                cInfo.SetInputDescription(i, inputDescription);
            cInfo.SetInstructionCountHint(0);
        }
        public virtual RawRect MapInvalidRect(uint inputIndex, RawRect invalidInputRect) => invalidInputRect;

        public void MapInputRectsToOutputRect(RawRect[] inputRects, RawRect[] inputOpaqueSubRects, out RawRect outputRect, out RawRect outputOpaqueSubRect)
        {
            outputRect =  inputRects[0];
            outputOpaqueSubRect = default;
        }
        public virtual RawRect MapInputRectsToOutputRect(RawRect[] inputRects, RawRect[] inputOpaqueSubRects, out RawRect outputOpaqueSubRect)
        { 
            outputOpaqueSubRect = default;
            return inputRects[0];
        }
        public virtual void MapOutputRectToInputRects(RawRect outputRect, RawRect[] inputRects)
        {
            for (int i = 0; i < inputRects.Length; i++) inputRects[i] =
                new(int.MinValue, int.MinValue, int.MaxValue, int.MaxValue);
        }
        /// <summary>The amount of tiles of the image to render. The tile size is specified by the [numthreads] attribute in the compute shader, in pixels.</summary>
        /// <remarks>If your tiles don't cover the whole image, only a portion of the image will render.</remarks>
        public virtual void CalculateThreadgroups(RawRect outputRect, out uint dimensionX, out uint dimensionY, out uint dimensionZ)
        {
            dimensionX = 1;
            dimensionY = 1;
            dimensionZ = 1;
        }
        public uint InputCount { get; set; }
        public uint GetInputCount()
        {
            return InputCount;
        }

        public Filter GetSampling => AnisotropicFiltering ? Filter.Anisotropic : ScaleDownSampling == SamplingFilter.Bilinear ?
            ScaleUpSampling == SamplingFilter.Bilinear ?
                MipmapSampling == SamplingFilter.Bilinear ? Filter.MinMagMipLinear : Filter.MinMagLinearMipPoint :
                MipmapSampling == SamplingFilter.Bilinear ? Filter.MinLinearMagPointMipLinear : Filter.MinLinearMagMipPoint :
            ScaleUpSampling == SamplingFilter.Bilinear ?
                MipmapSampling == SamplingFilter.Bilinear ? Filter.MinPointMagMipLinear : Filter.MinPointMagLinearMipPoint :
                MipmapSampling == SamplingFilter.Bilinear ? Filter.MinMagPointMipLinear : Filter.MinMagMipPoint;
    }

}

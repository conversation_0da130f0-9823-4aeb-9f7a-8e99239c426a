{"version": 2, "dgSpecHash": "XtuN6C+jTUc=", "success": false, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "expectedPackageFiles": ["F:\\NugetPackages\\concurrencyvisualizer\\3.0.0\\concurrencyvisualizer.3.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "F:\\NugetPackages\\quamotion.turbojpegwrapper\\2.0.32\\quamotion.turbojpegwrapper.2.0.32.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime\\2.2.0-beta\\sharpgen.runtime.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime.com\\2.2.0-beta\\sharpgen.runtime.com.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\validation\\2.5.42\\validation.2.5.42.nupkg.sha512", "F:\\NugetPackages\\vortice.d3dcompiler\\3.6.2\\vortice.d3dcompiler.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct2d1\\3.6.2\\vortice.direct2d1.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d11\\3.6.2\\vortice.direct3d11.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d12\\3.6.2\\vortice.direct3d12.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directml\\3.6.2\\vortice.directml.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directx\\3.6.2\\vortice.directx.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc\\3.6.2\\vortice.dxc.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc.native\\1.0.2\\vortice.dxc.native.1.0.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxgi\\3.6.2\\vortice.dxgi.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.mathematics\\1.9.3\\vortice.mathematics.1.9.3.nupkg.sha512"], "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'System.Drawing.Common' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/system.drawing.common/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'system.drawing.common'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FB1-DD9F-47F3-95B1-0A83CD8EB88B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "libraryId": "System.Drawing.Common", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F2693F-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "libraryId": "ComputeSharp", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.Dxc' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.dxc/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.dxc'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1DE268D2-FFDB-4B00-99A7-EE1AAA4280FF)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "libraryId": "ComputeSharp.Dxc", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.D2D1' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.d2d1/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.d2d1'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FD6-DD9F-47F3-95B1-0A83CD8EB88B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "libraryId": "ComputeSharp.D2D1", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Microsoft.Win32.SystemEvents' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/microsoft.win32.systemevents/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'microsoft.win32.systemevents'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 06A3E9A0-3E28-4624-A411-7DADCF5A112F)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestD3D11ComputeShader\\TestD3D11ComputeShader.csproj", "libraryId": "Microsoft.Win32.SystemEvents", "targetGraphs": []}]}
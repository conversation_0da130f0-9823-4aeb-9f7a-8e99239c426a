﻿using System;
using System.Numerics;
using System.Runtime.InteropServices;
using Vortice.Direct2D1;
using Vortice.Mathematics;

namespace DirectX;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct CircleCropEffectParameters
{
    public int OutputWidth;
    public int OutputHeight;
    public int TextureFromTransparent;
    public int TextureToTransparent;
    public float Progress;
    public float Ratio;
    public float2 dummy; // Next Vector4 must be aligned on a 16-byte boundary for hlsl.
    public Vector4 BgColor;
}

public class CircleCropEffect : ComplexEffetEffectBase
{
    static readonly Guid s_Guid = Guid.NewGuid();
    public CircleCropEffect() : base(s_Guid) { }

    public CircleCropEffectParameters cBuffer;

    [CustomEffectProperty(PropertyType.IUnknown, 0)]
    public int OutputWidth { get { return cBuffer.OutputWidth; } set { cBuffer.OutputWidth = value; OutputWidthInternal = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 1)]
    public int OutputHeight { get { return cBuffer.OutputHeight; } set { cBuffer.OutputHeight = value; OutputHeightInternal = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 2)]
    public bool TextureFromTransparent { get { return cBuffer.TextureFromTransparent != 0; } set { cBuffer.TextureFromTransparent = value ? 1 : 0; } }

    [CustomEffectProperty(PropertyType.IUnknown, 3)]
    public bool TextureToTransparent { get { return cBuffer.TextureToTransparent != 0; } set { cBuffer.TextureToTransparent = value ? 1 : 0; } }

    [CustomEffectProperty(PropertyType.IUnknown, 4)]
    public float Progress { get { return cBuffer.Progress; } set { cBuffer.Progress = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 5)]
    public float Ratio { get { return cBuffer.Ratio; } set { cBuffer.Ratio = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 6)]
    public Vector4 BgColor { get { return cBuffer.BgColor; } set { cBuffer.BgColor = value; } }

    public override void PrepareForRender(ChangeType changeType) //Passes the struct to Gpu. Without this, it would be useless.
    {
        dInfo?.SetPixelShaderConstantBuffer(cBuffer);
    }
}

﻿using ComputeSharp;
using DirectX;
using IronCompress;
using System.Buffers;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Vortice.Direct3D11;

namespace AlphaCodec;

// Define constants
internal partial class AlphaCompressor : IDisposable
{
    int _width, _height;
    internal int Width => _width;
    internal int Height => _height;
    const int RepeatShort = 1, RepeatLong = 254;

    DirectXParameters _parameters;

    ReadWriteBuffer<int> readWriteColumnHeaders; 
    ReadWriteBuffer<int> readWriteOutput1;
    ReadWriteBuffer<int> readWriteOutput2;
    Iron _iron;

    internal unsafe AlphaCompressor(DirectXParameters parameters, int width, int height)
    {
        _width = width; _height = height;
        _parameters = parameters;

        readWriteColumnHeaders = _parameters.GraphicsDevice.AllocateReadWriteBuffer<int>(width);
        readWriteOutput1 = _parameters.GraphicsDevice.AllocateReadWriteBuffer<int>(width * height);
        readWriteOutput2 = _parameters.GraphicsDevice.AllocateReadWriteBuffer<int>(width * height);

        _iron = new Iron(ArrayPool<byte>.Shared);
    }
    internal unsafe AlphaData CompressFromReadWriteBuffer(ReadWriteTexture2D<R8, float> input)
    {
        Debug.Assert(input.Width == _width && input.Height == _height);

        Stopwatch sw = Stopwatch.StartNew();
        _parameters.GraphicsDevice.For(_width, new CompressColumns(input, _height, readWriteColumnHeaders, readWriteOutput1));
        _parameters.GraphicsDevice.For(1, new AddColumnHeaders(readWriteColumnHeaders, _width));
        _parameters.GraphicsDevice.For(_width, new CompressLines(readWriteColumnHeaders, readWriteOutput1, readWriteOutput2));

        int[] columnHeaders = new int[_width];
        readWriteColumnHeaders.CopyTo(columnHeaders);
        int alphaLength = columnHeaders[^1];
        int[] alpha = new int[alphaLength];
        readWriteOutput2.CopyTo(alpha, 0, 0, alphaLength);

        AlphaData alphaData = new();

        Stopwatch stopwatch = Stopwatch.StartNew();
        ReadOnlySpan<byte> byteSpan = MemoryMarshal.Cast<int, byte>(columnHeaders);
        using (var compressed = _iron.Compress(Codec.Brotli, byteSpan))
            alphaData.CompressedColumnHeaders = compressed.AsSpan().ToArray();

        byteSpan = MemoryMarshal.Cast<int, byte>(alpha);
        using (var compressed = _iron.Compress(Codec.Brotli, byteSpan))
            alphaData.CompressedAlpha = compressed.AsSpan().ToArray();
        alphaData.AlphaLength = (uint)alpha.Length;
        stopwatch.Stop();
        sw.Stop();

        alphaData.WidthAlpha = (uint)_width;
        alphaData.HeightAlpha = (uint)_height;

        return alphaData;
    }
    internal unsafe AlphaData CompressFromR8(R8[] input)
    {
        var readWriteInput = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<R8, float>(_width, _height);
        readWriteInput.CopyFrom(input);

        return CompressFromReadWriteBuffer(readWriteInput);
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.X)]
    [GeneratedComputeShaderDescriptor]
    internal readonly partial struct CompressColumns(IReadWriteNormalizedTexture2D<float> input, int height, ReadWriteBuffer<int> columnHeaders, ReadWriteBuffer<int> output) : IComputeShader
    {
        public void Execute()
        {
            int x = ThreadIds.X;
            int width = DispatchSize.X;
            int pos = 0;
            int count = 0;
            int value = 0;
            int previous = -1;
            for (int y = 0; y < height; y++)
            {
                {
                    value = (int)(input[x, y] * 255);
                    if (value == RepeatShort) value = 0;
                    if (value == RepeatLong) value = 255;
                    if (value == previous) count++;
                    else
                    {
                        if (count > 255)
                        {
                            output[pos++ * width + x] = RepeatLong;
                            output[pos++ * width + x] = count / 256;
                            output[pos++ * width + x] = count % 256;
                        }
                        else if (count > 2)
                        {
                            output[pos++ * width + x] = RepeatShort;
                            output[pos++ * width + x] = count;
                        }
                        else if (count <= 2)
                        {
                            for (int c = 0; c < count; c++) output[pos++ * width + x] = previous;
                        }
                        count = 0;
                        output[pos++ * width + x] = value;
                        previous = value;
                    }
                }
                // NB: The end of the column is filled by the previous value...
                columnHeaders[x] = pos; // NB: Column height.
            }
        }
    }
    
    [ThreadGroupSize(1, 1, 1)]
    [GeneratedComputeShaderDescriptor]
    internal readonly partial struct AddColumnHeaders(ReadWriteBuffer<int> columnHeaders, int width) : IComputeShader
    {
        public void Execute()
        {
            int indexCompressed = 0;
            for (int x = 0; x < width; x++)
            {
                indexCompressed += columnHeaders[x];
                columnHeaders[x] = indexCompressed;
            }
        }
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.X)]
    [GeneratedComputeShaderDescriptor]
    internal readonly partial struct CompressLines(ReadWriteBuffer<int> columnHeaders, ReadWriteBuffer<int> input, ReadWriteBuffer<int> output) : IComputeShader
    {
        public void Execute()
        {
            int x = ThreadIds.X;
            int width = DispatchSize.X;

            int startIndexCompressed = (x == 0 ? 0 : columnHeaders[x - 1]);
            int length = columnHeaders[x] - startIndexCompressed;
            for (int pos = 0; pos < length; pos++)
            {
                int indexInput = pos * width + x;
                int value = input[indexInput];
                output[startIndexCompressed + pos] = value;
            }
        }
    }

    public void Dispose()
    {
        readWriteColumnHeaders?.Dispose();
        readWriteOutput1?.Dispose();
        readWriteOutput2?.Dispose();
        _iron = null;
    }
}
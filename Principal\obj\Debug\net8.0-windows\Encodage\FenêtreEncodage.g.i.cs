﻿#pragma checksum "..\..\..\..\Encodage\FenêtreEncodage.xaml" "{************************************}" "77A4C7749E02CDE9D61B4610145839638B3D4849"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using Principal;
using PropertyTools.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Principal {
    
    
    /// <summary>
    /// FenêtreEncodage
    /// </summary>
    public partial class FenêtreEncodage : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TextBoxChoixDiapositivesÀRégénérer;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TextBoxChoixDiapositives;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ChoixDiapositives;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox ListBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnregistrerProfils;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReprendreProfilsDéfaut;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonDébuter;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonAnnuler;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Principal;component/encodage/fen%c3%aatreencodage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TextBoxChoixDiapositivesÀRégénérer = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TextBoxChoixDiapositives = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ChoixDiapositives = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.ListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 5:
            this.EnregistrerProfils = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
            this.EnregistrerProfils.Click += new System.Windows.RoutedEventHandler(this.EnregistrerProfils_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ReprendreProfilsDéfaut = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
            this.ReprendreProfilsDéfaut.Click += new System.Windows.RoutedEventHandler(this.ReprendreProfilsDéfaut_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ButtonDébuter = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
            this.ButtonDébuter.Click += new System.Windows.RoutedEventHandler(this.ButtonDébuter_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ButtonAnnuler = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\Encodage\FenêtreEncodage.xaml"
            this.ButtonAnnuler.Click += new System.Windows.RoutedEventHandler(this.ButtonAnnuler_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}


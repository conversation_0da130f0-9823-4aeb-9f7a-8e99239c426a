{"version": 2, "dgSpecHash": "9s1a30Os3Ao=", "success": true, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\ceras\\4.1.7\\ceras.4.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading\\17.10.48\\microsoft.visualstudio.threading.17.10.48.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.threading.analyzers\\17.10.48\\microsoft.visualstudio.threading.analyzers.17.10.48.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.visualstudio.validation\\17.8.8\\microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\precisiontimer.net\\*******\\precisiontimer.net.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.5.2\\system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vanara.core\\4.0.3\\vanara.core.4.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vanara.pinvoke.gdi32\\4.0.3\\vanara.pinvoke.gdi32.4.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vanara.pinvoke.kernel32\\4.0.3\\vanara.pinvoke.kernel32.4.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vanara.pinvoke.shared\\4.0.3\\vanara.pinvoke.shared.4.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\vanara.pinvoke.user32\\4.0.3\\vanara.pinvoke.user32.4.0.3.nupkg.sha512"], "logs": []}
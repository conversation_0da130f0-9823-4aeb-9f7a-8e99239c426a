﻿using ComputeSharp;
using DirectX;
using IronCompress;
using System;
using System.Buffers;
using System.Diagnostics;
using System.Runtime.InteropServices;
using Vortice.Direct3D;
using Vortice.Direct3D11;
using Vortice.DXGI;

namespace AlphaCodec;

// Define constants
[StructLayout(LayoutKind.Sequential)]
struct Constants
{
    internal uint width;
    internal uint height;
}

internal class AlphaDecompressor : IDisposable
{
    uint _width, _height;
    internal uint Width => _width;
    internal uint Height => _height;
    DirectXParameters _parameters;
    ID3D11Device1 Device => _parameters.D3D11Device;
    ID3D11DeviceContext Context => _parameters.D3D11DeviceContext;
    ID3D11Buffer _constantBuffer;
    ID3D11Buffer _columnHeadersBuffer;
    ID3D11Buffer _inputBuffer;
    ID3D11Texture2D _outputTexture;
    ID3D11ComputeShader _computeShader;
    ID3D11ShaderResourceView _columnHeadersSRV;
    ID3D11ShaderResourceView _inputSRV;
    ID3D11UnorderedAccessView _outputUAV;
    Iron _iron;

    internal unsafe AlphaDecompressor(DirectXParameters parameters, uint width, uint height)
    {
        _width = width; _height = height;
        _parameters = parameters;
        // InitializeGraphics constants
        Constants constants = new() { width = width, height = height };

        // Create and bind constant buffer
        BufferDescription bufferDescription = new()
        {
            Usage = ResourceUsage.Default,
            ByteWidth = 16, // Must be a multiple of 16!
            BindFlags = BindFlags.ConstantBuffer
        };
        _constantBuffer = parameters.D3D11Device.CreateBuffer(bufferDescription, new SubresourceData(&constants));

        parameters.D3D11DeviceContext.CSSetConstantBuffers(0, [_constantBuffer]);

        // Create and bind byte address buffers
        _columnHeadersBuffer = CreateStructuredBuffer<int>(Device, null, width * sizeof(int), ResourceUsage.Dynamic, CpuAccessFlags.Write);
        _inputBuffer = CreateStructuredBuffer<int>(Device, null, sizeof(int) * width * height, ResourceUsage.Dynamic, CpuAccessFlags.Write);
        _outputTexture = CreateTexture2D(Device, width, height);

        // Create shader resource views
        _columnHeadersSRV = Device.CreateShaderResourceView(_columnHeadersBuffer, new ShaderResourceViewDescription
        {
            Format = Format.Unknown,
            ViewDimension = ShaderResourceViewDimension.Buffer,
            Buffer = new BufferShaderResourceView() { ElementWidth = width },
        });

        _inputSRV = Device.CreateShaderResourceView(_inputBuffer, new ShaderResourceViewDescription
        {
            Format = Format.Unknown,
            ViewDimension = ShaderResourceViewDimension.Buffer,
            Buffer = new BufferShaderResourceView() { ElementWidth = width * height },
        });

        // Create unordered access view for output texture
        _outputUAV = Device.CreateUnorderedAccessView(_outputTexture, new UnorderedAccessViewDescription
        {
            Format = Format.R8_UNorm,
            ViewDimension = UnorderedAccessViewDimension.Texture2D,
            Buffer = new BufferUnorderedAccessView
            {
                FirstElement = 0,
                NumElements = width * height
            }
        });

        Context.CSSetShaderResources(0, [_columnHeadersSRV]);
        Context.CSSetShaderResources(1, [_inputSRV]);
        Context.CSSetUnorderedAccessViews(0, [_outputUAV], null);

        var bytecode = Vortice.D3DCompiler.Compiler.CompileFromFile("AlphaDecompressor.hlsl", "CSMain", "cs_5_0");

        _computeShader = Device.CreateComputeShader(bytecode.Span);

        Context.CSSetShader(_computeShader);

        _iron = new Iron(ArrayPool<byte>.Shared);
    }
    /// <summary>
    /// The output texture must not be freed by the caller.
    /// </summary>
    /// <param name="alphaData"></param>
    /// <returns></returns>
    internal unsafe ID3D11Texture2D DecompressToTexture(AlphaData alphaData)
    {
        _parameters.D2D1Multithread.Enter(); 

        int[] columnHeaders, input;

        using (var uncompressed = _iron.Decompress(Codec.Brotli, alphaData.CompressedColumnHeaders, (int)_width * sizeof(int)))
            columnHeaders = MemoryMarshal.Cast<byte, int>(uncompressed.AsSpan()).ToArray();

        using (var uncompressed = _iron.Decompress(Codec.Brotli, alphaData.CompressedAlpha, (int)(alphaData.AlphaLength * sizeof(int))))
        input = MemoryMarshal.Cast<byte, int>(uncompressed.AsSpan()).ToArray();
        
        UpdateStructuredBuffer(Device, Context, _inputBuffer, input);
        UpdateStructuredBuffer(Device, Context, _columnHeadersBuffer, columnHeaders);

        Stopwatch stopwatch = Stopwatch.StartNew();
        // Dispatch the compute shader
        Context.Dispatch(_width, 1, 1);

        stopwatch.Stop();

        _parameters.D2D1Multithread.Leave();

        return _outputTexture;
    }

    internal unsafe R8[] DecompressToR8(AlphaData alphaData)
    {
        var texture = DecompressToTexture(alphaData);
        var output = new byte[_width * _height];
        ReadBackTexture2D(Device, Context, texture, output, _width, _height);
        return MemoryMarshal.Cast<byte, R8>(output).ToArray();
    }

    // Helper methods
    unsafe ID3D11Buffer CreateByteAddressBuffer(ID3D11Device device, byte[] data, uint size)
    {
        fixed (byte* ptr = data)
            return device.CreateBuffer(new BufferDescription
            {
                Usage = ResourceUsage.Default,
                ByteWidth = size,
                BindFlags = BindFlags.ShaderResource | BindFlags.UnorderedAccess,
                MiscFlags = ResourceOptionFlags.BufferAllowRawViews
            }, new SubresourceData(ptr));
    }

    unsafe ID3D11Buffer CreateStructuredBuffer<T>(ID3D11Device device, T[] data, uint size, ResourceUsage resourceUsage = ResourceUsage.Default, CpuAccessFlags cpuAccessFlags = CpuAccessFlags.None) where T : unmanaged
    {
        fixed (T* ptr = data)
            return device.CreateBuffer(new BufferDescription
            {
                Usage = resourceUsage,
                ByteWidth = size,
                BindFlags = BindFlags.ShaderResource,
                MiscFlags = ResourceOptionFlags.BufferStructured,
                StructureByteStride = (uint)sizeof(T),
                CPUAccessFlags = cpuAccessFlags,
            }, data != null ? new SubresourceData(ptr) : null);
    }

    unsafe ID3D11Texture2D CreateTexture2D(ID3D11Device device, uint width, uint height)
    {
        return device.CreateTexture2D(new Texture2DDescription
        {
            ArraySize = 1,
            BindFlags = BindFlags.ShaderResource | BindFlags.UnorderedAccess,
            CPUAccessFlags = CpuAccessFlags.None,
            Format = Format.R8_UNorm,
            MipLevels = 1,
            MiscFlags = ResourceOptionFlags.None,
            Usage = ResourceUsage.Default,
            Width = width,
            Height = height,
            SampleDescription = new SampleDescription(1, 0)
        });
    }
    unsafe void UpdateStructuredBuffer(ID3D11Device device, ID3D11DeviceContext context, ID3D11Buffer buffer, int[] data)
    {
        fixed (int* ptr = data)
        {
            var datamappedSubresource = context.Map(buffer, 0, MapMode.WriteDiscard, Vortice.Direct3D11.MapFlags.None);
            unsafe
            {
                Buffer.MemoryCopy(ptr, datamappedSubresource.DataPointer.ToPointer(), data.Length * sizeof(int), data.Length * sizeof(int));
            }
            context.Unmap(buffer, 0);
        }

        //// Create a staging buffer to hold the new data
        //ID3D11Buffer stagingBuffer = Device.CreateBuffer(new BufferDescription
        //{
        //    Usage = ResourceUsage.Staging,
        //    ByteWidth = data.Length * sizeof(int),
        //    BindFlags = BindFlags.None,
        //    CPUAccessFlags = CpuAccessFlags.Write,
        //    MiscFlags = ResourceOptionFlags.None
        //});

        //// Map the staging buffer to write the new data
        //MappedSubresource mappedResource = Context.Map(stagingBuffer, 0, MapMode.Write, Vortice.Direct3D11.MapFlags.None);
        //Marshal.Copy(data, 0, mappedResource.DataPointer, data.Length);
        //Context.Unmap(stagingBuffer, 0);

        //// Copy the staging buffer to the structured buffer
        //Context.CopyResource(buffer, stagingBuffer);

        //// ReleaseReference the staging buffer
        //stagingBuffer.Dispose();
    }

    void ReadBackStructuredBuffer(ID3D11Device device, ID3D11DeviceContext context, ID3D11Buffer buffer, int[] outputData, uint size)
    {
        using ID3D11Buffer stagingBuffer = device.CreateBuffer(new BufferDescription
        {
            Usage = ResourceUsage.Staging,
            ByteWidth = size,
            CPUAccessFlags = CpuAccessFlags.Read
        });

        context.CopyResource(stagingBuffer, buffer);

        MappedSubresource mappedResource = context.Map(stagingBuffer, 0, MapMode.Read, Vortice.Direct3D11.MapFlags.None);
        Marshal.Copy(mappedResource.DataPointer, outputData, 0, outputData.Length);
        context.Unmap(stagingBuffer, 0);
    }

    void ReadBackTexture2D(ID3D11Device device, ID3D11DeviceContext context, ID3D11Texture2D texture, byte[] outputData, uint width, uint height)
    {
        // Create a staging texture
        using ID3D11Texture2D stagingTexture = device.CreateTexture2D(new Texture2DDescription
        {
            Width = width,
            Height = height,
            MipLevels = 1,
            ArraySize = 1,
            Format = Format.R8_UNorm, 
            SampleDescription = new SampleDescription(1, 0),
            Usage = ResourceUsage.Staging,
            BindFlags = BindFlags.None,
            CPUAccessFlags = CpuAccessFlags.Read,
            MiscFlags = ResourceOptionFlags.None
        });

        // Copy the texture to the staging texture
        context.CopyResource(stagingTexture, texture);

        // Map the staging texture to read the data
        MappedSubresource mappedResource = context.Map(stagingTexture, 0, MapMode.Read, Vortice.Direct3D11.MapFlags.None);

        // Copy the data from the mapped resource to the output array
        int rowPitch = (int)width;
        for (int y = 0; y < height; y++)
        {
            IntPtr srcPtr = (nint)(mappedResource.DataPointer + y * mappedResource.RowPitch);
            Marshal.Copy(srcPtr, outputData, y * rowPitch, rowPitch);
        }

        // Unmap the staging texture
        context.Unmap(stagingTexture, 0);
    }

    public void Dispose()
    {
        _constantBuffer?.Dispose();
        _columnHeadersBuffer?.Dispose();
        _inputBuffer?.Dispose();
        _outputTexture?.Dispose();
        _computeShader?.Dispose();
        _columnHeadersSRV?.Dispose();
        _inputSRV?.Dispose();
        _outputUAV?.Dispose();
        _iron = null;
    }
}
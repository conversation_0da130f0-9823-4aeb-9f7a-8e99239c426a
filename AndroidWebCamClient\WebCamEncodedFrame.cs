﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AndroidWebCamClient
{
    [Flags]
    public enum WebCamMediaCodecBufferFlags
    {
        None = 0,
        KeyFrame = 1,
        SyncFrame = 1,
        CodecConfig = 2,
        EndOfStream = 4,
        PartialFrame = 8,
        DecodeOnly = 32
    }
    public struct WebCamEncodedFrame
    {
        public WebCamMediaCodecBufferFlags Flags;
        public byte[] Data;
        public byte[] ParameterSets;
    }
}

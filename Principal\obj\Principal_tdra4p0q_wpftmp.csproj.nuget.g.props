﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">F:\NugetPackages</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">F:\NugetPackages;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.1</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="F:\NugetPackages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)\sharpgen.runtime.com\2.2.0-beta\build\SharpGen.Runtime.COM.props" Condition="Exists('$(NuGetPackageRoot)\sharpgen.runtime.com\2.2.0-beta\build\SharpGen.Runtime.COM.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.directx\3.6.2\build\Vortice.DirectX.props" Condition="Exists('$(NuGetPackageRoot)\vortice.directx\3.6.2\build\Vortice.DirectX.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.dxgi\3.6.2\build\Vortice.DXGI.props" Condition="Exists('$(NuGetPackageRoot)\vortice.dxgi\3.6.2\build\Vortice.DXGI.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.dxc\3.6.2\build\Vortice.Dxc.props" Condition="Exists('$(NuGetPackageRoot)\vortice.dxc\3.6.2\build\Vortice.Dxc.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.direct3d12\3.6.2\build\Vortice.Direct3D12.props" Condition="Exists('$(NuGetPackageRoot)\vortice.direct3d12\3.6.2\build\Vortice.Direct3D12.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.directml\3.6.2\build\Vortice.DirectML.props" Condition="Exists('$(NuGetPackageRoot)\vortice.directml\3.6.2\build\Vortice.DirectML.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.direct3d11\3.6.2\build\Vortice.Direct3D11.props" Condition="Exists('$(NuGetPackageRoot)\vortice.direct3d11\3.6.2\build\Vortice.Direct3D11.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.direct2d1\3.6.2\build\Vortice.Direct2D1.props" Condition="Exists('$(NuGetPackageRoot)\vortice.direct2d1\3.6.2\build\Vortice.Direct2D1.props')" />
    <Import Project="$(NuGetPackageRoot)\vortice.d3dcompiler\3.6.2\build\Vortice.D3DCompiler.props" Condition="Exists('$(NuGetPackageRoot)\vortice.d3dcompiler\3.6.2\build\Vortice.D3DCompiler.props')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.ai.directml\1.15.4\build\Microsoft.AI.DirectML.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.ai.directml\1.15.4\build\Microsoft.AI.DirectML.props')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.ml.onnxruntime.directml\1.21.1\build\netstandard2.1\Microsoft.ML.OnnxRuntime.DirectML.props" Condition="Exists('$(NuGetPackageRoot)\microsoft.ml.onnxruntime.directml\1.21.1\build\netstandard2.1\Microsoft.ML.OnnxRuntime.DirectML.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMicrosoft_VisualStudio_Threading_Analyzers Condition=" '$(PkgMicrosoft_VisualStudio_Threading_Analyzers)' == '' ">F:\NugetPackages\microsoft.visualstudio.threading.analyzers\17.13.61</PkgMicrosoft_VisualStudio_Threading_Analyzers>
    <PkgMicrosoft_Xaml_Behaviors_Wpf Condition=" '$(PkgMicrosoft_Xaml_Behaviors_Wpf)' == '' ">F:\NugetPackages\microsoft.xaml.behaviors.wpf\1.1.77</PkgMicrosoft_Xaml_Behaviors_Wpf>
  </PropertyGroup>
</Project>
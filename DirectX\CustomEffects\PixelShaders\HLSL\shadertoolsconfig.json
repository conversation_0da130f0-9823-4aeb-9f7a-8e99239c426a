{"hlsl.preprocessorDefinitions": {"MY_PREPROCESSOR_DEFINE_1": "Foo", "MY_PREPROCESSOR_DEFINE_2": 1}, "hlsl.additionalIncludeDirectories": ["C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.19041.0\\um", "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.19041.0\\shared", "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.19041.0\\winrt", "C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\Include\\um"], "hlsl.virtualDirectoryMappings": {"/Project": "C:\\MyProject\\Shaders"}}
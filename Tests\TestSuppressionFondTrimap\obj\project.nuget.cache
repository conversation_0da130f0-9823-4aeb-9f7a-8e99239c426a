{"version": 2, "dgSpecHash": "WfphJxf6+RY=", "success": false, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.Core.Wpf' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.core.wpf/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.core.wpf'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E7A7-F8B5-46A7-87CA-00ED1FF5560D)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "HelixToolkit.Core.Wpf", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BC2-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "HelixToolkit", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'GeometRi' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/geometri/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'geometri'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 2A11D4EA-5B55-4083-AFA8-EF67C67AB17B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "GeometRi", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core.Wpf' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core.wpf/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core.wpf'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10F92-DD9F-47F3-95B1-0A83CD8EB88B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "HelixToolkit.SharpDX.Core.Wpf", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F2693F-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "ComputeSharp", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.Dxc' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.dxc/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.dxc'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1DE268D2-FFDB-4B00-99A7-EE1AAA4280FF)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "ComputeSharp.Dxc", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.D2D1' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.d2d1/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.d2d1'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FD6-DD9F-47F3-95B1-0A83CD8EB88B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "ComputeSharp.D2D1", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CCBA5-1DD4-486C-B451-DD20F799B176)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "HelixToolkit.SharpDX.Core", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CCBA5-1DD4-486C-B451-DD20F799B176)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "HelixToolkit.SharpDX.Core", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F25F5E-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "libraryId": "ComputeSharp.Core", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Échec du téléchargement du package 'Microsoft.AI.DirectML.1.14.1' à partir de 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/microsoft.ai.directml/1.14.1/microsoft.ai.directml.1.14.1.nupkg'.\r\nResponse status code does not indicate success: 401 (Unauthorized - No local versions of package 'microsoft.ai.directml'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F25E19-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Le flux 'ORT-Nightly [https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json]' répertorie le package 'Microsoft.AI.DirectML.1.14.1', mais plusieurs tentatives de téléchargement du fichier nupkg ont échoué. Soit le flux n'est pas valide, soit les packages exigés ont été supprimés pendant l'opération actuelle. Vérifiez que le package existe dans le flux et réessayez.", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "targetGraphs": []}]}
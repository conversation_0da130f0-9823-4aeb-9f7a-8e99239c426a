﻿<Window x:Class="AnglePickerControl.View.AnglePickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AnglePickerControl.View"
        xmlns:controls="clr-namespace:AnglePickerControl.View.Controls"
        xmlns:vm="clr-namespace:AnglePickerControl.ViewModel"
        mc:Ignorable="d"
        Title="Angle Picker Control" Height="450" Width="800"
        SizeToContent="WidthAndHeight"
        d:DataContext="{d:DesignInstance vm:MainVM}">

    <Window.Resources>
        <Style TargetType="Button" x:Key="AngleButtonStyle">
            <Setter Property="Margin" Value="5,20"></Setter>
            <Setter Property="Width" Value="40"></Setter>
            <Setter Property="Height" Value="40
                    "></Setter>
        </Style>
    </Window.Resources>

    <Grid Name="TopContainer" Margin="50,10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

        <controls:AnglePicker Angle="{Binding Angle, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" x:Name="MainAnglePicker" Grid.Row="0"
                              Width="200"
                              Height="200"
                              BackgroundInner="Beige"
                              StrokeCircle="Red"
                              StrokeAngleLine="Blue"
                              OnAngleChanged="MainAnglePicker_OnOnAngleChanged"></controls:AnglePicker>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"></ColumnDefinition>
                <ColumnDefinition Width="1*"></ColumnDefinition>
                <ColumnDefinition Width="1*"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <Button Name="LowerAngleButton" Grid.Column="0" Style="{StaticResource AngleButtonStyle}" HorizontalAlignment="Right" Command="{Binding LowerAngleCommand}">
                <Viewbox>
                    <Path Fill="Black" Data="M 8 0 L 8 12 L 0 6 Z" Margin="0,1"></Path>
                </Viewbox>
            </Button>

            <TextBlock Text="{Binding Angle, StringFormat={}{0:#.#} deg}" Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,10"
                       Width="100" TextAlignment="Center" FontSize="20"></TextBlock>

            <Button Name="RiseAngleButton" Grid.Column="2" Style="{StaticResource AngleButtonStyle}" HorizontalAlignment="Left" Command="{Binding RiseAngleCommand}">
                <Viewbox>
                    <Path Fill="Black" Data="M 0 0 L 8 6 L 0 12 Z" Margin="0,1"></Path>
                </Viewbox>
            </Button>
        </Grid>
    </Grid>
</Window>

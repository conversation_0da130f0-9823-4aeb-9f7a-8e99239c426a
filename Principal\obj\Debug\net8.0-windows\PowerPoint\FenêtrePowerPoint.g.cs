﻿#pragma checksum "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F819A5C6E92C59E2BFE0A3EE1D68B11558555BCB"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using DirectX;
using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Principal {
    
    
    /// <summary>
    /// FenêtrePowerPoint
    /// </summary>
    public partial class FenêtrePowerPoint : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 29 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TextBoxFichier;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonChoisirFichier;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ModernWpf.Controls.NumberBox NumberBoxDiapositive;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border bb;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DirectX.DirectXControl LeContrôle;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockErreurs;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonImporter;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonAnnuler;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Principal;component/powerpoint/fen%c3%aatrepowerpoint.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TextBoxFichier = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.ButtonChoisirFichier = ((System.Windows.Controls.Button)(target));
            
            #line 30 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
            this.ButtonChoisirFichier.Click += new System.Windows.RoutedEventHandler(this.ButtonChoisirFichier_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.NumberBoxDiapositive = ((ModernWpf.Controls.NumberBox)(target));
            return;
            case 4:
            this.bb = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.LeContrôle = ((DirectX.DirectXControl)(target));
            return;
            case 6:
            this.TextBlockErreurs = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ButtonImporter = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
            this.ButtonImporter.Click += new System.Windows.RoutedEventHandler(this.ButtonImporter_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ButtonAnnuler = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\..\PowerPoint\FenêtrePowerPoint.xaml"
            this.ButtonAnnuler.Click += new System.Windows.RoutedEventHandler(this.ButtonAnnuler_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}


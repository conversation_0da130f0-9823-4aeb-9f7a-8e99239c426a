{"format": 1, "restore": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj": {}}, "projects": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj", "projectName": "AndroidWebCamClient", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\AndroidWebCamClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\AndroidWebCamClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Ceras": {"target": "Package", "version": "[4.1.7, )"}, "Microsoft.VisualStudio.Threading": {"target": "Package", "version": "[17.10.48, )"}, "PrecisionTimer.NET": {"target": "Package", "version": "[2.4.0.4, )"}, "Vanara.PInvoke.Kernel32": {"target": "Package", "version": "[4.0.3, )"}, "Vanara.PInvoke.User32": {"target": "Package", "version": "[4.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.304/PortableRuntimeIdentifierGraph.json"}}}}}
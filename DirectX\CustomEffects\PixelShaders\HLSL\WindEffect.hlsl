#include "Header1.hlsli"
cbuffer constants : register(b0)
{
    CONSTANTBUFFER_STANDARD_FIELDS
    
    float size;
}

#include "Header2.hlsli"

float rand(float2 co)
{
    return fract(sin(dot(co.xy, float2(12.9898, 78.233))) * 43758.5453);
}

float4 effet(float2 uv)
{
    float r = rand(float2(0, uv.y));
    float m = smoothstep(0.0, -size, uv.x * (1.0 - size) + size * r - (progress * (1.0 + size)));
    return mix(
    getFromColor(),
    getToColor(),
    m
  );
}

endCode

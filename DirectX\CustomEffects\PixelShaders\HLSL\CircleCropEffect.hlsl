#include "Header1.hlsli"
cbuffer constants : register(b0)
{
	CONSTANTBUFFER_STANDARD_FIELDS
	float2 dummy; // Next float4 must be aligned on a 16-byte boundary
	float4 bgcolor;
}

#define COMPLEXINPUTS
#include "Header2.hlsli"

float4 effet(float2 p)
{
	float2 ratio2 = float2(1.0, 1.0 / ratio);
	float s = pow(2.0 * abs(progress - 0.5), 3.0);

	float dist = length((float2(p) - 0.5) * ratio2);
	return mix(
		progress < 0.5 ? getFromColor(p) : getToColor(p), // branching is ok here as we statically depend on progress uniform (branching won't change over pixels)
		bgcolor,
		step(s, dist)
	);
}

endCode
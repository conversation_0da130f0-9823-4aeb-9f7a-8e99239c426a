{"version": 2, "dgSpecHash": "g2XEvQgbmY4=", "success": false, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "expectedPackageFiles": ["F:\\NugetPackages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.systemevents\\5.0.0\\microsoft.win32.systemevents.5.0.0.nupkg.sha512", "F:\\NugetPackages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "F:\\NugetPackages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "F:\\NugetPackages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "F:\\NugetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512"], "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4.runtime.win", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4.runtime.win", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'System.Drawing.Common' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/system.drawing.common/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'system.drawing.common'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FB1-DD9F-47F3-95B1-0A83CD8EB88B)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "System.Drawing.Common", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'MediaFoundation.NetCore' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/mediafoundation.netcore/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'mediafoundation.netcore'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E70E-F8B5-46A7-87CA-00ED1FF5560D)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "MediaFoundation.NetCore", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.Windows' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.windows/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.windows'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CC862-1DD4-486C-B451-DD20F799B176)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4.Windows", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.Windows' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.windows/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.windows'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CC862-1DD4-486C-B451-DD20F799B176)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4.Windows", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.WpfExtensions' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.wpfextensions/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.wpfextensions'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 0D40DFF6-5D2E-4988-8BA8-0D14AA458F18)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4.WpfExtensions", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.WpfExtensions' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.wpfextensions/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.wpfextensions'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 0D40DFF6-5D2E-4988-8BA8-0D14AA458F18)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCpp\\TestCpp.csproj", "libraryId": "OpenCvSharp4.WpfExtensions", "targetGraphs": []}]}
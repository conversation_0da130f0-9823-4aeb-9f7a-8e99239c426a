{"format": 1, "restore": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj": {}}, "projects": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj", "projectName": "DirectX", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"ComputeSharp": {"target": "Package", "version": "[3.2.0, )"}, "ComputeSharp.D2D1": {"target": "Package", "version": "[3.2.0, )"}, "ComputeSharp.Dxc": {"target": "Package", "version": "[3.2.0, )"}, "ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "HelixToolkit": {"target": "Package", "version": "[2.27.0, )"}, "HelixToolkit.SharpDX.Assimp": {"target": "Package", "version": "[2.27.0, )"}, "HelixToolkit.SharpDX.Core": {"target": "Package", "version": "[2.27.0, )"}, "HelixToolkit.SharpDX.Core.Wpf": {"target": "Package", "version": "[2.27.0, )"}, "MediaFoundation.NetCore": {"target": "Package", "version": "[2024.5.10, )"}, "Microsoft.VisualStudio.Threading": {"target": "Package", "version": "[17.13.61, )"}, "PrecisionTimer.NET": {"target": "Package", "version": "[2.4.0.4, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Text.RegularExpressions": {"target": "Package", "version": "[4.3.1, )"}, "Vanara.PInvoke.Kernel32": {"target": "Package", "version": "[4.1.2, )"}, "Vanara.PInvoke.Ole": {"target": "Package", "version": "[4.1.2, )"}, "Vanara.PInvoke.User32": {"target": "Package", "version": "[4.1.2, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}, "Vortice.XAudio2": {"target": "Package", "version": "[3.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj": {"version": "1.0.4-beta", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj", "projectName": "Afx", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SharpGen.Runtime": {"target": "Package", "version": "[2.1.2-beta, )", "autoReferenced": true}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.1.2-beta, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\AR\\Ar.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\AR\\Ar.csproj", "projectName": "Ar", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\AR\\Ar.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\AR\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Common\\Common.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Common\\Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SharpGen.Runtime": {"target": "Package", "version": "[2.1.2-beta, )", "autoReferenced": true}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.1.2-beta, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Common\\Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Common\\Common.csproj", "projectName": "Common", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Common\\Common.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SharpGen.Runtime": {"target": "Package", "version": "[2.1.2-beta, )", "autoReferenced": true}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.1.2-beta, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\Image.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\Image.csproj", "projectName": "Image", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\Image.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Vfx\\Vfx.csproj": {"version": "1.0.4-beta", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Vfx\\Vfx.csproj", "projectName": "Vfx", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Vfx\\Vfx.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Vfx\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\Image.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\Image.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj", "projectName": "SharpSvg", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Ceras": {"target": "Package", "version": "[4.1.7, )"}, "ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "ExCSS": {"target": "Package", "version": "[4.3.0, )"}, "Fizzler": {"target": "Package", "version": "[1.3.1, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "Svg": {"target": "Package", "version": "[3.4.7, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "projectName": "TestSuppressionFondMaxime", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\TestSuppressionFondMaxime.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondMaxime\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\AR\\Ar.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\AR\\Ar.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Vfx\\Vfx.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Vfx\\Vfx.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Ceras": {"target": "Package", "version": "[4.1.7, )"}, "ComputeSharp": {"target": "Package", "version": "[3.1.0, )"}, "ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "GeometRi": {"target": "Package", "version": "[1.4.1.1, )"}, "HelixToolkit": {"target": "Package", "version": "[2.26.0, )"}, "HelixToolkit.Core.Wpf": {"target": "Package", "version": "[2.26.0, )"}, "HelixToolkit.SharpDX.Core.Wpf": {"target": "Package", "version": "[2.26.0, )"}, "MediaFoundation.NetCore": {"target": "Package", "version": "[2024.5.10, )"}, "NetOctree": {"target": "Package", "version": "[2.1.0, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "Supercluster.KDTree.Standard": {"target": "Package", "version": "[1.0.5, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj", "projectName": "UserControls", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpSvg\\SharpSvg.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AvalonEdit": {"target": "Package", "version": "[6.3.1.120, )"}, "ModernWpfUI": {"target": "Package", "version": "[0.9.6, )"}, "PropertyTools.Wpf": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj", "projectName": "Utils", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Ceras": {"target": "Package", "version": "[4.1.7, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}
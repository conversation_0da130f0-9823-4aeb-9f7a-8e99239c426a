{"version": 3, "targets": {"net6.0-windows7.0": {"System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}}}, "libraries": {"System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["OpenCvSharp4 >= 4.10.0.20241108", "OpenCvSharp4.Windows >= 4.10.0.20241108", "OpenCvSharp4.WpfExtensions >= 4.10.0.20241108"]}, "packageFolders": {"F:\\NugetPackages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\TestCppWPF.csproj", "projectName": "TestCppWPF", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\TestCppWPF.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestCppWPF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"OpenCvSharp4": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4.Windows": {"target": "Package", "version": "[4.10.0.20241108, )"}, "OpenCvSharp4.WpfExtensions": {"target": "Package", "version": "[4.10.0.20241108, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "libraryId": "OpenCvSharp4.runtime.win"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "libraryId": "OpenCvSharp4.runtime.win"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'System.Drawing.Common' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/system.drawing.common/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'system.drawing.common'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FB1-DD9F-47F3-95B1-0A83CD8EB88B)).", "libraryId": "System.Drawing.Common"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.WpfExtensions' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.wpfextensions/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.wpfextensions'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 0D40DFF6-5D2E-4988-8BA8-0D14AA458F18)).", "libraryId": "OpenCvSharp4.WpfExtensions"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.WpfExtensions' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.wpfextensions/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.wpfextensions'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 0D40DFF6-5D2E-4988-8BA8-0D14AA458F18)).", "libraryId": "OpenCvSharp4.WpfExtensions"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.Windows' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.windows/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.windows'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CC862-1DD4-486C-B451-DD20F799B176)).", "libraryId": "OpenCvSharp4.Windows"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.Windows' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.windows/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.windows'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CC862-1DD4-486C-B451-DD20F799B176)).", "libraryId": "OpenCvSharp4.Windows"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "OpenCvSharp4"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "OpenCvSharp4"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Microsoft.Win32.SystemEvents' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/microsoft.win32.systemevents/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'microsoft.win32.systemevents'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 06A3E9A0-3E28-4624-A411-7DADCF5A112F)).", "libraryId": "Microsoft.Win32.SystemEvents"}]}
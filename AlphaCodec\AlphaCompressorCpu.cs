﻿#if false

using ComputeSharp;
using DirectX;
using IronCompress;
using SharpDX.DXGI;
using System;
using System.Buffers;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using Vortice;
using Vortice.Direct3D;
using Vortice.Direct3D11;
using Vortice.DXGI;
using Vortice.Mathematics;
using static AlphaCodec.Program;
using static Vanara.PInvoke.Kernel32;
using static Vanara.PInvoke.User32;

namespace AlphaCodec;

/// <summary>
/// ***************** Pas à jour!!!
/// </summary>
internal partial class AlphaCompressorCpu : IDisposable
{
    DirectXParameters _parameters;
    int _width, _height;
    ReadWriteBuffer<int> readWriteColumnHeaders; 
    ReadWriteBuffer<int> readWriteOutput1;
    ReadWriteBuffer<int> readWriteOutput2;

    internal unsafe AlphaCompressorCpu(DirectXParameters parameters, int width, int height)
    {
        _width = width; _height = height;
        _parameters = parameters;

        readWriteColumnHeaders = _parameters.GraphicsDevice.AllocateReadWriteBuffer<int>(width);
        readWriteOutput1 = _parameters.GraphicsDevice.AllocateReadWriteBuffer<int>(width * height);
        readWriteOutput2 = _parameters.GraphicsDevice.AllocateReadWriteBuffer<int>(width * height);

    }
    internal unsafe AlphaData Compress(ReadWriteBuffer<float> input)
    {
        Debug.Assert(input.Length == _width * _height);
        var device = _parameters.D3D11Device1;
        var context = _parameters.D3D11DeviceContext;

        //_parameters.GraphicsDevice.For(_width, new CompressColumns(input, _height, readWriteColumnHeaders, readWriteOutput1));
        //_parameters.GraphicsDevice.For(1, new AddColumnHeaders(readWriteColumnHeaders, _width));
        //_parameters.GraphicsDevice.For(_width, new CompressLines(readWriteColumnHeaders, readWriteOutput1, readWriteOutput2));

        int[] columnHeaders = new int[_width];
        readWriteColumnHeaders.CopyTo(columnHeaders);
        int[] alpha = new int[_width * _height];
        readWriteOutput2.CopyTo(alpha);

        AlphaData alphaData = new();

        var iron = new Iron(ArrayPool<byte>.Shared);

        ReadOnlySpan<byte> byteSpan = MemoryMarshal.Cast<int, byte>(columnHeaders);
        using (var compressed = iron.Compress(Codec.Brotli, byteSpan))
        alphaData.compressedColumnHeaders = compressed.AsSpan().ToArray();

        byteSpan = MemoryMarshal.Cast<int, byte>(alpha);
        using (var compressed = iron.Compress(Codec.Brotli, byteSpan))
            alphaData.compressedAlpha = compressed.AsSpan().ToArray();

        return alphaData;
    }

    const int RepeatShort = 1, RepeatLong = 254;

    static void CompresserColonnes(float[] input, int width, int height, int[] columnHeaders, int[] output)
    {
        for (int x = 0; x < width; x++)
        //Parallel.For(0, width, (i) =>
        {
            int pos = 0;
            int count = 0;
            int value = 0;
            int previous = -1;
            for (int y = 0; y < height; y++)
            {
                int indexInput = y * width + x;
                value = (int)(input[indexInput] * 255);
                if (value == RepeatShort) value = 0;
                if (value == RepeatLong) value = 255;
                if (value == previous) count++;
                else
                {
                    if (count > 255)
                    {
                        output[pos++ * width + x] = RepeatLong;
                        output[pos++ * width + x] = count / 256;
                        output[pos++ * width + x] = count % 256;
                    }
                    else if (count > 2)
                    {
                        output[pos++ * width + x] = RepeatShort;
                        output[pos++ * width + x] = count;
                    }
                    else if (count <= 2)
                    {
                        for (int c = 0; c < count; c++) output[pos++ * width + x] = previous;
                    }
                    count = 0;
                    output[pos++ * width + x] = value;
                    previous = value;
                }
            }
            // NB: The end of the column is filled by the previous value...
            columnHeaders[x] = pos; // NB: Column height.
        } //);
    }

    static void AdditionnerCulumnHeaders(int[] columnHeaders, int width)
    {
        int indexCompressed = 0;
        for (int x = 0; x < width; x++)
        {
            indexCompressed += columnHeaders[x];
            columnHeaders[x] = indexCompressed;
        }
    }

    static void CompresserLignes(int[] columnHeaders, int[] input, int width, out int[] output)
    {
        var outputT = new int[input.Length];
        for (int x = 0; x < width; x++)
        {
            int startIndexCompressed = (x == 0 ? 0 : columnHeaders[x - 1]);
            int length = columnHeaders[x] - startIndexCompressed;
            for (int pos = 0; pos < length; pos++)
            {
                int indexInput = pos * width + x;
                int value = input[indexInput];
                outputT[startIndexCompressed + pos] = value;
            }
        }
        output = new int[columnHeaders[width - 1]];
        Buffer.BlockCopy(outputT, 0, output, 0, columnHeaders[width - 1] * sizeof(int));
    }

    static void Décompresser(int[] columnHeaders, int[] input, int width, int height, out float[] output)
    {
        output = new float[width * height];
        int indexCompressed;
        for (int x = 0; x < width; x++)
        {
            int y = 0;
            int previous = 0;
            indexCompressed = x == 0 ? 0 : columnHeaders[x - 1];
            while (indexCompressed < columnHeaders[x])
            {
                int value = input[indexCompressed++];
                int outputIndex = y * width + x;
                if (value == RepeatShort)
                {
                    int count = input[indexCompressed++];
                    for (int i = 0; i < count; i++) output[y++ * width + x] = previous;
                }
                else if (value == RepeatLong)
                {
                    int count = input[indexCompressed++] * 256 + input[indexCompressed++];
                    for (int i = 0; i < count; i++) output[y++ * width + x] = previous;
                }
                else output[y++ * width + x] = value;
                previous = value;
            }
            while (y < height)
            {
                output[y++ * width + x] = previous;
            }
        }
    }


    internal void Dispose()
    {
        readWriteColumnHeaders?.Dispose();
        readWriteOutput1?.Dispose();
        readWriteOutput2?.Dispose();
    }
}

#endif
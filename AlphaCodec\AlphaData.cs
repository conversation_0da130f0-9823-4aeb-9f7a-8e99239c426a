﻿using ComputeSharp;
using DirectX;
using Utils;
using Vortice.Direct3D11;

namespace AlphaCodec;
public class AlphaData
{
    static AlphaCompressor s_alphaCompressor;
    static AlphaDecompressor s_alphaDecompressor;

    public byte[] CompressedColumnHeaders { get; set; }
    public uint WidthAlpha { get; set; }
    public uint HeightAlpha { get; set; }
    public uint AlphaLength { get; set; } // NB: This is not WidthAlpha * HeightAlpha, as not all this space is needed!
    public byte[] CompressedAlpha { get; set; }
    static AlphaData()
    {
        RessourcesStatiquesLibérables.Ajouter(LibérerRessourcesStatiques);
    }
    public AlphaData()
    {
    }

    public static AlphaData Create(DirectXParameters parameters, ReadWriteTexture2D<R8, float> texture)
    {
        if (s_alphaCompressor == null || s_alphaCompressor.Width != texture.Width || s_alphaCompressor.Height != texture.Height)
        {
            s_alphaCompressor?.Dispose();
            s_alphaCompressor = new(parameters, texture.Width, texture.Height);
        }
        return s_alphaCompressor.CompressFromReadWriteBuffer(texture);
    }
    public static AlphaData Create(DirectXParameters parameters, R8[] texture, int width, int height)
    {
        if (s_alphaCompressor == null || s_alphaCompressor.Width != width || s_alphaCompressor.Height != height)
        {
            s_alphaCompressor?.Dispose();
            s_alphaCompressor = new(parameters, width, height);
        }
        return s_alphaCompressor.CompressFromR8(texture);
    }
    /// <summary>
    /// Warning: The same texture is returned always, so be careful with multithread use!
    /// </summary>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public ID3D11Texture2D GetTexture(DirectXParameters parameters)
    {
        if (s_alphaDecompressor == null || s_alphaDecompressor.Width != WidthAlpha || s_alphaDecompressor.Height != HeightAlpha)
        {
            s_alphaDecompressor?.Dispose();
            s_alphaDecompressor = new(parameters, WidthAlpha, HeightAlpha);
        }
        return s_alphaDecompressor.DecompressToTexture(this);
    }
    public R8[] GetR8(DirectXParameters parameters)
    {
        if (s_alphaDecompressor == null || s_alphaDecompressor.Width != WidthAlpha || s_alphaDecompressor.Height != HeightAlpha)
        {
            s_alphaDecompressor?.Dispose();
            s_alphaDecompressor = new(parameters, WidthAlpha, HeightAlpha);
        }
        return s_alphaDecompressor.DecompressToR8(this);
    }
    public static void LibérerRessourcesStatiques()
    {
        s_alphaCompressor?.Dispose();
        s_alphaCompressor = null;
        s_alphaDecompressor?.Dispose();
        s_alphaDecompressor = null;
    }
}

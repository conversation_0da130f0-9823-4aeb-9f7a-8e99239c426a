{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"TestSuppressionFondConsole/1.0.0": {"dependencies": {"Ceras": "4.1.7", "ComputeSharp": "3.2.0", "ComputeSharp.D2D1": "3.2.0", "ComputeSharp.Dxc": "3.2.0", "ConcurrencyVisualizer": "3.0.0", "EditeurAudioVideoCpp": "1.0.0", "Quamotion.TurboJpegWrapper": "2.0.32", "SharpGen.Runtime.COM": "2.2.0-beta", "Utils": "1.0.0", "Vortice.D3DCompiler": "3.6.2", "Vortice.DXGI": "3.6.2", "Vortice.Direct2D1": "3.6.2", "Vortice.Direct3D11": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectML": "3.6.2", "Vortice.DirectX": "3.6.2", "Vortice.Dxc": "3.6.2", "Vortice.Mathematics": "1.9.3"}, "runtime": {"TestSuppressionFondConsole.dll": {}}}, "Ceras/4.1.7": {"dependencies": {"System.Buffers": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "runtime": {"lib/netstandard2.0/Ceras.dll": {"assemblyVersion": "4.1.7.0", "fileVersion": "4.1.7.0"}}}, "ComputeSharp/3.2.0": {"dependencies": {"ComputeSharp.Core": "3.2.0"}, "runtime": {"lib/net8.0/ComputeSharp.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ComputeSharp.Core/3.2.0": {"runtime": {"lib/net8.0/ComputeSharp.Core.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ComputeSharp.D2D1/3.2.0": {"dependencies": {"ComputeSharp.Core": "3.2.0"}, "runtime": {"lib/net8.0/ComputeSharp.D2D1.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ComputeSharp.Dxc/3.2.0": {"dependencies": {"ComputeSharp": "3.2.0"}, "runtime": {"lib/net8.0/ComputeSharp.Dxc.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "ConcurrencyVisualizer/3.0.0": {"dependencies": {"System.Security.Permissions": "4.7.0"}, "runtime": {"lib/.NETStandard20/Microsoft.ConcurrencyVisualizer.Markers.dll": {"assemblyVersion": "16.0.0.0", "fileVersion": "16.0.15122.3"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.SystemEvents/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Quamotion.TurboJpegWrapper/2.0.32": {"dependencies": {"Validation": "2.5.42"}, "runtime": {"lib/net5.0/Quamotion.TurboJpegWrapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.32.48778"}}, "runtimeTargets": {"runtimes/osx-x64/native/libturbojpeg.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/turbojpeg.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.2.0.0"}, "runtimes/win-x64/native/vcruntime140.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "14.0.24245.0"}, "runtimes/win-x86/native/turbojpeg.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.2.0.0"}, "runtimes/win-x86/native/vcruntime140.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "14.0.24245.0"}}}, "SharpGen.Runtime/2.2.0-beta": {"runtime": {"lib/net8.0/SharpGen.Runtime.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "SharpGen.Runtime.COM/2.2.0-beta": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta"}, "runtime": {"lib/net8.0/SharpGen.Runtime.COM.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "System.Buffers/4.5.0": {}, "System.Drawing.Common/9.0.4": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.4"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16312"}, "lib/net8.0/System.Private.Windows.Core.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16312"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "9.0.4"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Validation/2.5.42": {"dependencies": {"System.Runtime.InteropServices": "4.3.0"}, "runtime": {"lib/netstandard2.0/Validation.dll": {"assemblyVersion": "*******", "fileVersion": "2.5.42.18049"}}}, "Vortice.D3DCompiler/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.D3DCompiler.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Direct2D1/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.Direct2D1.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Direct3D11/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.Direct3D11.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Direct3D12/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.Direct3D12.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.DirectML/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.DirectML.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.DirectX/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "SharpGen.Runtime.COM": "2.2.0-beta", "Vortice.Mathematics": "1.9.3"}, "runtime": {"lib/net8.0/Vortice.DirectX.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Dxc/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "SharpGen.Runtime.COM": "2.2.0-beta", "Vortice.Dxc.Native": "1.0.2"}, "runtime": {"lib/net8.0/Vortice.Dxc.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Dxc.Native/1.0.2": {"runtimeTargets": {"runtimes/linux-x64/native/libdxcompiler.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libdxil.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/dxcompiler.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.8.2407.7"}, "runtimes/win-arm64/native/dxil.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "101.8.2407.12"}, "runtimes/win-x64/native/dxcompiler.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.8.2407.7"}, "runtimes/win-x64/native/dxil.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "101.8.2407.12"}}}, "Vortice.DXGI/3.6.2": {"dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "runtime": {"lib/net8.0/Vortice.DXGI.dll": {"assemblyVersion": "3.6.2.0", "fileVersion": "3.6.2.0"}}}, "Vortice.Mathematics/1.9.3": {"runtime": {"lib/net8.0/Vortice.Mathematics.dll": {"assemblyVersion": "1.9.3.0", "fileVersion": "1.9.3.0"}}}, "EditeurAudioVideoCpp/1.0.0": {"runtime": {"EditeurAudioVideoCpp.dll": {"assemblyVersion": "1.0.9386.29276", "fileVersion": "0.0.0.0"}}}, "Utils/1.0.0": {"dependencies": {"Ceras": "4.1.7", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "9.0.4"}, "runtime": {"Utils.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"TestSuppressionFondConsole/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Ceras/4.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-5hKNZAySyylTKELWfnLAFDG9A23Osp0Yqonpnmv2/MPqcVJjMC6/N/5EKJHYmMaw2xnLL8kiQaCe6Fu+tjNG/w==", "path": "ceras/4.1.7", "hashPath": "ceras.4.1.7.nupkg.sha512"}, "ComputeSharp/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaFaqJS2cp6pCmjXcqG4KxE8KAy+x7W9SLxF1UnvPr+V29nj0a+0dMp0qSVNu49ePhYNBvSv8ZzuHZr2ac23rA==", "path": "computesharp/3.2.0", "hashPath": "computesharp.3.2.0.nupkg.sha512"}, "ComputeSharp.Core/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ImIKT+x8IpWG+i/D8bIBtxeZ7FwlzcpKtoOgLzrwgJu0XopoVLsh3Xu6uzgZQIgtegFZK15PYqpgZKhMu2UTcg==", "path": "computesharp.core/3.2.0", "hashPath": "computesharp.core.3.2.0.nupkg.sha512"}, "ComputeSharp.D2D1/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-wvBSCHuJ6OXC5F1FsrlxAnUeOTyULR0+bidV+Elouzw9u7drPjfrfqu5jVvjiubaa4GjW1aPi28iD37vIfijmA==", "path": "computesharp.d2d1/3.2.0", "hashPath": "computesharp.d2d1.3.2.0.nupkg.sha512"}, "ComputeSharp.Dxc/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQ06P4B4crPQIJ/JBWxLPtUIAar34Y5sMXpjfPx5aVge4wto/4lL1tLS7tqqBFx4GhgQrloCsQO3XJVnIdpOrQ==", "path": "computesharp.dxc/3.2.0", "hashPath": "computesharp.dxc.3.2.0.nupkg.sha512"}, "ConcurrencyVisualizer/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3pCe8zt7mLtBiFJbyNA6XeU6jT10l683WkPipaNj1ktjEXur3HSF8/w3P8dyeImxz+EFgNHrEzY+58Xqj1M8cA==", "path": "concurrencyvisualizer/3.0.0", "hashPath": "concurrencyvisualizer.3.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-kHgtAkXhNEP8oGuAVe3Q5admxsdMlSdWE2rXcA9FfeGDZJQawPccmZgnOswgW3ugUPSJt7VH+TMQPz65mnhGSQ==", "path": "microsoft.win32.systemevents/9.0.4", "hashPath": "microsoft.win32.systemevents.9.0.4.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Quamotion.TurboJpegWrapper/2.0.32": {"type": "package", "serviceable": true, "sha512": "sha512-tVRoNDchXwyHdHJv5tiB70I8OzJXb+heNseOsFc9cgBTDF/pbcD+Tsefu6PJPVUsMJ8NowKB0Vw5CRbni8t/zw==", "path": "quamotion.turbojpegwrapper/2.0.32", "hashPath": "quamotion.turbojpegwrapper.2.0.32.nupkg.sha512"}, "SharpGen.Runtime/2.2.0-beta": {"type": "package", "serviceable": true, "sha512": "sha512-pqf/lAf4jy1iWqkm37JmhoQhBMPVudI/F9qp2zVvzjWAPeSggRIuxGMVEZQ4UQiqtJ1Rf/+j3MVAONGYyCEDzQ==", "path": "sharpgen.runtime/2.2.0-beta", "hashPath": "sharpgen.runtime.2.2.0-beta.nupkg.sha512"}, "SharpGen.Runtime.COM/2.2.0-beta": {"type": "package", "serviceable": true, "sha512": "sha512-4vsXC8ohyVslcUDVBoVXLDkjKprqujh3GWy+DqqULjyZ3GCx7nwRAV5DdrXZxX70iEiKyI3TxW3Qhf/oOXeC1Q==", "path": "sharpgen.runtime.com/2.2.0-beta", "hashPath": "sharpgen.runtime.com.2.2.0-beta.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Drawing.Common/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SbtusMUT1bCxZ14904ZPo2GZyelze0rwUni9wXrp8KX9Zlsda8idqpxra1RBvOA85WM0wW+fCI4GLrlCTYiE6A==", "path": "system.drawing.common/9.0.4", "hashPath": "system.drawing.common.9.0.4.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}, "Validation/2.5.42": {"type": "package", "serviceable": true, "sha512": "sha512-lzd+CAUssDzD1A3R+iqqgzRkkNtLpNNsund1aUXZP/sKD2nxnex6nQ55C8uWKEQjNYKe8/LpBp0HLRNuBsxKQQ==", "path": "validation/2.5.42", "hashPath": "validation.2.5.42.nupkg.sha512"}, "Vortice.D3DCompiler/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-h5PSVwQpgUs5+4QP5bJEuVyEUd0liz7pVcSayZ5JRlT8veGiaosK3EdFwGqZ+vnj+dkjlyS7+sA+18w+hB9fcA==", "path": "vortice.d3dcompiler/3.6.2", "hashPath": "vortice.d3dcompiler.3.6.2.nupkg.sha512"}, "Vortice.Direct2D1/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-axuDhGNPkdLwSzlcUAWr/Ju6s2tvnVKxM4MSl9zYcPePiUOTsKF39oByBZSjXllkQY01WG2o4+p00TZz/dL57Q==", "path": "vortice.direct2d1/3.6.2", "hashPath": "vortice.direct2d1.3.6.2.nupkg.sha512"}, "Vortice.Direct3D11/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-+tXkwV4YVFgox3NjLGBOPmEHK8Jd/8ElI3X8aX7xq4JhsC3J9BqpUhpI+4LOKeCGN+XCORhyFq0ow+pB44YQTg==", "path": "vortice.direct3d11/3.6.2", "hashPath": "vortice.direct3d11.3.6.2.nupkg.sha512"}, "Vortice.Direct3D12/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-fr9VpWkTfH1Jio5jvP0WBGcoZo+2hy2DUVHTYQMLL95KsCTc9PsG0rdtToolaQvorKFbF6hY2npZRiTy5wnxMA==", "path": "vortice.direct3d12/3.6.2", "hashPath": "vortice.direct3d12.3.6.2.nupkg.sha512"}, "Vortice.DirectML/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-VKKEW2V4lbkVPcaV2xnuaHLmvIrHToshi89kNkA0htSc46suADMU2Jo6SFlgqzM/byWgRGmoalZDrS6hcMOHkQ==", "path": "vortice.directml/3.6.2", "hashPath": "vortice.directml.3.6.2.nupkg.sha512"}, "Vortice.DirectX/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-pa/97U5IHascS/UJzMWFSKIs3wxh36zc8JpI+fzi/JLQk4wfTZur0n/Y9Lcp8i7hsf613Vu/6n6IImWSc9wupw==", "path": "vortice.directx/3.6.2", "hashPath": "vortice.directx.3.6.2.nupkg.sha512"}, "Vortice.Dxc/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-266EVwYCj1FU9eDRRnq05Ovu/32/V3ipbStYkmnS0rvVMUPio0okJ9DhfswVoFPJi1OLjAoP5Y9icQ9vmy3nzQ==", "path": "vortice.dxc/3.6.2", "hashPath": "vortice.dxc.3.6.2.nupkg.sha512"}, "Vortice.Dxc.Native/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-8deBQXEgEhbHvo6eQJyzHVKNja+yndSug6oFkf83aaBRr4GqbrlutgwyVsoUEDGCamvc6rEPDagkGeySuf2XfA==", "path": "vortice.dxc.native/1.0.2", "hashPath": "vortice.dxc.native.1.0.2.nupkg.sha512"}, "Vortice.DXGI/3.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPnBb1x3+CEqq7L5SEejElD2tMjkdE+bqN4c/eSNHj9WEpRPvAO0QFSEN8d9LiRDt9i9geD8m7E4s8sLMgz2qQ==", "path": "vortice.dxgi/3.6.2", "hashPath": "vortice.dxgi.3.6.2.nupkg.sha512"}, "Vortice.Mathematics/1.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-VzXWjzM4F5pK3gP3m0wv9LqhRrcmQebf+lK/gIPLZ+TPXTptMal74iGkQbivIJm9OwdMZdGOuk1dvwc2iJHUGQ==", "path": "vortice.mathematics/1.9.3", "hashPath": "vortice.mathematics.1.9.3.nupkg.sha512"}, "EditeurAudioVideoCpp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Utils/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
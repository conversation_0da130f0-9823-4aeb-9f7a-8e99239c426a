﻿using System;
using System.Runtime.InteropServices;
using Vortice.Direct2D1;

namespace DirectX;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct ButterflyWaveScrawlerEffectParameters
{
    public int OutputWidth;
    public int OutputHeight;
    public int TextureFromTransparent;
    public int TextureToTransparent;
    public float Progress;
    public float Ratio;

    public float Amplitude;
    public float Waves;
    public float ColorSeparation;
}

public class ButterflyWaveScrawlerEffect : ComplexEffetEffectBase
{
    static readonly Guid s_Guid = Guid.NewGuid();
    public ButterflyWaveScrawlerEffect() : base(s_Guid) { }

    public ButterflyWaveScrawlerEffectParameters cBuffer;

    [CustomEffectProperty(PropertyType.IUnknown, 0)]
    public int OutputWidth { get { return cBuffer.OutputWidth; } set { cBuffer.OutputWidth = value; OutputWidthInternal = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 1)]
    public int OutputHeight { get { return cBuffer.OutputHeight; } set { cBuffer.OutputHeight = value; OutputHeightInternal = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 2)]
    public bool TextureFromTransparent { get { return cBuffer.TextureFromTransparent != 0; } set { cBuffer.TextureFromTransparent = value ? 1 : 0; } }

    [CustomEffectProperty(PropertyType.IUnknown, 3)]
    public bool TextureToTransparent { get { return cBuffer.TextureToTransparent != 0; } set { cBuffer.TextureToTransparent = value ? 1 : 0; } }

    [CustomEffectProperty(PropertyType.IUnknown, 4)]
    public float Progress { get { return cBuffer.Progress; } set { cBuffer.Progress = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 5)]
    public float Ratio { get { return cBuffer.Ratio; } set { cBuffer.Ratio = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 6)]
    public float Amplitude { get { return cBuffer.Amplitude; } set { cBuffer.Amplitude = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 7)]
    public float Waves { get { return cBuffer.Waves; } set { cBuffer.Waves = value; } }

    [CustomEffectProperty(PropertyType.IUnknown, 8)]
    public float ColorSeparation { get { return cBuffer.ColorSeparation; } set { cBuffer.ColorSeparation = value; } }

    public override void PrepareForRender(ChangeType changeType) //Passes the struct to Gpu. Without this, it would be useless.
    {
        dInfo?.SetPixelShaderConstantBuffer(cBuffer);
    }
}

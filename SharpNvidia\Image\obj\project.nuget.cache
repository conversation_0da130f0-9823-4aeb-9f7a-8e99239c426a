{"version": 2, "dgSpecHash": "9MNvqn5/l2Y=", "success": true, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Image\\Image.csproj", "expectedPackageFiles": ["F:\\NugetPackages\\concurrencyvisualizer\\3.0.0\\concurrencyvisualizer.3.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.systemevents\\4.7.0\\microsoft.win32.systemevents.4.7.0.nupkg.sha512", "F:\\NugetPackages\\quamotion.turbojpegwrapper\\2.0.32\\quamotion.turbojpegwrapper.2.0.32.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime\\2.2.0-beta\\sharpgen.runtime.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime.com\\2.2.0-beta\\sharpgen.runtime.com.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\system.drawing.common\\4.7.0\\system.drawing.common.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512", "F:\\NugetPackages\\validation\\2.5.42\\validation.2.5.42.nupkg.sha512", "F:\\NugetPackages\\vortice.d3dcompiler\\3.6.2\\vortice.d3dcompiler.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct2d1\\3.6.2\\vortice.direct2d1.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d11\\3.6.2\\vortice.direct3d11.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d12\\3.6.2\\vortice.direct3d12.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directml\\3.6.2\\vortice.directml.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directx\\3.6.2\\vortice.directx.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc\\3.6.2\\vortice.dxc.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc.native\\1.0.2\\vortice.dxc.native.1.0.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxgi\\3.6.2\\vortice.dxgi.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.mathematics\\1.9.3\\vortice.mathematics.1.9.3.nupkg.sha512"], "logs": []}
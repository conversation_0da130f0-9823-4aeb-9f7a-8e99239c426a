{"version": 2, "dgSpecHash": "fvQyIwQ4kmU=", "success": false, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "OpenCvSharp4.runtime.win", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4.runtime.win' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4.runtime.win/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4.runtime.win'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BE6-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "OpenCvSharp4.runtime.win", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Svg.Skia' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/svg.skia/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'svg.skia'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E780-F8B5-46A7-87CA-00ED1FF5560D)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "Svg.Skia", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Svg.Skia' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/svg.skia/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'svg.skia'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E780-F8B5-46A7-87CA-00ED1FF5560D)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "Svg.Skia", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'LiteDB' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/litedb/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'litedb'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 06A3E905-3E28-4624-A411-7DADCF5A112F)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "LiteDB", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'LiteDB' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/litedb/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'litedb'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 06A3E905-3E28-4624-A411-7DADCF5A112F)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "LiteDB", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Échec du téléchargement du package 'System.Text.Json.7.0.2' à partir de 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/system.text.json/7.0.2/system.text.json.7.0.2.nupkg'.\r\nResponse status code does not indicate success: 401 (Unauthorized - No local versions of package 'system.text.json'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F884E-FEB8-4D0B-BCE7-1D73B102BF80)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "targetGraphs": []}, {"code": "Undefined", "level": "Error", "message": "Le flux 'ORT-Nightly [https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json]' répertorie le package 'System.Text.Json.7.0.2', mais plusieurs tentatives de téléchargement du fichier nupkg ont échoué. Soit le flux n'est pas valide, soit les packages exigés ont été supprimés pendant l'opération actuelle. Vérifiez que le package existe dans le flux et réessayez.", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "OpenCvSharp4", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'OpenCvSharp4' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/opencvsharp4/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'opencvsharp4'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26828-28C1-4E5B-8B11-88510A1D79A7)).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Tests\\Tests.csproj", "libraryId": "OpenCvSharp4", "targetGraphs": []}]}
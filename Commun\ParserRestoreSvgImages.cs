﻿using Svg;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using Vortice.Direct2D1;

namespace Commun;
public class ParserRestoreSvgImages
{
    readonly TamponImagesBgra _tamponBitmaps;

    public ParserRestoreSvgImages(TamponImagesBgra tamponBitmaps)
    {
        _tamponBitmaps = tamponBitmaps;
    }
    private void ParseSVGElementToEav(ID2D1DeviceContext6 dc, SvgElement element, out List<int> imagesUtilisées, ref bool contientVidéos)
    {
        imagesUtilisées = new();
        if (element.GetType().Name == "SvgImage")
        {
            SvgImage image = (SvgImage)element;
            if (image.TryGetAttribute("href", out string hRef))
            {
                Uri uri = new(hRef);
                switch (uri.Scheme)
                {
                    case "image":
                        Debugger.Break();
                        string newHref = _tamponBitmaps.TrouverHref(dc, image.Href, out int key);
                        if (newHref != null)
                        {
                            imagesUtilisées.Add(key);
                            image.Href = newHref;
                        }
                        break;
                    case "video":
                        if (image.TryGetAttribute("data-video", out string videoName))
                        {
                            Debugger.Break();
                            image.CustomAttributes.Remove("data-video");
                            image.Href = $@"video:{videoName}";
                        }
                        contientVidéos = true;
                        break;
                    default:
                        break;
                }
            }
        }
        foreach (SvgElement child in element.Children)
        {
            bool contientVidéosT = false;
            ParseSVGElementToEav(dc, child, out imagesUtilisées, ref contientVidéosT);
            if (contientVidéosT) contientVidéos = true;
        }
    }
    /// <summary>
    /// Parses the images contained in the svg for Eav. The svg can come from a file or from an external software.
    /// </summary>
    /// <param name="sourceSVG"></param>
    public string ParseSVGDocument(ID2D1DeviceContext6 dc, string svgData, out List<int> imagesUtilisées, out bool contientVidéos)
    {
        using MemoryStream stream0 = new(Encoding.UTF8.GetBytes(svgData));
        SvgDocument svgDocument = SvgDocument.Open<SvgDocument>(stream0);
        imagesUtilisées = new();
        contientVidéos = false;
        foreach (SvgElement child in svgDocument.Children)
        {
            bool contientVidéosT = false;
            ParseSVGElementToEav(dc, child, out List<int> imagesUtiliséesT, ref contientVidéosT);
            if (contientVidéosT) contientVidéos = true;
        }
        return svgDocument.GetXML();
    }

}


{"version": 3, "targets": {"net8.0-windows7.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Ceras >= 4.1.7", "ComputeSharp >= 3.1.0", "ComputeSharp.D2D1 >= 3.1.0", "ComputeSharp.Dxc >= 3.1.0", "ConcurrencyVisualizer >= 3.0.0", "GeometRi >= 1.4.1.1", "HelixToolkit >= 2.26.0", "HelixToolkit.Core.Wpf >= 2.26.0", "HelixToolkit.SharpDX.Core.Wpf >= 2.26.0", "MediaFoundation.NetCore >= 2024.5.10", "Microsoft.ML.OnnxRuntime.DirectML >= 1.18.1", "NetOctree >= 2.1.0", "Quamotion.TurboJpegWrapper >= 2.0.32", "SharpGen.Runtime.COM >= 2.2.0-beta", "Supercluster.KDTree.Standard >= 1.0.5", "System.Net.Http >= 4.3.4", "System.Text.RegularExpressions >= 4.3.1", "Vortice.D3DCompiler >= 3.6.2", "Vortice.DXGI >= 3.6.2", "Vortice.Direct2D1 >= 3.6.2", "Vortice.Direct3D11 >= 3.6.2", "Vortice.Direct3D12 >= 3.6.2", "Vortice.DirectML >= 3.6.2", "Vortice.DirectX >= 3.6.2", "Vortice.Dxc >= 3.6.2", "Vortice.Mathematics >= 1.9.3"]}, "packageFolders": {"F:\\NugetPackages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "projectName": "TestSuppressionFondTrimap", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\TestSuppressionFondTrimap.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondTrimap\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Ceras": {"target": "Package", "version": "[4.1.7, )"}, "ComputeSharp": {"target": "Package", "version": "[3.1.0, )"}, "ComputeSharp.D2D1": {"target": "Package", "version": "[3.1.0, )"}, "ComputeSharp.Dxc": {"target": "Package", "version": "[3.1.0, )"}, "ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "GeometRi": {"target": "Package", "version": "[1.4.1.1, )"}, "HelixToolkit": {"target": "Package", "version": "[2.26.0, )"}, "HelixToolkit.Core.Wpf": {"target": "Package", "version": "[2.26.0, )"}, "HelixToolkit.SharpDX.Core.Wpf": {"target": "Package", "version": "[2.26.0, )"}, "MediaFoundation.NetCore": {"target": "Package", "version": "[2024.5.10, )"}, "Microsoft.ML.OnnxRuntime.DirectML": {"target": "Package", "version": "[1.18.1, )"}, "NetOctree": {"target": "Package", "version": "[2.1.0, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "Supercluster.KDTree.Standard": {"target": "Package", "version": "[1.0.5, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Text.RegularExpressions": {"target": "Package", "version": "[4.3.1, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.Core.Wpf' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.core.wpf/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.core.wpf'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 3EF8E7A7-F8B5-46A7-87CA-00ED1FF5560D)).", "libraryId": "HelixToolkit.Core.Wpf"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 487F8BC2-FEB8-4D0B-BCE7-1D73B102BF80)).", "libraryId": "HelixToolkit"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'GeometRi' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/geometri/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'geometri'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 2A11D4EA-5B55-4083-AFA8-EF67C67AB17B)).", "libraryId": "GeometRi"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core.Wpf' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core.wpf/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core.wpf'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10F92-DD9F-47F3-95B1-0A83CD8EB88B)).", "libraryId": "HelixToolkit.SharpDX.Core.Wpf"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F2693F-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "ComputeSharp"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.Dxc' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.dxc/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.dxc'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1DE268D2-FFDB-4B00-99A7-EE1AAA4280FF)).", "libraryId": "ComputeSharp.Dxc"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.D2D1' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.d2d1/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.d2d1'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FD6-DD9F-47F3-95B1-0A83CD8EB88B)).", "libraryId": "ComputeSharp.D2D1"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CCBA5-1DD4-486C-B451-DD20F799B176)).", "libraryId": "HelixToolkit.SharpDX.Core"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'HelixToolkit.SharpDX.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/helixtoolkit.sharpdx.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'helixtoolkit.sharpdx.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 805CCBA5-1DD4-486C-B451-DD20F799B176)).", "libraryId": "HelixToolkit.SharpDX.Core"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.Core' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.core/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.core'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F25F5E-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "ComputeSharp.Core"}, {"code": "Undefined", "level": "Error", "message": "Échec du téléchargement du package 'Microsoft.AI.DirectML.1.14.1' à partir de 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/microsoft.ai.directml/1.14.1/microsoft.ai.directml.1.14.1.nupkg'.\r\nResponse status code does not indicate success: 401 (Unauthorized - No local versions of package 'microsoft.ai.directml'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F25E19-28C1-4E5B-8B11-88510A1D79A7))."}, {"code": "Undefined", "level": "Error", "message": "Le flux 'ORT-Nightly [https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json]' répertorie le package 'Microsoft.AI.DirectML.1.14.1', mais plusieurs tentatives de téléchargement du fichier nupkg ont échoué. Soit le flux n'est pas valide, soit les packages exigés ont été supprimés pendant l'opération actuelle. Vérifiez que le package existe dans le flux et réessayez."}]}
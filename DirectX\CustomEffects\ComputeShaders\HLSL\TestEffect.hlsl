Texture2D InputTexture : register(t0);
SamplerState InputSampler : register(s0);
RWTexture2D<float4> OutputTexture;
RWBuffer<float> g_RWBuffer : register(u5);

cbuffer systemConstants : register(b0)
{
    int4 resultRect;
    int2 outputOffset;
    float2 sceneToInput0X;
    float2 sceneToInput0Y;
};

[numthreads(16, 16, 1)]
void main(uint3 dispatchThreadId : SV_DispatchThreadID, uint3 groupThreadId : SV_GroupThreadID, uint3 groupId : SV_GroupID, uint groupIndex : SV_GroupIndex)
{
    float value = g_RWBuffer[dispatchThreadId.x];
    OutputTexture[dispatchThreadId.xy + outputOffset.xy + resultRect.xy] = float4(value, value, value, value); //magenta
}
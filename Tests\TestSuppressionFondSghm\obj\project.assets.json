{"version": 3, "targets": {"net8.0-windows7.0": {"AssimpNet/5.0.0-beta1": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.3/AssimpNet.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/AssimpNet.dll": {"related": ".xml"}}, "build": {"build/_._": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libassimp.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/libassimp.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/assimp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/assimp.dll": {"assetType": "native", "rid": "win-x86"}}}, "AvalonEdit/6.3.1.120": {"type": "package", "compile": {"lib/net8.0-windows7.0/ICSharpCode.AvalonEdit.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/ICSharpCode.AvalonEdit.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}, "Ceras/4.1.7": {"type": "package", "dependencies": {"System.Buffers": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/Ceras.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Ceras.dll": {"related": ".pdb;.xml"}}}, "ConcurrencyVisualizer/3.0.0": {"type": "package", "dependencies": {"System.Security.Permissions": "4.7.0"}, "compile": {"lib/.NETStandard20/Microsoft.ConcurrencyVisualizer.Markers.dll": {"related": ".xml"}}, "runtime": {"lib/.NETStandard20/Microsoft.ConcurrencyVisualizer.Markers.dll": {"related": ".xml"}}}, "Cyotek.Drawing.BitmapFont/2.0.0": {"type": "package", "compile": {"lib/netcoreapp3.1/Cyotek.Drawing.BitmapFont.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.1/Cyotek.Drawing.BitmapFont.dll": {"related": ".xml"}}}, "ExCSS/4.3.0": {"type": "package", "compile": {"lib/net8.0/ExCSS.dll": {}}, "runtime": {"lib/net8.0/ExCSS.dll": {}}}, "Fizzler/1.3.1": {"type": "package", "compile": {"lib/netstandard2.0/Fizzler.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Fizzler.dll": {"related": ".pdb"}}}, "HelixToolkit/2.27.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "compile": {"lib/netstandard2.0/HelixToolkit.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/HelixToolkit.dll": {"related": ".xml"}}}, "HelixToolkit.SharpDX.Assimp/2.27.0": {"type": "package", "dependencies": {"AssimpNet": "5.0.0-beta1", "HelixToolkit.SharpDX.Core": "[2.27.0]", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "SharpDX": "4.2.0", "SharpDX.Direct3D11": "4.2.0", "SharpDX.Mathematics": "4.2.0"}, "compile": {"lib/netstandard2.0/HelixToolkit.SharpDX.Core.Assimp.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/HelixToolkit.SharpDX.Core.Assimp.dll": {"related": ".deps.json;.xml"}}}, "HelixToolkit.SharpDX.Core/2.27.0": {"type": "package", "dependencies": {"Cyotek.Drawing.BitmapFont": "2.0.0", "HelixToolkit": "[2.27.0]", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "SharpDX": "4.2.0", "SharpDX.D3DCompiler": "4.2.0", "SharpDX.DXGI": "4.2.0", "SharpDX.Direct2D1": "4.2.0", "SharpDX.Direct3D11": "4.2.0", "SharpDX.Mathematics": "4.2.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Drawing.Primitives": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "compile": {"lib/netstandard2.0/HelixToolkit.SharpDX.Core.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/HelixToolkit.SharpDX.Core.dll": {"related": ".deps.json;.xml"}}}, "HelixToolkit.SharpDX.Core.Wpf/2.27.0": {"type": "package", "dependencies": {"Cyotek.Drawing.BitmapFont": "2.0.0", "HelixToolkit": "[2.27.0]", "HelixToolkit.SharpDX.Core": "[2.27.0]", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "SharpDX": "4.2.0", "SharpDX.D3DCompiler": "4.2.0", "SharpDX.DXGI": "4.2.0", "SharpDX.Direct2D1": "4.2.0", "SharpDX.Direct3D11": "4.2.0", "SharpDX.Direct3D9": "4.2.0", "SharpDX.Mathematics": "4.2.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Drawing.Primitives": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Runtime.Serialization.Xml": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "compile": {"lib/netcoreapp3.1/HelixToolkit.SharpDX.Core.Wpf.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netcoreapp3.1/HelixToolkit.SharpDX.Core.Wpf.dll": {"related": ".deps.json;.xml"}}}, "MediaFoundation.NetCore/2024.5.10": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "compile": {"lib/net8.0-windows7.0/MediaFoundation.dll": {}}, "runtime": {"lib/net8.0-windows7.0/MediaFoundation.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.3": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.VisualStudio.Threading/17.13.61": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Threading.Analyzers": "17.13.61", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8"}}, "Microsoft.VisualStudio.Threading.Analyzers/17.13.61": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"type": "package", "dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}, "compile": {"lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "resource": {"lib/net8.0-windows7.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net8.0-windows7.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net8.0-windows7.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net8.0-windows7.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net8.0-windows7.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net8.0-windows7.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net8.0-windows7.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net8.0-windows7.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net8.0-windows7.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net8.0-windows7.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net8.0-windows7.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net8.0-windows7.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0-windows7.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "compile": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"related": ".xml"}}, "resource": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/9.0.4": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "ModernWpfUI/0.9.6": {"type": "package", "compile": {"lib/net5.0-windows7.0/ModernWpf.Controls.dll": {"related": ".xml"}, "lib/net5.0-windows7.0/ModernWpf.dll": {"related": ".Controls.xml;.xml"}}, "runtime": {"lib/net5.0-windows7.0/ModernWpf.Controls.dll": {"related": ".xml"}, "lib/net5.0-windows7.0/ModernWpf.dll": {"related": ".Controls.xml;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"], "resource": {"lib/net5.0-windows7.0/af-ZA/ModernWpf.Controls.resources.dll": {"locale": "af-ZA"}, "lib/net5.0-windows7.0/af-ZA/ModernWpf.resources.dll": {"locale": "af-ZA"}, "lib/net5.0-windows7.0/am-ET/ModernWpf.Controls.resources.dll": {"locale": "am-ET"}, "lib/net5.0-windows7.0/am-ET/ModernWpf.resources.dll": {"locale": "am-ET"}, "lib/net5.0-windows7.0/ar-SA/ModernWpf.Controls.resources.dll": {"locale": "ar-SA"}, "lib/net5.0-windows7.0/ar-SA/ModernWpf.resources.dll": {"locale": "ar-SA"}, "lib/net5.0-windows7.0/az-Latn-AZ/ModernWpf.Controls.resources.dll": {"locale": "az-Latn-AZ"}, "lib/net5.0-windows7.0/az-Latn-AZ/ModernWpf.resources.dll": {"locale": "az-Latn-AZ"}, "lib/net5.0-windows7.0/be-BY/ModernWpf.Controls.resources.dll": {"locale": "be-BY"}, "lib/net5.0-windows7.0/be-BY/ModernWpf.resources.dll": {"locale": "be-BY"}, "lib/net5.0-windows7.0/bg-BG/ModernWpf.Controls.resources.dll": {"locale": "bg-BG"}, "lib/net5.0-windows7.0/bg-BG/ModernWpf.resources.dll": {"locale": "bg-BG"}, "lib/net5.0-windows7.0/bn-BD/ModernWpf.Controls.resources.dll": {"locale": "bn-BD"}, "lib/net5.0-windows7.0/bn-BD/ModernWpf.resources.dll": {"locale": "bn-BD"}, "lib/net5.0-windows7.0/bs-Latn-BA/ModernWpf.Controls.resources.dll": {"locale": "bs-Latn-BA"}, "lib/net5.0-windows7.0/bs-Latn-BA/ModernWpf.resources.dll": {"locale": "bs-Latn-BA"}, "lib/net5.0-windows7.0/ca-ES/ModernWpf.Controls.resources.dll": {"locale": "ca-ES"}, "lib/net5.0-windows7.0/ca-ES/ModernWpf.resources.dll": {"locale": "ca-ES"}, "lib/net5.0-windows7.0/cs-CZ/ModernWpf.Controls.resources.dll": {"locale": "cs-CZ"}, "lib/net5.0-windows7.0/cs-CZ/ModernWpf.resources.dll": {"locale": "cs-CZ"}, "lib/net5.0-windows7.0/da-DK/ModernWpf.Controls.resources.dll": {"locale": "da-DK"}, "lib/net5.0-windows7.0/da-DK/ModernWpf.resources.dll": {"locale": "da-DK"}, "lib/net5.0-windows7.0/de-DE/ModernWpf.Controls.resources.dll": {"locale": "de-DE"}, "lib/net5.0-windows7.0/de-DE/ModernWpf.resources.dll": {"locale": "de-DE"}, "lib/net5.0-windows7.0/el-GR/ModernWpf.Controls.resources.dll": {"locale": "el-GR"}, "lib/net5.0-windows7.0/el-GR/ModernWpf.resources.dll": {"locale": "el-GR"}, "lib/net5.0-windows7.0/en-GB/ModernWpf.Controls.resources.dll": {"locale": "en-GB"}, "lib/net5.0-windows7.0/en-GB/ModernWpf.resources.dll": {"locale": "en-GB"}, "lib/net5.0-windows7.0/es-ES/ModernWpf.Controls.resources.dll": {"locale": "es-ES"}, "lib/net5.0-windows7.0/es-ES/ModernWpf.resources.dll": {"locale": "es-ES"}, "lib/net5.0-windows7.0/es-MX/ModernWpf.Controls.resources.dll": {"locale": "es-MX"}, "lib/net5.0-windows7.0/es-MX/ModernWpf.resources.dll": {"locale": "es-MX"}, "lib/net5.0-windows7.0/et-EE/ModernWpf.Controls.resources.dll": {"locale": "et-EE"}, "lib/net5.0-windows7.0/et-EE/ModernWpf.resources.dll": {"locale": "et-EE"}, "lib/net5.0-windows7.0/eu-ES/ModernWpf.Controls.resources.dll": {"locale": "eu-ES"}, "lib/net5.0-windows7.0/eu-ES/ModernWpf.resources.dll": {"locale": "eu-ES"}, "lib/net5.0-windows7.0/fa-IR/ModernWpf.Controls.resources.dll": {"locale": "fa-IR"}, "lib/net5.0-windows7.0/fa-IR/ModernWpf.resources.dll": {"locale": "fa-IR"}, "lib/net5.0-windows7.0/fi-FI/ModernWpf.Controls.resources.dll": {"locale": "fi-FI"}, "lib/net5.0-windows7.0/fi-FI/ModernWpf.resources.dll": {"locale": "fi-FI"}, "lib/net5.0-windows7.0/fil-PH/ModernWpf.Controls.resources.dll": {"locale": "fil-PH"}, "lib/net5.0-windows7.0/fil-PH/ModernWpf.resources.dll": {"locale": "fil-PH"}, "lib/net5.0-windows7.0/fr-CA/ModernWpf.Controls.resources.dll": {"locale": "fr-CA"}, "lib/net5.0-windows7.0/fr-CA/ModernWpf.resources.dll": {"locale": "fr-CA"}, "lib/net5.0-windows7.0/fr-FR/ModernWpf.Controls.resources.dll": {"locale": "fr-FR"}, "lib/net5.0-windows7.0/fr-FR/ModernWpf.resources.dll": {"locale": "fr-FR"}, "lib/net5.0-windows7.0/gl-ES/ModernWpf.Controls.resources.dll": {"locale": "gl-ES"}, "lib/net5.0-windows7.0/gl-ES/ModernWpf.resources.dll": {"locale": "gl-ES"}, "lib/net5.0-windows7.0/he-IL/ModernWpf.Controls.resources.dll": {"locale": "he-IL"}, "lib/net5.0-windows7.0/he-IL/ModernWpf.resources.dll": {"locale": "he-IL"}, "lib/net5.0-windows7.0/hi-IN/ModernWpf.Controls.resources.dll": {"locale": "hi-IN"}, "lib/net5.0-windows7.0/hi-IN/ModernWpf.resources.dll": {"locale": "hi-IN"}, "lib/net5.0-windows7.0/hr-HR/ModernWpf.Controls.resources.dll": {"locale": "hr-HR"}, "lib/net5.0-windows7.0/hr-HR/ModernWpf.resources.dll": {"locale": "hr-HR"}, "lib/net5.0-windows7.0/hu-HU/ModernWpf.Controls.resources.dll": {"locale": "hu-HU"}, "lib/net5.0-windows7.0/hu-HU/ModernWpf.resources.dll": {"locale": "hu-HU"}, "lib/net5.0-windows7.0/id-ID/ModernWpf.Controls.resources.dll": {"locale": "id-ID"}, "lib/net5.0-windows7.0/id-ID/ModernWpf.resources.dll": {"locale": "id-ID"}, "lib/net5.0-windows7.0/is-IS/ModernWpf.Controls.resources.dll": {"locale": "is-IS"}, "lib/net5.0-windows7.0/is-IS/ModernWpf.resources.dll": {"locale": "is-IS"}, "lib/net5.0-windows7.0/it-IT/ModernWpf.Controls.resources.dll": {"locale": "it-IT"}, "lib/net5.0-windows7.0/it-IT/ModernWpf.resources.dll": {"locale": "it-IT"}, "lib/net5.0-windows7.0/ja-JP/ModernWpf.Controls.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net5.0-windows7.0/ja-JP/ModernWpf.resources.dll": {"locale": "ja-<PERSON>"}, "lib/net5.0-windows7.0/ka-GE/ModernWpf.Controls.resources.dll": {"locale": "ka-GE"}, "lib/net5.0-windows7.0/ka-GE/ModernWpf.resources.dll": {"locale": "ka-GE"}, "lib/net5.0-windows7.0/kk-KZ/ModernWpf.Controls.resources.dll": {"locale": "kk-KZ"}, "lib/net5.0-windows7.0/kk-KZ/ModernWpf.resources.dll": {"locale": "kk-KZ"}, "lib/net5.0-windows7.0/km-KH/ModernWpf.Controls.resources.dll": {"locale": "km-KH"}, "lib/net5.0-windows7.0/km-KH/ModernWpf.resources.dll": {"locale": "km-KH"}, "lib/net5.0-windows7.0/kn-IN/ModernWpf.Controls.resources.dll": {"locale": "kn-IN"}, "lib/net5.0-windows7.0/kn-IN/ModernWpf.resources.dll": {"locale": "kn-IN"}, "lib/net5.0-windows7.0/ko-KR/ModernWpf.Controls.resources.dll": {"locale": "ko-KR"}, "lib/net5.0-windows7.0/ko-KR/ModernWpf.resources.dll": {"locale": "ko-KR"}, "lib/net5.0-windows7.0/lo-LA/ModernWpf.Controls.resources.dll": {"locale": "lo-LA"}, "lib/net5.0-windows7.0/lo-LA/ModernWpf.resources.dll": {"locale": "lo-LA"}, "lib/net5.0-windows7.0/lt-LT/ModernWpf.Controls.resources.dll": {"locale": "lt-LT"}, "lib/net5.0-windows7.0/lt-LT/ModernWpf.resources.dll": {"locale": "lt-LT"}, "lib/net5.0-windows7.0/lv-LV/ModernWpf.Controls.resources.dll": {"locale": "lv-LV"}, "lib/net5.0-windows7.0/lv-LV/ModernWpf.resources.dll": {"locale": "lv-LV"}, "lib/net5.0-windows7.0/mk-MK/ModernWpf.Controls.resources.dll": {"locale": "mk-MK"}, "lib/net5.0-windows7.0/mk-MK/ModernWpf.resources.dll": {"locale": "mk-MK"}, "lib/net5.0-windows7.0/ml-IN/ModernWpf.Controls.resources.dll": {"locale": "ml-IN"}, "lib/net5.0-windows7.0/ml-IN/ModernWpf.resources.dll": {"locale": "ml-IN"}, "lib/net5.0-windows7.0/ms-MY/ModernWpf.Controls.resources.dll": {"locale": "ms-MY"}, "lib/net5.0-windows7.0/ms-MY/ModernWpf.resources.dll": {"locale": "ms-MY"}, "lib/net5.0-windows7.0/nb-NO/ModernWpf.Controls.resources.dll": {"locale": "nb-NO"}, "lib/net5.0-windows7.0/nb-NO/ModernWpf.resources.dll": {"locale": "nb-NO"}, "lib/net5.0-windows7.0/nl-NL/ModernWpf.Controls.resources.dll": {"locale": "nl-NL"}, "lib/net5.0-windows7.0/nl-NL/ModernWpf.resources.dll": {"locale": "nl-NL"}, "lib/net5.0-windows7.0/nn-NO/ModernWpf.Controls.resources.dll": {"locale": "nn-NO"}, "lib/net5.0-windows7.0/nn-NO/ModernWpf.resources.dll": {"locale": "nn-NO"}, "lib/net5.0-windows7.0/pl-PL/ModernWpf.Controls.resources.dll": {"locale": "pl-PL"}, "lib/net5.0-windows7.0/pl-PL/ModernWpf.resources.dll": {"locale": "pl-PL"}, "lib/net5.0-windows7.0/pt-BR/ModernWpf.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net5.0-windows7.0/pt-BR/ModernWpf.resources.dll": {"locale": "pt-BR"}, "lib/net5.0-windows7.0/pt-PT/ModernWpf.Controls.resources.dll": {"locale": "pt-PT"}, "lib/net5.0-windows7.0/pt-PT/ModernWpf.resources.dll": {"locale": "pt-PT"}, "lib/net5.0-windows7.0/ro-RO/ModernWpf.Controls.resources.dll": {"locale": "ro-RO"}, "lib/net5.0-windows7.0/ro-RO/ModernWpf.resources.dll": {"locale": "ro-RO"}, "lib/net5.0-windows7.0/ru-RU/ModernWpf.Controls.resources.dll": {"locale": "ru-RU"}, "lib/net5.0-windows7.0/ru-RU/ModernWpf.resources.dll": {"locale": "ru-RU"}, "lib/net5.0-windows7.0/sk-SK/ModernWpf.Controls.resources.dll": {"locale": "sk-SK"}, "lib/net5.0-windows7.0/sk-SK/ModernWpf.resources.dll": {"locale": "sk-SK"}, "lib/net5.0-windows7.0/sl-SI/ModernWpf.Controls.resources.dll": {"locale": "sl-SI"}, "lib/net5.0-windows7.0/sl-SI/ModernWpf.resources.dll": {"locale": "sl-SI"}, "lib/net5.0-windows7.0/sq-AL/ModernWpf.Controls.resources.dll": {"locale": "sq-AL"}, "lib/net5.0-windows7.0/sq-AL/ModernWpf.resources.dll": {"locale": "sq-AL"}, "lib/net5.0-windows7.0/sr-Latn-RS/ModernWpf.Controls.resources.dll": {"locale": "sr-Latn-RS"}, "lib/net5.0-windows7.0/sr-Latn-RS/ModernWpf.resources.dll": {"locale": "sr-Latn-RS"}, "lib/net5.0-windows7.0/sv-SE/ModernWpf.Controls.resources.dll": {"locale": "sv-SE"}, "lib/net5.0-windows7.0/sv-SE/ModernWpf.resources.dll": {"locale": "sv-SE"}, "lib/net5.0-windows7.0/sw-KE/ModernWpf.Controls.resources.dll": {"locale": "sw-KE"}, "lib/net5.0-windows7.0/sw-KE/ModernWpf.resources.dll": {"locale": "sw-KE"}, "lib/net5.0-windows7.0/ta-IN/ModernWpf.Controls.resources.dll": {"locale": "ta-IN"}, "lib/net5.0-windows7.0/ta-IN/ModernWpf.resources.dll": {"locale": "ta-IN"}, "lib/net5.0-windows7.0/te-IN/ModernWpf.Controls.resources.dll": {"locale": "te-IN"}, "lib/net5.0-windows7.0/te-IN/ModernWpf.resources.dll": {"locale": "te-IN"}, "lib/net5.0-windows7.0/th-TH/ModernWpf.Controls.resources.dll": {"locale": "th-TH"}, "lib/net5.0-windows7.0/th-TH/ModernWpf.resources.dll": {"locale": "th-TH"}, "lib/net5.0-windows7.0/tr-TR/ModernWpf.Controls.resources.dll": {"locale": "tr-TR"}, "lib/net5.0-windows7.0/tr-TR/ModernWpf.resources.dll": {"locale": "tr-TR"}, "lib/net5.0-windows7.0/uk-UA/ModernWpf.Controls.resources.dll": {"locale": "uk-UA"}, "lib/net5.0-windows7.0/uk-UA/ModernWpf.resources.dll": {"locale": "uk-UA"}, "lib/net5.0-windows7.0/uz-Latn-UZ/ModernWpf.Controls.resources.dll": {"locale": "uz-Latn-UZ"}, "lib/net5.0-windows7.0/uz-Latn-UZ/ModernWpf.resources.dll": {"locale": "uz-Latn-UZ"}, "lib/net5.0-windows7.0/vi-VN/ModernWpf.Controls.resources.dll": {"locale": "vi-VN"}, "lib/net5.0-windows7.0/vi-VN/ModernWpf.resources.dll": {"locale": "vi-VN"}}}, "NETStandard.Library/1.6.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "PrecisionTimer.NET/*******": {"type": "package", "compile": {"lib/netstandard2.0/PrecisionTimer.NET.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/PrecisionTimer.NET.dll": {"related": ".xml"}}}, "PropertyTools/3.1.0": {"type": "package", "compile": {"lib/netstandard2.0/PropertyTools.dll": {}}, "runtime": {"lib/netstandard2.0/PropertyTools.dll": {}}}, "PropertyTools.Wpf/3.1.0": {"type": "package", "dependencies": {"PropertyTools": "3.1.0", "System.ComponentModel.Annotations": "4.6.0"}, "compile": {"lib/netcoreapp3.0/PropertyTools.Wpf.dll": {}}, "runtime": {"lib/netcoreapp3.0/PropertyTools.Wpf.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Quamotion.TurboJpegWrapper/2.0.32": {"type": "package", "dependencies": {"Validation": "2.5.42"}, "compile": {"lib/net5.0/Quamotion.TurboJpegWrapper.dll": {}}, "runtime": {"lib/net5.0/Quamotion.TurboJpegWrapper.dll": {}}, "runtimeTargets": {"runtimes/osx-x64/native/libturbojpeg.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/turbojpeg.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/vcruntime140.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/turbojpeg.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/vcruntime140.dll": {"assetType": "native", "rid": "win-x86"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "debian.8-x64"}}}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.23-x64"}}}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "fedora.24-x64"}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.13.2-x64"}}}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "opensuse.42.1-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib": {"assetType": "native", "rid": "osx.10.10-x64"}}}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "rhel.7-x64"}}}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.14.04-x64"}}}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.04-x64"}}}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "runtimeTargets": {"runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so": {"assetType": "native", "rid": "ubuntu.16.10-x64"}}}, "SharpDX/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.1/SharpDX.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard1.1/SharpDX.dll": {"related": ".pdb"}}}, "SharpDX.D3DCompiler/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "compile": {"lib/netstandard1.1/SharpDX.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard1.1/SharpDX.D3DCompiler.dll": {"related": ".pdb;.xml"}}}, "SharpDX.Direct2D1/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0", "SharpDX.DXGI": "4.2.0"}, "compile": {"lib/netstandard1.1/SharpDX.Direct2D1.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard1.1/SharpDX.Direct2D1.dll": {"related": ".pdb;.xml"}}}, "SharpDX.Direct3D11/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0", "SharpDX.DXGI": "4.2.0"}, "compile": {"lib/netstandard1.1/SharpDX.Direct3D11.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard1.1/SharpDX.Direct3D11.dll": {"related": ".pdb;.xml"}}}, "SharpDX.Direct3D9/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "compile": {"lib/netstandard1.3/SharpDX.Direct3D9.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard1.3/SharpDX.Direct3D9.dll": {"related": ".pdb;.xml"}}}, "SharpDX.DXGI/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "compile": {"lib/netstandard1.1/SharpDX.DXGI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard1.1/SharpDX.DXGI.dll": {"related": ".pdb;.xml"}}}, "SharpDX.Mathematics/4.2.0": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1", "SharpDX": "4.2.0"}, "compile": {"lib/netstandard1.1/SharpDX.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard1.1/SharpDX.Mathematics.dll": {"related": ".pdb;.xml"}}}, "SharpGen.Runtime/2.2.0-beta": {"type": "package", "compile": {"lib/net8.0/SharpGen.Runtime.dll": {}}, "runtime": {"lib/net8.0/SharpGen.Runtime.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SharpGen.Runtime.COM/2.2.0-beta": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta"}, "compile": {"lib/net8.0/SharpGen.Runtime.COM.dll": {}}, "runtime": {"lib/net8.0/SharpGen.Runtime.COM.dll": {}}, "build": {"build/SharpGen.Runtime.COM.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/SharpGen.Runtime.COM.props": {}}}, "Svg/3.4.7": {"type": "package", "dependencies": {"ExCSS": "4.2.3", "System.Drawing.Common": "5.0.3"}, "compile": {"lib/net8.0/Svg.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Svg.dll": {"related": ".xml"}}}, "System.AppContext/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Buffers/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.3.0": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Annotations/4.6.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"related": ".xml"}}}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Console/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Console.dll": {"related": ".xml"}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"lib/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Tools.dll": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {"related": ".xml"}}}, "System.Drawing.Common/9.0.4": {"type": "package", "dependencies": {"Microsoft.Win32.SystemEvents": "9.0.4"}, "compile": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net8.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"related": ".pdb;.xml"}, "lib/net8.0/System.Private.Windows.Core.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Drawing.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Drawing.Primitives.dll": {}}, "runtime": {"lib/netstandard1.1/System.Drawing.Primitives.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.Globalization.Calendars/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Calendars.dll": {"related": ".xml"}}}, "System.Globalization.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "dependencies": {"System.Buffers": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.ZipFile.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.Compression.ZipFile.dll": {}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Net.Http/4.3.4": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}, "compile": {"ref/netstandard1.3/System.Net.Http.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Net.Http.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Net.Http.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {"related": ".xml"}}}, "System.Net.Sockets/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Sockets.dll": {"related": ".xml"}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Private.DataContractSerialization/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0", "System.Xml.XmlSerializer": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Private.DataContractSerialization.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Extensions.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {"related": ".xml"}}}, "System.Runtime/4.3.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.1", "Microsoft.NETCore.Targets": "1.1.3"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Numerics/4.3.0": {"type": "package", "dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Runtime.Numerics.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Runtime.Serialization.Xml/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Private.DataContractSerialization": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Xml.dll": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assetType": "runtime", "rid": "unix"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}, "compile": {"ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Permissions/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {"related": ".xml"}}}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "dependencies": {"System.Runtime": "4.3.1"}, "compile": {"ref/netcoreapp1.1/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"lib/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.1/System.Threading.Tasks.Parallel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.Parallel.dll": {}}}, "System.Threading.Timer/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.2/System.Threading.Timer.dll": {"related": ".xml"}}}, "System.Windows.Extensions/4.7.0": {"type": "package", "dependencies": {"System.Drawing.Common": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.ReaderWriter.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XmlDocument.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {}}}, "System.Xml.XmlSerializer/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Xml.XmlSerializer.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlSerializer.dll": {}}}, "Validation/2.5.42": {"type": "package", "dependencies": {"System.Runtime.InteropServices": "4.3.0"}, "compile": {"lib/netstandard2.0/Validation.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Validation.dll": {"related": ".xml"}}}, "Vanara.Core/4.1.2": {"type": "package", "compile": {"lib/net8.0-windows7.0/Vanara.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.Core.dll": {"related": ".xml"}}, "resource": {"lib/net8.0-windows7.0/fr/Vanara.Core.resources.dll": {"locale": "fr"}}}, "Vanara.PInvoke.Cryptography/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Shared": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Cryptography.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Gdi32/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Shared": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Kernel32/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Shared": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Ole/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Rpc": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Ole.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Ole.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Rpc/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Security": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Rpc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Rpc.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Security/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Cryptography": "4.1.2", "Vanara.PInvoke.Kernel32": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Security.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Security.dll": {"related": ".xml"}}}, "Vanara.PInvoke.Shared/4.1.2": {"type": "package", "dependencies": {"Vanara.Core": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.Shared.dll": {"related": ".xml"}}}, "Vanara.PInvoke.User32/4.1.2": {"type": "package", "dependencies": {"Vanara.PInvoke.Gdi32": "4.1.2", "Vanara.PInvoke.Kernel32": "4.1.2"}, "compile": {"lib/net8.0-windows7.0/Vanara.PInvoke.User32.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/Vanara.PInvoke.User32.dll": {"related": ".xml"}}}, "Vortice.D3DCompiler/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "compile": {"lib/net8.0/Vortice.D3DCompiler.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.D3DCompiler.dll": {"related": ".xml"}}, "build": {"build/Vortice.D3DCompiler.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.D3DCompiler.props": {}}}, "Vortice.Direct2D1/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "compile": {"lib/net8.0/Vortice.Direct2D1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.Direct2D1.dll": {"related": ".xml"}}, "build": {"build/Vortice.Direct2D1.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.Direct2D1.props": {}}}, "Vortice.Direct3D11/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "compile": {"lib/net8.0/Vortice.Direct3D11.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.Direct3D11.dll": {"related": ".xml"}}, "build": {"build/Vortice.Direct3D11.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.Direct3D11.props": {}}}, "Vortice.Direct3D12/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2"}, "compile": {"lib/net8.0/Vortice.Direct3D12.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.Direct3D12.dll": {"related": ".xml"}}, "build": {"build/Vortice.Direct3D12.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.Direct3D12.props": {}}}, "Vortice.DirectML/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DXGI": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectX": "3.6.2"}, "compile": {"lib/net8.0/Vortice.DirectML.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.DirectML.dll": {"related": ".xml"}}, "build": {"build/Vortice.DirectML.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.DirectML.props": {}}}, "Vortice.DirectX/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "SharpGen.Runtime.COM": "2.2.0-beta", "Vortice.Mathematics": "1.9.2"}, "compile": {"lib/net8.0/Vortice.DirectX.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.DirectX.dll": {"related": ".xml"}}, "build": {"build/Vortice.DirectX.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.DirectX.props": {}}}, "Vortice.Dxc/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "SharpGen.Runtime.COM": "2.2.0-beta", "Vortice.Dxc.Native": "1.0.2"}, "compile": {"lib/net8.0/Vortice.Dxc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.Dxc.dll": {"related": ".xml"}}, "build": {"build/Vortice.Dxc.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.Dxc.props": {}}}, "Vortice.Dxc.Native/1.0.2": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libdxcompiler.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x64/native/libdxil.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/win-arm64/native/dxcompiler.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/dxil.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/dxcompiler.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/dxil.dll": {"assetType": "native", "rid": "win-x64"}}}, "Vortice.DXGI/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "compile": {"lib/net8.0/Vortice.DXGI.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.DXGI.dll": {"related": ".xml"}}, "build": {"build/Vortice.DXGI.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/Vortice.DXGI.props": {}}}, "Vortice.Mathematics/1.9.3": {"type": "package", "compile": {"lib/net8.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}}, "Vortice.XAudio2/3.6.2": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.2.0-beta", "Vortice.DirectX": "3.6.2"}, "compile": {"lib/net8.0/Vortice.XAudio2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Vortice.XAudio2.dll": {"related": ".xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "DirectX/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"ComputeSharp": "3.2.0", "ComputeSharp.D2D1": "3.2.0", "ComputeSharp.Dxc": "3.2.0", "ConcurrencyVisualizer": "3.0.0", "HelixToolkit": "2.27.0", "HelixToolkit.SharpDX.Assimp": "2.27.0", "HelixToolkit.SharpDX.Core": "2.27.0", "HelixToolkit.SharpDX.Core.Wpf": "2.27.0", "MediaFoundation.NetCore": "2024.5.10", "Microsoft.VisualStudio.Threading": "17.13.61", "PrecisionTimer.NET": "*******", "Quamotion.TurboJpegWrapper": "2.0.32", "SharpGen.Runtime.COM": "2.2.0-beta", "System.Net.Http": "4.3.4", "System.Text.RegularExpressions": "4.3.1", "Utils": "1.0.0", "Vanara.PInvoke.Kernel32": "4.1.2", "Vanara.PInvoke.Ole": "4.1.2", "Vanara.PInvoke.User32": "4.1.2", "Vortice.D3DCompiler": "3.6.2", "Vortice.DXGI": "3.6.2", "Vortice.Direct2D1": "3.6.2", "Vortice.Direct3D11": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectML": "3.6.2", "Vortice.DirectX": "3.6.2", "Vortice.Dxc": "3.6.2", "Vortice.Mathematics": "1.9.3", "Vortice.XAudio2": "3.6.2"}, "compile": {"bin/placeholder/DirectX.dll": {}}, "runtime": {"bin/placeholder/DirectX.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "SharpSvg/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Ceras": "4.1.7", "ConcurrencyVisualizer": "3.0.0", "DirectX": "1.0.0", "ExCSS": "4.3.0", "Fizzler": "1.3.1", "Quamotion.TurboJpegWrapper": "2.0.32", "SharpGen.Runtime.COM": "2.2.0-beta", "Svg": "3.4.7", "Vortice.D3DCompiler": "3.6.2", "Vortice.DXGI": "3.6.2", "Vortice.Direct2D1": "3.6.2", "Vortice.Direct3D11": "3.6.2", "Vortice.Direct3D12": "3.6.2", "Vortice.DirectML": "3.6.2", "Vortice.DirectX": "3.6.2", "Vortice.Dxc": "3.6.2", "Vortice.Mathematics": "1.9.3"}, "compile": {"bin/placeholder/SharpSvg.dll": {}}, "runtime": {"bin/placeholder/SharpSvg.dll": {}}}, "UserControls/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"AvalonEdit": "6.3.1.120", "ModernWpfUI": "0.9.6", "PropertyTools.Wpf": "3.1.0", "SharpSvg": "1.0.0"}, "compile": {"bin/placeholder/UserControls.dll": {}}, "runtime": {"bin/placeholder/UserControls.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Utils/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Ceras": "4.1.7", "Newtonsoft.Json": "13.0.3", "System.Drawing.Common": "9.0.4"}, "compile": {"bin/placeholder/Utils.dll": {}}, "runtime": {"bin/placeholder/Utils.dll": {}}}}}, "libraries": {"AssimpNet/5.0.0-beta1": {"sha512": "oiUmn2KNaLThdbr6oT461uyCwclsqfZ0UB36vqkAUm8ttBWjKOCBmtp9V+X6xDGZmQaJe9YknW5eoyATTikUNQ==", "type": "package", "path": "assimpnet/5.0.0-beta1", "files": [".nupkg.metadata", ".signature.p7s", "AssimpNet Documentation (October 2018).chm", "ChangeLog.txt", "License.txt", "assimpnet.5.0.0-beta1.nupkg.sha512", "assimpnet.nuspec", "build/AssimpNet.targets", "lib/net35/AssimpNet.dll", "lib/net35/AssimpNet.xml", "lib/net40/AssimpNet.dll", "lib/net40/AssimpNet.xml", "lib/netstandard1.3/AssimpNet.dll", "lib/netstandard1.3/AssimpNet.xml", "logo.png", "runtimes/linux-x64/native/libassimp.so", "runtimes/osx-x64/native/libassimp.dylib", "runtimes/win-x64/native/assimp.dll", "runtimes/win-x86/native/assimp.dll"]}, "AvalonEdit/6.3.1.120": {"sha512": "6ASBKPzAg64BLDlFOJo4Ypr1sA/6hB5+TfHLJaZYUSTE0jgxvKlVqKeo/ISTG9qEMxu604qUu6BGugmJkfdrNw==", "type": "package", "path": "avalonedit/6.3.1.120", "files": [".nupkg.metadata", ".signature.p7s", "PackageReadme.md", "avalonedit.6.3.1.120.nupkg.sha512", "avalonedit.nuspec", "images/AvalonEditNuGetPackageIcon.png", "lib/net462/ICSharpCode.AvalonEdit.dll", "lib/net462/ICSharpCode.AvalonEdit.xml", "lib/net6.0-windows7.0/ICSharpCode.AvalonEdit.dll", "lib/net6.0-windows7.0/ICSharpCode.AvalonEdit.xml", "lib/net8.0-windows7.0/ICSharpCode.AvalonEdit.dll", "lib/net8.0-windows7.0/ICSharpCode.AvalonEdit.xml"]}, "Ceras/4.1.7": {"sha512": "5hKNZAySyylTKELWfnLAFDG9A23Osp0Yqonpnmv2/MPqcVJjMC6/N/5EKJHYmMaw2xnLL8kiQaCe6Fu+tjNG/w==", "type": "package", "path": "ceras/4.1.7", "files": [".nupkg.metadata", ".signature.p7s", "ceras.4.1.7.nupkg.sha512", "ceras.nuspec", "lib/net45/Ceras.dll", "lib/net45/Ceras.pdb", "lib/net45/Ceras.xml", "lib/net47/Ceras.dll", "lib/net47/Ceras.pdb", "lib/net47/Ceras.xml", "lib/netstandard2.0/Ceras.dll", "lib/netstandard2.0/Ceras.pdb", "lib/netstandard2.0/Ceras.xml"]}, "ConcurrencyVisualizer/3.0.0": {"sha512": "3pCe8zt7mLtBiFJbyNA6XeU6jT10l683WkPipaNj1ktjEXur3HSF8/w3P8dyeImxz+EFgNHrEzY+58Xqj1M8cA==", "type": "package", "path": "concurrencyvisualizer/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "concurrencyvisualizer.3.0.0.nupkg.sha512", "concurrencyvisualizer.nuspec", "lib/.NETStandard20/Microsoft.ConcurrencyVisualizer.Markers.dll", "lib/.NETStandard20/Microsoft.ConcurrencyVisualizer.Markers.xml", "lib/net45/Microsoft.ConcurrencyVisualizer.Markers.dll"]}, "Cyotek.Drawing.BitmapFont/2.0.0": {"sha512": "t6vYF0sXgNb6dP9QNbJxJWw3RdCEWC6Llv47oPYpobYAhmKcgmAZJv6HXK03DEUmo7ff6yPvsyEWTkia+UAONw==", "type": "package", "path": "cyotek.drawing.bitmapfont/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "cyotek.drawing.bitmapfont.2.0.0.nupkg.sha512", "cyotek.drawing.bitmapfont.nuspec", "icon.png", "lib/net35/Cyotek.Drawing.BitmapFont.dll", "lib/net35/Cyotek.Drawing.BitmapFont.xml", "lib/net40/Cyotek.Drawing.BitmapFont.dll", "lib/net40/Cyotek.Drawing.BitmapFont.xml", "lib/net452/Cyotek.Drawing.BitmapFont.dll", "lib/net452/Cyotek.Drawing.BitmapFont.xml", "lib/net462/Cyotek.Drawing.BitmapFont.dll", "lib/net462/Cyotek.Drawing.BitmapFont.xml", "lib/net472/Cyotek.Drawing.BitmapFont.dll", "lib/net472/Cyotek.Drawing.BitmapFont.xml", "lib/net48/Cyotek.Drawing.BitmapFont.dll", "lib/net48/Cyotek.Drawing.BitmapFont.xml", "lib/netcoreapp2.1/Cyotek.Drawing.BitmapFont.dll", "lib/netcoreapp2.1/Cyotek.Drawing.BitmapFont.xml", "lib/netcoreapp2.2/Cyotek.Drawing.BitmapFont.dll", "lib/netcoreapp2.2/Cyotek.Drawing.BitmapFont.xml", "lib/netcoreapp3.1/Cyotek.Drawing.BitmapFont.dll", "lib/netcoreapp3.1/Cyotek.Drawing.BitmapFont.xml", "lib/netstandard1.3/Cyotek.Drawing.BitmapFont.dll", "lib/netstandard1.3/Cyotek.Drawing.BitmapFont.xml", "lib/netstandard2.0/Cyotek.Drawing.BitmapFont.dll", "lib/netstandard2.0/Cyotek.Drawing.BitmapFont.xml", "lib/netstandard2.1/Cyotek.Drawing.BitmapFont.dll", "lib/netstandard2.1/Cyotek.Drawing.BitmapFont.xml"]}, "ExCSS/4.3.0": {"sha512": "LyVZ925Cl6Z4TJrbogt3+DkvanYWZcJs0tzKg8lWHAY9PgniVMluNy+l5vXDMmvt5gBgekULasEnUqVAavPuZw==", "type": "package", "path": "excss/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "excss.4.3.0.nupkg.sha512", "excss.nuspec", "lib/net48/ExCSS.dll", "lib/net6.0/ExCSS.dll", "lib/net7.0/ExCSS.dll", "lib/net8.0/ExCSS.dll", "lib/netcoreapp3.1/ExCSS.dll", "lib/netstandard2.0/ExCSS.dll", "lib/netstandard2.1/ExCSS.dll", "readme.md"]}, "Fizzler/1.3.1": {"sha512": "ujU39lID9SL1PjD2hyiaQJw8tObM2c+T/vlTNV0Xl9+rj/8a2rHr9G2j9e5dL8rhkekZ1Wx5AIEYoO/LW06dnA==", "type": "package", "path": "fizzler/1.3.1", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.txt", "fizzler.1.3.1.nupkg.sha512", "fizzler.nuspec", "lib/netstandard1.0/Fizzler.dll", "lib/netstandard1.0/Fizzler.pdb", "lib/netstandard2.0/Fizzler.dll", "lib/netstandard2.0/Fizzler.pdb"]}, "HelixToolkit/2.27.0": {"sha512": "sXq9gXWIA/QYlx1Po9ac36hGwPT8BkpzSpGO7RgpGIuCOdJGXRASA9mJa6GJeTLfs5BYPzVwjKmyghw5ivM0bQ==", "type": "package", "path": "helixtoolkit/2.27.0", "files": [".nupkg.metadata", ".signature.p7s", "AUTHORS", "CONTRIBUTORS", "LICENSE", "README.md", "helixtoolkit.2.27.0.nupkg.sha512", "helixtoolkit.nuspec", "helixtoolkit.png", "lib/netstandard2.0/HelixToolkit.dll", "lib/netstandard2.0/HelixToolkit.xml"]}, "HelixToolkit.SharpDX.Assimp/2.27.0": {"sha512": "Sybp+f2YxPnqA1SNsRaPhuGLK0DYAv1J7ZoHFJUABRt68v1+oT+GzP/MC6jjOAD84MmRv3hlHv43H+3x9w8/1A==", "type": "package", "path": "helixtoolkit.sharpdx.assimp/2.27.0", "files": [".nupkg.metadata", ".signature.p7s", "AUTHORS", "CONTRIBUTORS", "LICENSE", "README.md", "helixtoolkit.png", "helixtoolkit.sharpdx.assimp.2.27.0.nupkg.sha512", "helixtoolkit.sharpdx.assimp.nuspec", "lib/net46/HelixToolkit.Wpf.SharpDX.Assimp.dll", "lib/net46/HelixToolkit.Wpf.SharpDX.Assimp.xml", "lib/netstandard2.0/HelixToolkit.SharpDX.Core.Assimp.deps.json", "lib/netstandard2.0/HelixToolkit.SharpDX.Core.Assimp.dll", "lib/netstandard2.0/HelixToolkit.SharpDX.Core.Assimp.xml", "lib/uap10.0/HelixToolkit.UWP.Assimp.XML", "lib/uap10.0/HelixToolkit.UWP.Assimp.dll", "lib/uap10.0/HelixToolkit.UWP.Assimp.pri", "lib/uap10.0/HelixToolkit.UWP/HelixToolkit.UWP.xr.xml", "lib/uap10.0/HelixToolkit.UWP/Resources/arial.dds", "lib/uap10.0/HelixToolkit.UWP/Resources/arial.fnt", "lib/uap10.0/HelixToolkit.UWP/Resources/csParticleInsert.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/csParticleUpdate.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/dsMeshTriTessellation.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsBillboard.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsLine.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsLineArrowHead.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsLineArrowHeadTail.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsMeshNormalVector.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsMeshSkinnedOut.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsParticle.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/gsPoint.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/hsMeshTriTessellation.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psBillboardText.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psBillboardTextOIT.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psBillboardTextOITDP.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psColor.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psDepthStencilOnly.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psDiffuseMap.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectBloomBlurHorizontal.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectBloomBlurVertical.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectBloomCombine.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectBloomExtract.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectGaussianBlurHorizontal.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectGaussianBlurVertical.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectMeshBorderHighlight.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectMeshDiffuseXRayGrid.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectMeshXRay.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectMeshXRayGrid.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectOutlineQuad.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectOutlineQuadStencil.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectOutlineQualFinal.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psEffectOutlineSmooth.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psFXAA.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psLUMA.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psLine.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psLineColor.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshBlinnPhong.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshBlinnPhongOIT.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshBlinnPhongOITDP.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshBlinnPhongOITQuad.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshClipPlaneBackface.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshClipPlaneQuad.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshColorStripe.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshDiffuseMapOIT.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshDiffuseMapOITDP.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshOITDPFinal.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshOITDPFirst.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshPBR.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshPBROIT.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshPBROITDP.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psMeshXRay.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psNormals.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psParticle.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psParticleOIT.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psParticleOITDP.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psPlaneGrid.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psPoint.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psPositions.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psSSAO.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psSSAOBlur.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psSSAOP1.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psScreenDup.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psShadow.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psSkybox.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psSprite.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psViewCube.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psVolume.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psVolumeCube.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psVolumeDiffuse.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psWireframe.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psWireframeOIT.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/psWireframeOITDP.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsBillboard.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsBillboardInstancing.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsBoneSkinningBasic.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsCubeMap.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshBatched.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshBatchedSSAO.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshBatchedShadow.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshBatchedWireframe.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshClipPlane.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshClipPlaneQuad.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshDefault.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshDepth.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshInstancing.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshInstancingTessellation.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshOutlinePass1.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshOutlineScreenQuad.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshSSAO.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshShadow.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshTessellation.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsMeshWireframe.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsParticle.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsPlaneGrid.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsPoint.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsPointShadow.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsSSAO.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsScreenDup.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsScreenDupCursor.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsScreenQuad.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsSkybox.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsSprite.cso", "lib/uap10.0/HelixToolkit.UWP/Resources/vsVolume.cso", "lib/uap10.0/HelixToolkit.UWP/Themes/Generic.xaml"]}, "HelixToolkit.SharpDX.Core/2.27.0": {"sha512": "IkivjYXuM2YusPrJDCQQf9qsZgPnD0FLO33wZsch9m1BmoRTDOoGTgs6W6gdRu1PpP39Uo7BUwsmLgOIusSXBA==", "type": "package", "path": "helixtoolkit.sharpdx.core/2.27.0", "files": [".nupkg.metadata", ".signature.p7s", "AUTHORS", "CONTRIBUTORS", "LICENSE", "README.md", "helixtoolkit.png", "helixtoolkit.sharpdx.core.2.27.0.nupkg.sha512", "helixtoolkit.sharpdx.core.nuspec", "lib/netstandard2.0/HelixToolkit.SharpDX.Core.deps.json", "lib/netstandard2.0/HelixToolkit.SharpDX.Core.dll", "lib/netstandard2.0/HelixToolkit.SharpDX.Core.xml"]}, "HelixToolkit.SharpDX.Core.Wpf/2.27.0": {"sha512": "r6k8Jhw+gqCk4K57F25omnO8eQrIu6exMdiO5piZv6zXpUH9Yu4GZdEsymhe4rUE0k2wH0VN0aAOSA0fOytBIw==", "type": "package", "path": "helixtoolkit.sharpdx.core.wpf/2.27.0", "files": [".nupkg.metadata", ".signature.p7s", "AUTHORS", "CONTRIBUTORS", "LICENSE", "README.md", "helixtoolkit.png", "helixtoolkit.sharpdx.core.wpf.2.27.0.nupkg.sha512", "helixtoolkit.sharpdx.core.wpf.nuspec", "lib/netcoreapp3.1/HelixToolkit.SharpDX.Core.Wpf.deps.json", "lib/netcoreapp3.1/HelixToolkit.SharpDX.Core.Wpf.dll", "lib/netcoreapp3.1/HelixToolkit.SharpDX.Core.Wpf.xml"]}, "MediaFoundation.NetCore/2024.5.10": {"sha512": "q+tZLvg++T3ZAOEIW1IkNHjDqjI9B8nGyg3GOz6uYl1R4c5U3OdSw5/JndDqlQs94v5Tc6PuUIeTtOBV/lbPxw==", "type": "package", "path": "mediafoundation.netcore/2024.5.10", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net461/MediaFoundation.dll", "lib/net472/MediaFoundation.dll", "lib/net5.0-windows7.0/MediaFoundation.dll", "lib/net6.0-windows7.0/MediaFoundation.dll", "lib/net7.0-windows7.0/MediaFoundation.dll", "lib/net8.0-windows7.0/MediaFoundation.dll", "lib/netcoreapp3.1/MediaFoundation.dll", "license.txt", "mediafoundation.netcore.2024.5.10.nupkg.sha512", "mediafoundation.netcore.nuspec"]}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"sha512": "/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "build/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.3": {"sha512": "3Wrmi0kJDzClwAC+iBdUBpEKmEle8FQNsCs77fkiOIw/9oYA07bL1EZNX0kQ2OMN3xpwvl0vAtOCYY3ndDNlhQ==", "type": "package", "path": "microsoft.netcore.targets/1.1.3", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.3.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.VisualStudio.Threading/17.13.61": {"sha512": "R4iFsidiSB4P/zS+8BBz0fPFmgb64PQRQABuuIOjwgWgHBF0IHMcNz9wjG9CfN76BzVqgQU6/AJbM1NrJPE6zA==", "type": "package", "path": "microsoft.visualstudio.threading/17.13.61", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "microsoft.visualstudio.threading.17.13.61.nupkg.sha512", "microsoft.visualstudio.threading.nuspec"]}, "Microsoft.VisualStudio.Threading.Analyzers/17.13.61": {"sha512": "9ysRZR7xkVFsbwWjk0dx5tefySxhNMMwxmIr3G6UaFeugYE+TEghaTsmGiwT39maa2WFvH4zvjnEfDaCLq+Dtw==", "type": "package", "path": "microsoft.visualstudio.threading.analyzers/17.13.61", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.CSharp.dll", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.dll", "analyzers/cs/Microsoft.VisualStudio.Threading.Analyzers.dll", "analyzers/cs/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/de/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/es/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/fr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/it/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ja/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ko/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/pl/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/pt-BR/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/ru/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/tr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/zh-<PERSON>/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/cs/zh-Hant/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.VisualBasic.dll", "analyzers/vb/Microsoft.VisualStudio.Threading.Analyzers.dll", "analyzers/vb/cs/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/de/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/es/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/fr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/it/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ja/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ko/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/pl/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/pt-BR/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/ru/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/tr/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/zh-<PERSON>/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "analyzers/vb/zh-Hant/Microsoft.VisualStudio.Threading.Analyzers.CodeFixes.resources.dll", "build/AdditionalFiles/vs-threading.LegacyThreadSwitchingMembers.txt", "build/AdditionalFiles/vs-threading.MainThreadAssertingMethods.txt", "build/AdditionalFiles/vs-threading.MainThreadSwitchingMethods.txt", "build/AdditionalFiles/vs-threading.MembersRequiringMainThread.txt", "build/Microsoft.VisualStudio.Threading.Analyzers.targets", "microsoft.visualstudio.threading.analyzers.17.13.61.nupkg.sha512", "microsoft.visualstudio.threading.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.VisualStudio.Threading.Only/17.13.61": {"sha512": "vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "type": "package", "path": "microsoft.visualstudio.threading.only/17.13.61", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "README.md", "lib/net472/Microsoft.VisualStudio.Threading.dll", "lib/net472/Microsoft.VisualStudio.Threading.xml", "lib/net472/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/manifest.spdx.json", "lib/net472/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net472/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.dll", "lib/net8.0-windows7.0/Microsoft.VisualStudio.Threading.xml", "lib/net8.0-windows7.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/manifest.spdx.json", "lib/net8.0-windows7.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0-windows7.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/Microsoft.VisualStudio.Threading.dll", "lib/net8.0/Microsoft.VisualStudio.Threading.xml", "lib/net8.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/manifest.spdx.json", "lib/net8.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/net8.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Threading.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/manifest.spdx.json", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Threading.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll", "microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512", "microsoft.visualstudio.threading.only.nuspec"]}, "Microsoft.VisualStudio.Validation/17.8.8": {"sha512": "rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "type": "package", "path": "microsoft.visualstudio.validation/17.8.8", "files": [".nupkg.metadata", ".signature.p7s", "NOTICE", "PackageIcon.png", "lib/net6.0/Microsoft.VisualStudio.Validation.dll", "lib/net6.0/Microsoft.VisualStudio.Validation.xml", "lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.dll", "lib/netstandard2.0/Microsoft.VisualStudio.Validation.xml", "lib/netstandard2.0/cs/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/de/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/es/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/fr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/it/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ja/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ko/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pl/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/ru/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/tr/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.VisualStudio.Validation.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll", "microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "microsoft.visualstudio.validation.nuspec"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/9.0.4": {"sha512": "kHgtAkXhNEP8oGuAVe3Q5admxsdMlSdWE2rXcA9FfeGDZJQawPccmZgnOswgW3ugUPSJt7VH+TMQPz65mnhGSQ==", "type": "package", "path": "microsoft.win32.systemevents/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/net9.0/Microsoft.Win32.SystemEvents.dll", "lib/net9.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.9.0.4.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "ModernWpfUI/0.9.6": {"sha512": "eWe90HciyudQQtZVbZ9QFssS74BwK69W7cBd52AEZHmrsS4hrSA+/BpUbAFWzzFDH6FBE1xsHPiRC+wHS7xgJw==", "type": "package", "path": "modernwpfui/0.9.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/ModernWpf.Controls.dll", "lib/net45/ModernWpf.Controls.xml", "lib/net45/ModernWpf.dll", "lib/net45/ModernWpf.xml", "lib/net45/af-ZA/ModernWpf.Controls.resources.dll", "lib/net45/af-ZA/ModernWpf.resources.dll", "lib/net45/am-ET/ModernWpf.Controls.resources.dll", "lib/net45/am-ET/ModernWpf.resources.dll", "lib/net45/ar-SA/ModernWpf.Controls.resources.dll", "lib/net45/ar-SA/ModernWpf.resources.dll", "lib/net45/az-Latn-AZ/ModernWpf.Controls.resources.dll", "lib/net45/az-Latn-AZ/ModernWpf.resources.dll", "lib/net45/be-BY/ModernWpf.Controls.resources.dll", "lib/net45/be-BY/ModernWpf.resources.dll", "lib/net45/bg-BG/ModernWpf.Controls.resources.dll", "lib/net45/bg-BG/ModernWpf.resources.dll", "lib/net45/bn-BD/ModernWpf.Controls.resources.dll", "lib/net45/bn-BD/ModernWpf.resources.dll", "lib/net45/bs-Latn-BA/ModernWpf.Controls.resources.dll", "lib/net45/bs-Latn-BA/ModernWpf.resources.dll", "lib/net45/ca-ES/ModernWpf.Controls.resources.dll", "lib/net45/ca-ES/ModernWpf.resources.dll", "lib/net45/cs-CZ/ModernWpf.Controls.resources.dll", "lib/net45/cs-CZ/ModernWpf.resources.dll", "lib/net45/da-DK/ModernWpf.Controls.resources.dll", "lib/net45/da-DK/ModernWpf.resources.dll", "lib/net45/de-DE/ModernWpf.Controls.resources.dll", "lib/net45/de-DE/ModernWpf.resources.dll", "lib/net45/el-GR/ModernWpf.Controls.resources.dll", "lib/net45/el-GR/ModernWpf.resources.dll", "lib/net45/en-GB/ModernWpf.Controls.resources.dll", "lib/net45/en-GB/ModernWpf.resources.dll", "lib/net45/es-ES/ModernWpf.Controls.resources.dll", "lib/net45/es-ES/ModernWpf.resources.dll", "lib/net45/es-MX/ModernWpf.Controls.resources.dll", "lib/net45/es-MX/ModernWpf.resources.dll", "lib/net45/et-EE/ModernWpf.Controls.resources.dll", "lib/net45/et-EE/ModernWpf.resources.dll", "lib/net45/eu-ES/ModernWpf.Controls.resources.dll", "lib/net45/eu-ES/ModernWpf.resources.dll", "lib/net45/fa-IR/ModernWpf.Controls.resources.dll", "lib/net45/fa-IR/ModernWpf.resources.dll", "lib/net45/fi-FI/ModernWpf.Controls.resources.dll", "lib/net45/fi-FI/ModernWpf.resources.dll", "lib/net45/fil-PH/ModernWpf.Controls.resources.dll", "lib/net45/fil-PH/ModernWpf.resources.dll", "lib/net45/fr-CA/ModernWpf.Controls.resources.dll", "lib/net45/fr-CA/ModernWpf.resources.dll", "lib/net45/fr-FR/ModernWpf.Controls.resources.dll", "lib/net45/fr-FR/ModernWpf.resources.dll", "lib/net45/gl-ES/ModernWpf.Controls.resources.dll", "lib/net45/gl-ES/ModernWpf.resources.dll", "lib/net45/he-IL/ModernWpf.Controls.resources.dll", "lib/net45/he-IL/ModernWpf.resources.dll", "lib/net45/hi-IN/ModernWpf.Controls.resources.dll", "lib/net45/hi-IN/ModernWpf.resources.dll", "lib/net45/hr-HR/ModernWpf.Controls.resources.dll", "lib/net45/hr-HR/ModernWpf.resources.dll", "lib/net45/hu-HU/ModernWpf.Controls.resources.dll", "lib/net45/hu-HU/ModernWpf.resources.dll", "lib/net45/id-ID/ModernWpf.Controls.resources.dll", "lib/net45/id-ID/ModernWpf.resources.dll", "lib/net45/is-IS/ModernWpf.Controls.resources.dll", "lib/net45/is-IS/ModernWpf.resources.dll", "lib/net45/it-IT/ModernWpf.Controls.resources.dll", "lib/net45/it-IT/ModernWpf.resources.dll", "lib/net45/ja-JP/ModernWpf.Controls.resources.dll", "lib/net45/ja-JP/ModernWpf.resources.dll", "lib/net45/ka-GE/ModernWpf.Controls.resources.dll", "lib/net45/ka-GE/ModernWpf.resources.dll", "lib/net45/kk-KZ/ModernWpf.Controls.resources.dll", "lib/net45/kk-KZ/ModernWpf.resources.dll", "lib/net45/km-KH/ModernWpf.Controls.resources.dll", "lib/net45/km-KH/ModernWpf.resources.dll", "lib/net45/kn-IN/ModernWpf.Controls.resources.dll", "lib/net45/kn-IN/ModernWpf.resources.dll", "lib/net45/ko-KR/ModernWpf.Controls.resources.dll", "lib/net45/ko-KR/ModernWpf.resources.dll", "lib/net45/lo-LA/ModernWpf.Controls.resources.dll", "lib/net45/lo-LA/ModernWpf.resources.dll", "lib/net45/lt-LT/ModernWpf.Controls.resources.dll", "lib/net45/lt-LT/ModernWpf.resources.dll", "lib/net45/lv-LV/ModernWpf.Controls.resources.dll", "lib/net45/lv-LV/ModernWpf.resources.dll", "lib/net45/mk-MK/ModernWpf.Controls.resources.dll", "lib/net45/mk-MK/ModernWpf.resources.dll", "lib/net45/ml-IN/ModernWpf.Controls.resources.dll", "lib/net45/ml-IN/ModernWpf.resources.dll", "lib/net45/ms-MY/ModernWpf.Controls.resources.dll", "lib/net45/ms-MY/ModernWpf.resources.dll", "lib/net45/nb-NO/ModernWpf.Controls.resources.dll", "lib/net45/nb-NO/ModernWpf.resources.dll", "lib/net45/nl-NL/ModernWpf.Controls.resources.dll", "lib/net45/nl-NL/ModernWpf.resources.dll", "lib/net45/nn-NO/ModernWpf.Controls.resources.dll", "lib/net45/nn-NO/ModernWpf.resources.dll", "lib/net45/pl-PL/ModernWpf.Controls.resources.dll", "lib/net45/pl-PL/ModernWpf.resources.dll", "lib/net45/pt-BR/ModernWpf.Controls.resources.dll", "lib/net45/pt-BR/ModernWpf.resources.dll", "lib/net45/pt-PT/ModernWpf.Controls.resources.dll", "lib/net45/pt-PT/ModernWpf.resources.dll", "lib/net45/ro-RO/ModernWpf.Controls.resources.dll", "lib/net45/ro-RO/ModernWpf.resources.dll", "lib/net45/ru-RU/ModernWpf.Controls.resources.dll", "lib/net45/ru-RU/ModernWpf.resources.dll", "lib/net45/sk-SK/ModernWpf.Controls.resources.dll", "lib/net45/sk-SK/ModernWpf.resources.dll", "lib/net45/sl-SI/ModernWpf.Controls.resources.dll", "lib/net45/sl-SI/ModernWpf.resources.dll", "lib/net45/sq-AL/ModernWpf.Controls.resources.dll", "lib/net45/sq-AL/ModernWpf.resources.dll", "lib/net45/sr-Latn-RS/ModernWpf.Controls.resources.dll", "lib/net45/sr-Latn-RS/ModernWpf.resources.dll", "lib/net45/sv-SE/ModernWpf.Controls.resources.dll", "lib/net45/sv-SE/ModernWpf.resources.dll", "lib/net45/sw-KE/ModernWpf.Controls.resources.dll", "lib/net45/sw-KE/ModernWpf.resources.dll", "lib/net45/ta-IN/ModernWpf.Controls.resources.dll", "lib/net45/ta-IN/ModernWpf.resources.dll", "lib/net45/te-IN/ModernWpf.Controls.resources.dll", "lib/net45/te-IN/ModernWpf.resources.dll", "lib/net45/th-TH/ModernWpf.Controls.resources.dll", "lib/net45/th-TH/ModernWpf.resources.dll", "lib/net45/tr-TR/ModernWpf.Controls.resources.dll", "lib/net45/tr-TR/ModernWpf.resources.dll", "lib/net45/uk-UA/ModernWpf.Controls.resources.dll", "lib/net45/uk-UA/ModernWpf.resources.dll", "lib/net45/uz-Latn-UZ/ModernWpf.Controls.resources.dll", "lib/net45/uz-Latn-UZ/ModernWpf.resources.dll", "lib/net45/vi-VN/ModernWpf.Controls.resources.dll", "lib/net45/vi-VN/ModernWpf.resources.dll", "lib/net462/ModernWpf.Controls.dll", "lib/net462/ModernWpf.Controls.xml", "lib/net462/ModernWpf.dll", "lib/net462/ModernWpf.xml", "lib/net462/af-ZA/ModernWpf.Controls.resources.dll", "lib/net462/af-ZA/ModernWpf.resources.dll", "lib/net462/am-ET/ModernWpf.Controls.resources.dll", "lib/net462/am-ET/ModernWpf.resources.dll", "lib/net462/ar-SA/ModernWpf.Controls.resources.dll", "lib/net462/ar-SA/ModernWpf.resources.dll", "lib/net462/az-Latn-AZ/ModernWpf.Controls.resources.dll", "lib/net462/az-Latn-AZ/ModernWpf.resources.dll", "lib/net462/be-BY/ModernWpf.Controls.resources.dll", "lib/net462/be-BY/ModernWpf.resources.dll", "lib/net462/bg-BG/ModernWpf.Controls.resources.dll", "lib/net462/bg-BG/ModernWpf.resources.dll", "lib/net462/bn-BD/ModernWpf.Controls.resources.dll", "lib/net462/bn-BD/ModernWpf.resources.dll", "lib/net462/bs-Latn-BA/ModernWpf.Controls.resources.dll", "lib/net462/bs-Latn-BA/ModernWpf.resources.dll", "lib/net462/ca-ES/ModernWpf.Controls.resources.dll", "lib/net462/ca-ES/ModernWpf.resources.dll", "lib/net462/cs-CZ/ModernWpf.Controls.resources.dll", "lib/net462/cs-CZ/ModernWpf.resources.dll", "lib/net462/da-DK/ModernWpf.Controls.resources.dll", "lib/net462/da-DK/ModernWpf.resources.dll", "lib/net462/de-DE/ModernWpf.Controls.resources.dll", "lib/net462/de-DE/ModernWpf.resources.dll", "lib/net462/el-GR/ModernWpf.Controls.resources.dll", "lib/net462/el-GR/ModernWpf.resources.dll", "lib/net462/en-GB/ModernWpf.Controls.resources.dll", "lib/net462/en-GB/ModernWpf.resources.dll", "lib/net462/es-ES/ModernWpf.Controls.resources.dll", "lib/net462/es-ES/ModernWpf.resources.dll", "lib/net462/es-MX/ModernWpf.Controls.resources.dll", "lib/net462/es-MX/ModernWpf.resources.dll", "lib/net462/et-EE/ModernWpf.Controls.resources.dll", "lib/net462/et-EE/ModernWpf.resources.dll", "lib/net462/eu-ES/ModernWpf.Controls.resources.dll", "lib/net462/eu-ES/ModernWpf.resources.dll", "lib/net462/fa-IR/ModernWpf.Controls.resources.dll", "lib/net462/fa-IR/ModernWpf.resources.dll", "lib/net462/fi-FI/ModernWpf.Controls.resources.dll", "lib/net462/fi-FI/ModernWpf.resources.dll", "lib/net462/fil-PH/ModernWpf.Controls.resources.dll", "lib/net462/fil-PH/ModernWpf.resources.dll", "lib/net462/fr-CA/ModernWpf.Controls.resources.dll", "lib/net462/fr-CA/ModernWpf.resources.dll", "lib/net462/fr-FR/ModernWpf.Controls.resources.dll", "lib/net462/fr-FR/ModernWpf.resources.dll", "lib/net462/gl-ES/ModernWpf.Controls.resources.dll", "lib/net462/gl-ES/ModernWpf.resources.dll", "lib/net462/he-IL/ModernWpf.Controls.resources.dll", "lib/net462/he-IL/ModernWpf.resources.dll", "lib/net462/hi-IN/ModernWpf.Controls.resources.dll", "lib/net462/hi-IN/ModernWpf.resources.dll", "lib/net462/hr-HR/ModernWpf.Controls.resources.dll", "lib/net462/hr-HR/ModernWpf.resources.dll", "lib/net462/hu-HU/ModernWpf.Controls.resources.dll", "lib/net462/hu-HU/ModernWpf.resources.dll", "lib/net462/id-ID/ModernWpf.Controls.resources.dll", "lib/net462/id-ID/ModernWpf.resources.dll", "lib/net462/is-IS/ModernWpf.Controls.resources.dll", "lib/net462/is-IS/ModernWpf.resources.dll", "lib/net462/it-IT/ModernWpf.Controls.resources.dll", "lib/net462/it-IT/ModernWpf.resources.dll", "lib/net462/ja-JP/ModernWpf.Controls.resources.dll", "lib/net462/ja-JP/ModernWpf.resources.dll", "lib/net462/ka-GE/ModernWpf.Controls.resources.dll", "lib/net462/ka-GE/ModernWpf.resources.dll", "lib/net462/kk-KZ/ModernWpf.Controls.resources.dll", "lib/net462/kk-KZ/ModernWpf.resources.dll", "lib/net462/km-KH/ModernWpf.Controls.resources.dll", "lib/net462/km-KH/ModernWpf.resources.dll", "lib/net462/kn-IN/ModernWpf.Controls.resources.dll", "lib/net462/kn-IN/ModernWpf.resources.dll", "lib/net462/ko-KR/ModernWpf.Controls.resources.dll", "lib/net462/ko-KR/ModernWpf.resources.dll", "lib/net462/lo-LA/ModernWpf.Controls.resources.dll", "lib/net462/lo-LA/ModernWpf.resources.dll", "lib/net462/lt-LT/ModernWpf.Controls.resources.dll", "lib/net462/lt-LT/ModernWpf.resources.dll", "lib/net462/lv-LV/ModernWpf.Controls.resources.dll", "lib/net462/lv-LV/ModernWpf.resources.dll", "lib/net462/mk-MK/ModernWpf.Controls.resources.dll", "lib/net462/mk-MK/ModernWpf.resources.dll", "lib/net462/ml-IN/ModernWpf.Controls.resources.dll", "lib/net462/ml-IN/ModernWpf.resources.dll", "lib/net462/ms-MY/ModernWpf.Controls.resources.dll", "lib/net462/ms-MY/ModernWpf.resources.dll", "lib/net462/nb-NO/ModernWpf.Controls.resources.dll", "lib/net462/nb-NO/ModernWpf.resources.dll", "lib/net462/nl-NL/ModernWpf.Controls.resources.dll", "lib/net462/nl-NL/ModernWpf.resources.dll", "lib/net462/nn-NO/ModernWpf.Controls.resources.dll", "lib/net462/nn-NO/ModernWpf.resources.dll", "lib/net462/pl-PL/ModernWpf.Controls.resources.dll", "lib/net462/pl-PL/ModernWpf.resources.dll", "lib/net462/pt-BR/ModernWpf.Controls.resources.dll", "lib/net462/pt-BR/ModernWpf.resources.dll", "lib/net462/pt-PT/ModernWpf.Controls.resources.dll", "lib/net462/pt-PT/ModernWpf.resources.dll", "lib/net462/ro-RO/ModernWpf.Controls.resources.dll", "lib/net462/ro-RO/ModernWpf.resources.dll", "lib/net462/ru-RU/ModernWpf.Controls.resources.dll", "lib/net462/ru-RU/ModernWpf.resources.dll", "lib/net462/sk-SK/ModernWpf.Controls.resources.dll", "lib/net462/sk-SK/ModernWpf.resources.dll", "lib/net462/sl-SI/ModernWpf.Controls.resources.dll", "lib/net462/sl-SI/ModernWpf.resources.dll", "lib/net462/sq-AL/ModernWpf.Controls.resources.dll", "lib/net462/sq-AL/ModernWpf.resources.dll", "lib/net462/sr-Latn-RS/ModernWpf.Controls.resources.dll", "lib/net462/sr-Latn-RS/ModernWpf.resources.dll", "lib/net462/sv-SE/ModernWpf.Controls.resources.dll", "lib/net462/sv-SE/ModernWpf.resources.dll", "lib/net462/sw-KE/ModernWpf.Controls.resources.dll", "lib/net462/sw-KE/ModernWpf.resources.dll", "lib/net462/ta-IN/ModernWpf.Controls.resources.dll", "lib/net462/ta-IN/ModernWpf.resources.dll", "lib/net462/te-IN/ModernWpf.Controls.resources.dll", "lib/net462/te-IN/ModernWpf.resources.dll", "lib/net462/th-TH/ModernWpf.Controls.resources.dll", "lib/net462/th-TH/ModernWpf.resources.dll", "lib/net462/tr-TR/ModernWpf.Controls.resources.dll", "lib/net462/tr-TR/ModernWpf.resources.dll", "lib/net462/uk-UA/ModernWpf.Controls.resources.dll", "lib/net462/uk-UA/ModernWpf.resources.dll", "lib/net462/uz-Latn-UZ/ModernWpf.Controls.resources.dll", "lib/net462/uz-Latn-UZ/ModernWpf.resources.dll", "lib/net462/vi-VN/ModernWpf.Controls.resources.dll", "lib/net462/vi-VN/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ModernWpf.Controls.dll", "lib/net5.0-windows7.0/ModernWpf.Controls.xml", "lib/net5.0-windows7.0/ModernWpf.dll", "lib/net5.0-windows7.0/ModernWpf.xml", "lib/net5.0-windows7.0/af-ZA/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/af-ZA/ModernWpf.resources.dll", "lib/net5.0-windows7.0/am-ET/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/am-ET/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ar-SA/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ar-SA/ModernWpf.resources.dll", "lib/net5.0-windows7.0/az-Latn-AZ/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/az-Latn-AZ/ModernWpf.resources.dll", "lib/net5.0-windows7.0/be-BY/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/be-BY/ModernWpf.resources.dll", "lib/net5.0-windows7.0/bg-BG/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/bg-BG/ModernWpf.resources.dll", "lib/net5.0-windows7.0/bn-BD/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/bn-BD/ModernWpf.resources.dll", "lib/net5.0-windows7.0/bs-Latn-BA/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/bs-Latn-BA/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ca-ES/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ca-ES/ModernWpf.resources.dll", "lib/net5.0-windows7.0/cs-CZ/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/cs-CZ/ModernWpf.resources.dll", "lib/net5.0-windows7.0/da-DK/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/da-DK/ModernWpf.resources.dll", "lib/net5.0-windows7.0/de-DE/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/de-DE/ModernWpf.resources.dll", "lib/net5.0-windows7.0/el-GR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/el-GR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/en-GB/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/en-GB/ModernWpf.resources.dll", "lib/net5.0-windows7.0/es-ES/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/es-ES/ModernWpf.resources.dll", "lib/net5.0-windows7.0/es-MX/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/es-MX/ModernWpf.resources.dll", "lib/net5.0-windows7.0/et-EE/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/et-EE/ModernWpf.resources.dll", "lib/net5.0-windows7.0/eu-ES/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/eu-ES/ModernWpf.resources.dll", "lib/net5.0-windows7.0/fa-IR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/fa-IR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/fi-FI/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/fi-FI/ModernWpf.resources.dll", "lib/net5.0-windows7.0/fil-PH/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/fil-PH/ModernWpf.resources.dll", "lib/net5.0-windows7.0/fr-CA/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/fr-CA/ModernWpf.resources.dll", "lib/net5.0-windows7.0/fr-FR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/fr-FR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/gl-ES/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/gl-ES/ModernWpf.resources.dll", "lib/net5.0-windows7.0/he-IL/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/he-IL/ModernWpf.resources.dll", "lib/net5.0-windows7.0/hi-IN/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/hi-IN/ModernWpf.resources.dll", "lib/net5.0-windows7.0/hr-HR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/hr-HR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/hu-HU/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/hu-HU/ModernWpf.resources.dll", "lib/net5.0-windows7.0/id-ID/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/id-ID/ModernWpf.resources.dll", "lib/net5.0-windows7.0/is-IS/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/is-IS/ModernWpf.resources.dll", "lib/net5.0-windows7.0/it-IT/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/it-IT/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ja-JP/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ja-JP/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ka-GE/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ka-GE/ModernWpf.resources.dll", "lib/net5.0-windows7.0/kk-KZ/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/kk-KZ/ModernWpf.resources.dll", "lib/net5.0-windows7.0/km-KH/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/km-KH/ModernWpf.resources.dll", "lib/net5.0-windows7.0/kn-IN/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/kn-IN/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ko-KR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ko-KR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/lo-LA/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/lo-LA/ModernWpf.resources.dll", "lib/net5.0-windows7.0/lt-LT/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/lt-LT/ModernWpf.resources.dll", "lib/net5.0-windows7.0/lv-LV/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/lv-LV/ModernWpf.resources.dll", "lib/net5.0-windows7.0/mk-MK/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/mk-MK/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ml-IN/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ml-IN/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ms-MY/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ms-MY/ModernWpf.resources.dll", "lib/net5.0-windows7.0/nb-NO/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/nb-NO/ModernWpf.resources.dll", "lib/net5.0-windows7.0/nl-NL/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/nl-NL/ModernWpf.resources.dll", "lib/net5.0-windows7.0/nn-NO/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/nn-NO/ModernWpf.resources.dll", "lib/net5.0-windows7.0/pl-PL/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/pl-PL/ModernWpf.resources.dll", "lib/net5.0-windows7.0/pt-BR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/pt-BR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/pt-PT/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/pt-PT/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ro-RO/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ro-RO/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ru-RU/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ru-RU/ModernWpf.resources.dll", "lib/net5.0-windows7.0/sk-SK/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/sk-SK/ModernWpf.resources.dll", "lib/net5.0-windows7.0/sl-SI/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/sl-SI/ModernWpf.resources.dll", "lib/net5.0-windows7.0/sq-AL/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/sq-AL/ModernWpf.resources.dll", "lib/net5.0-windows7.0/sr-Latn-RS/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/sr-Latn-RS/ModernWpf.resources.dll", "lib/net5.0-windows7.0/sv-SE/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/sv-SE/ModernWpf.resources.dll", "lib/net5.0-windows7.0/sw-KE/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/sw-KE/ModernWpf.resources.dll", "lib/net5.0-windows7.0/ta-IN/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/ta-IN/ModernWpf.resources.dll", "lib/net5.0-windows7.0/te-IN/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/te-IN/ModernWpf.resources.dll", "lib/net5.0-windows7.0/th-TH/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/th-TH/ModernWpf.resources.dll", "lib/net5.0-windows7.0/tr-TR/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/tr-TR/ModernWpf.resources.dll", "lib/net5.0-windows7.0/uk-UA/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/uk-UA/ModernWpf.resources.dll", "lib/net5.0-windows7.0/uz-Latn-UZ/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/uz-Latn-UZ/ModernWpf.resources.dll", "lib/net5.0-windows7.0/vi-VN/ModernWpf.Controls.resources.dll", "lib/net5.0-windows7.0/vi-VN/ModernWpf.resources.dll", "lib/netcoreapp3.0/ModernWpf.Controls.dll", "lib/netcoreapp3.0/ModernWpf.Controls.xml", "lib/netcoreapp3.0/ModernWpf.dll", "lib/netcoreapp3.0/ModernWpf.xml", "lib/netcoreapp3.0/af-ZA/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/af-ZA/ModernWpf.resources.dll", "lib/netcoreapp3.0/am-ET/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/am-ET/ModernWpf.resources.dll", "lib/netcoreapp3.0/ar-SA/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ar-SA/ModernWpf.resources.dll", "lib/netcoreapp3.0/az-Latn-AZ/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/az-Latn-AZ/ModernWpf.resources.dll", "lib/netcoreapp3.0/be-BY/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/be-BY/ModernWpf.resources.dll", "lib/netcoreapp3.0/bg-BG/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/bg-BG/ModernWpf.resources.dll", "lib/netcoreapp3.0/bn-BD/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/bn-BD/ModernWpf.resources.dll", "lib/netcoreapp3.0/bs-Latn-BA/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/bs-Latn-BA/ModernWpf.resources.dll", "lib/netcoreapp3.0/ca-ES/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ca-ES/ModernWpf.resources.dll", "lib/netcoreapp3.0/cs-CZ/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/cs-CZ/ModernWpf.resources.dll", "lib/netcoreapp3.0/da-DK/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/da-DK/ModernWpf.resources.dll", "lib/netcoreapp3.0/de-DE/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/de-DE/ModernWpf.resources.dll", "lib/netcoreapp3.0/el-GR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/el-GR/ModernWpf.resources.dll", "lib/netcoreapp3.0/en-GB/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/en-GB/ModernWpf.resources.dll", "lib/netcoreapp3.0/es-ES/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/es-ES/ModernWpf.resources.dll", "lib/netcoreapp3.0/es-MX/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/es-MX/ModernWpf.resources.dll", "lib/netcoreapp3.0/et-EE/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/et-EE/ModernWpf.resources.dll", "lib/netcoreapp3.0/eu-ES/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/eu-ES/ModernWpf.resources.dll", "lib/netcoreapp3.0/fa-IR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/fa-IR/ModernWpf.resources.dll", "lib/netcoreapp3.0/fi-FI/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/fi-FI/ModernWpf.resources.dll", "lib/netcoreapp3.0/fil-PH/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/fil-PH/ModernWpf.resources.dll", "lib/netcoreapp3.0/fr-CA/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/fr-CA/ModernWpf.resources.dll", "lib/netcoreapp3.0/fr-FR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/fr-FR/ModernWpf.resources.dll", "lib/netcoreapp3.0/gl-ES/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/gl-ES/ModernWpf.resources.dll", "lib/netcoreapp3.0/he-IL/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/he-IL/ModernWpf.resources.dll", "lib/netcoreapp3.0/hi-IN/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/hi-IN/ModernWpf.resources.dll", "lib/netcoreapp3.0/hr-HR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/hr-HR/ModernWpf.resources.dll", "lib/netcoreapp3.0/hu-HU/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/hu-HU/ModernWpf.resources.dll", "lib/netcoreapp3.0/id-ID/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/id-ID/ModernWpf.resources.dll", "lib/netcoreapp3.0/is-IS/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/is-IS/ModernWpf.resources.dll", "lib/netcoreapp3.0/it-IT/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/it-IT/ModernWpf.resources.dll", "lib/netcoreapp3.0/ja-JP/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ja-JP/ModernWpf.resources.dll", "lib/netcoreapp3.0/ka-GE/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ka-GE/ModernWpf.resources.dll", "lib/netcoreapp3.0/kk-KZ/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/kk-KZ/ModernWpf.resources.dll", "lib/netcoreapp3.0/km-KH/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/km-KH/ModernWpf.resources.dll", "lib/netcoreapp3.0/kn-IN/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/kn-IN/ModernWpf.resources.dll", "lib/netcoreapp3.0/ko-KR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ko-KR/ModernWpf.resources.dll", "lib/netcoreapp3.0/lo-LA/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/lo-LA/ModernWpf.resources.dll", "lib/netcoreapp3.0/lt-LT/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/lt-LT/ModernWpf.resources.dll", "lib/netcoreapp3.0/lv-LV/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/lv-LV/ModernWpf.resources.dll", "lib/netcoreapp3.0/mk-MK/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/mk-MK/ModernWpf.resources.dll", "lib/netcoreapp3.0/ml-IN/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ml-IN/ModernWpf.resources.dll", "lib/netcoreapp3.0/ms-MY/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ms-MY/ModernWpf.resources.dll", "lib/netcoreapp3.0/nb-NO/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/nb-NO/ModernWpf.resources.dll", "lib/netcoreapp3.0/nl-NL/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/nl-NL/ModernWpf.resources.dll", "lib/netcoreapp3.0/nn-NO/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/nn-NO/ModernWpf.resources.dll", "lib/netcoreapp3.0/pl-PL/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/pl-PL/ModernWpf.resources.dll", "lib/netcoreapp3.0/pt-BR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/pt-BR/ModernWpf.resources.dll", "lib/netcoreapp3.0/pt-PT/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/pt-PT/ModernWpf.resources.dll", "lib/netcoreapp3.0/ro-RO/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ro-RO/ModernWpf.resources.dll", "lib/netcoreapp3.0/ru-RU/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ru-RU/ModernWpf.resources.dll", "lib/netcoreapp3.0/sk-SK/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/sk-SK/ModernWpf.resources.dll", "lib/netcoreapp3.0/sl-SI/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/sl-SI/ModernWpf.resources.dll", "lib/netcoreapp3.0/sq-AL/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/sq-AL/ModernWpf.resources.dll", "lib/netcoreapp3.0/sr-Latn-RS/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/sr-Latn-RS/ModernWpf.resources.dll", "lib/netcoreapp3.0/sv-SE/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/sv-SE/ModernWpf.resources.dll", "lib/netcoreapp3.0/sw-KE/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/sw-KE/ModernWpf.resources.dll", "lib/netcoreapp3.0/ta-IN/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/ta-IN/ModernWpf.resources.dll", "lib/netcoreapp3.0/te-IN/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/te-IN/ModernWpf.resources.dll", "lib/netcoreapp3.0/th-TH/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/th-TH/ModernWpf.resources.dll", "lib/netcoreapp3.0/tr-TR/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/tr-TR/ModernWpf.resources.dll", "lib/netcoreapp3.0/uk-UA/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/uk-UA/ModernWpf.resources.dll", "lib/netcoreapp3.0/uz-Latn-UZ/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/uz-Latn-UZ/ModernWpf.resources.dll", "lib/netcoreapp3.0/vi-VN/ModernWpf.Controls.resources.dll", "lib/netcoreapp3.0/vi-VN/ModernWpf.resources.dll", "modernwpfui.0.9.6.nupkg.sha512", "modernwpfui.nuspec", "readme.txt"]}, "NETStandard.Library/1.6.1": {"sha512": "WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "type": "package", "path": "netstandard.library/1.6.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "netstandard.library.1.6.1.nupkg.sha512", "netstandard.library.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "PrecisionTimer.NET/*******": {"sha512": "gFOgq5SfGgkmR7GVqv9YM7oWhMXQS5R3XO3oD0Rzp0hZmkpJkufuR4KowtgosrXOz56gD6fXA2SRc0Vr/h7O/w==", "type": "package", "path": "precisiontimer.net/*******", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/PrecisionTimer.NET.dll", "lib/netstandard2.0/PrecisionTimer.NET.xml", "package.png", "precisiontimer.net.*******.nupkg.sha512", "precisiontimer.net.nuspec"]}, "PropertyTools/3.1.0": {"sha512": "NAbA0+9rXe3L372/A4QOuCt0UCw9iPKI4Hkeu8PycC9TSJmEpsAgRb/qdXaPLkE+Z3zT3n1lPIGL7Oi+WBltqg==", "type": "package", "path": "propertytools/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/PropertyTools.dll", "lib/netstandard2.0/PropertyTools.dll", "propertytools.3.1.0.nupkg.sha512", "propertytools.nuspec"]}, "PropertyTools.Wpf/3.1.0": {"sha512": "e1CyxgZ+6RUeEHXTU0+ozNgdQIYciouu4I/9ygSaGn6v8ftzJ/us/FOlZyHyhf7QNakGH0Jds6eMZ45CM3ES3w==", "type": "package", "path": "propertytools.wpf/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net45/PropertyTools.Wpf.dll", "lib/netcoreapp3.0/PropertyTools.Wpf.dll", "propertytools.wpf.3.1.0.nupkg.sha512", "propertytools.wpf.nuspec"]}, "Quamotion.TurboJpegWrapper/2.0.32": {"sha512": "tVRoNDchXwyHdHJv5tiB70I8OzJXb+heNseOsFc9cgBTDF/pbcD+Tsefu6PJPVUsMJ8NowKB0Vw5CRbni8t/zw==", "type": "package", "path": "quamotion.turbojpegwrapper/2.0.32", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Quamotion.TurboJpegWrapper.dll", "lib/net5.0/Quamotion.TurboJpegWrapper.dll", "lib/netstandard2.1/Quamotion.TurboJpegWrapper.dll", "quamotion.turbojpegwrapper.2.0.32.nupkg.sha512", "quamotion.turbojpegwrapper.nuspec", "runtimes/osx-x64/native/libturbojpeg.dylib", "runtimes/win-x64/native/turbojpeg.dll", "runtimes/win-x64/native/vcruntime140.dll", "runtimes/win-x86/native/turbojpeg.dll", "runtimes/win-x86/native/vcruntime140.dll"]}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "type": "package", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/debian.8-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "type": "package", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.23-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "type": "package", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/fedora.24-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.native.System/4.3.0": {"sha512": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "type": "package", "path": "runtime.native.system/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.3.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.IO.Compression/4.3.0": {"sha512": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "type": "package", "path": "runtime.native.system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.io.compression.4.3.0.nupkg.sha512", "runtime.native.system.io.compression.nuspec"]}, "runtime.native.System.Net.Http/4.3.0": {"sha512": "ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "type": "package", "path": "runtime.native.system.net.http/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.net.http.4.3.0.nupkg.sha512", "runtime.native.system.net.http.nuspec"]}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "type": "package", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.native.system.security.cryptography.apple.nuspec"]}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "type": "package", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.native.system.security.cryptography.openssl.nuspec"]}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "type": "package", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.13.2-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "type": "package", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/opensuse.42.1-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"sha512": "kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.Apple.dylib"]}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "type": "package", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/osx.10.10-x64/native/System.Security.Cryptography.Native.OpenSsl.dylib"]}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "type": "package", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/rhel.7-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "type": "package", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.14.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "type": "package", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.04-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"sha512": "leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "type": "package", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.nuspec", "runtimes/ubuntu.16.10-x64/native/System.Security.Cryptography.Native.OpenSsl.so"]}, "SharpDX/4.2.0": {"sha512": "3pv0LFMvfK/dv1qISJnn8xBeeT6R/FRvr0EV4KI2DGsL84Qlv6P7isWqxGyU0LCwlSVCJN3jgHJ4Bl0KI2PJww==", "type": "package", "path": "sharpdx/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/.xml", "lib/net40/SharpDX.dll", "lib/net40/SharpDX.pdb", "lib/net45/.xml", "lib/net45/SharpDX.dll", "lib/net45/SharpDX.pdb", "lib/netstandard1.1/.xml", "lib/netstandard1.1/SharpDX.dll", "lib/netstandard1.1/SharpDX.pdb", "lib/uap10.0/.xml", "lib/uap10.0/SharpDX.dll", "lib/uap10.0/SharpDX.pdb", "lib/uap10.0/SharpDX.pri", "sharpdx.4.2.0.nupkg.sha512", "sharpdx.nuspec"]}, "SharpDX.D3DCompiler/4.2.0": {"sha512": "Rnsd6Ilp127xbXqhTit8WKFQUrXwWxqVGpglyWDNkIBCk0tWXNQEjrJpsl0KAObzyZaa33+EXAikLVt5fnd3GA==", "type": "package", "path": "sharpdx.d3dcompiler/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SharpDX.D3DCompiler.dll", "lib/net40/SharpDX.D3DCompiler.pdb", "lib/net40/SharpDX.D3DCompiler.xml", "lib/net45/SharpDX.D3DCompiler.dll", "lib/net45/SharpDX.D3DCompiler.pdb", "lib/net45/SharpDX.D3DCompiler.xml", "lib/netstandard1.1/SharpDX.D3DCompiler.dll", "lib/netstandard1.1/SharpDX.D3DCompiler.pdb", "lib/netstandard1.1/SharpDX.D3DCompiler.xml", "lib/uap10.0/SharpDX.D3DCompiler.dll", "lib/uap10.0/SharpDX.D3DCompiler.pdb", "lib/uap10.0/SharpDX.D3DCompiler.pri", "lib/uap10.0/SharpDX.D3DCompiler.xml", "sharpdx.d3dcompiler.4.2.0.nupkg.sha512", "sharpdx.d3dcompiler.nuspec"]}, "SharpDX.Direct2D1/4.2.0": {"sha512": "Qs8LzDMaQf1u3KB8ArHu9pDv6itZ++QXs99a/bVAG+nKr0Hx5NG4mcN5vsfE0mVR2TkeHfeUm4PksRah6VUPtA==", "type": "package", "path": "sharpdx.direct2d1/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SharpDX.Direct2D1.dll", "lib/net40/SharpDX.Direct2D1.pdb", "lib/net40/SharpDX.Direct2D1.xml", "lib/net45/SharpDX.Direct2D1.dll", "lib/net45/SharpDX.Direct2D1.pdb", "lib/net45/SharpDX.Direct2D1.xml", "lib/netstandard1.1/SharpDX.Direct2D1.dll", "lib/netstandard1.1/SharpDX.Direct2D1.pdb", "lib/netstandard1.1/SharpDX.Direct2D1.xml", "lib/uap10.0/SharpDX.Direct2D1.dll", "lib/uap10.0/SharpDX.Direct2D1.pdb", "lib/uap10.0/SharpDX.Direct2D1.pri", "lib/uap10.0/SharpDX.Direct2D1.xml", "sharpdx.direct2d1.4.2.0.nupkg.sha512", "sharpdx.direct2d1.nuspec"]}, "SharpDX.Direct3D11/4.2.0": {"sha512": "oTm/iT5X/IIuJ8kNYP+DTC/MhBhqtRF5dbgPPFgLBdQv0BKzNTzXQQXd7SveBFjQg6hXEAJ2jGCAzNYvGFc9LA==", "type": "package", "path": "sharpdx.direct3d11/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SharpDX.Direct3D11.dll", "lib/net40/SharpDX.Direct3D11.pdb", "lib/net40/SharpDX.Direct3D11.xml", "lib/net45/SharpDX.Direct3D11.dll", "lib/net45/SharpDX.Direct3D11.pdb", "lib/net45/SharpDX.Direct3D11.xml", "lib/netstandard1.1/SharpDX.Direct3D11.dll", "lib/netstandard1.1/SharpDX.Direct3D11.pdb", "lib/netstandard1.1/SharpDX.Direct3D11.xml", "lib/uap10.0/SharpDX.Direct3D11.dll", "lib/uap10.0/SharpDX.Direct3D11.pdb", "lib/uap10.0/SharpDX.Direct3D11.pri", "lib/uap10.0/SharpDX.Direct3D11.xml", "sharpdx.direct3d11.4.2.0.nupkg.sha512", "sharpdx.direct3d11.nuspec"]}, "SharpDX.Direct3D9/4.2.0": {"sha512": "1KgqXvPj/j5hLYxfcOQoJdHskamu7EWg60n9LBAYhun7dR+sKS0ZDNb7NuKwFP1bM0HZqvokjXQIf3oA2NpdqA==", "type": "package", "path": "sharpdx.direct3d9/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SharpDX.Direct3D9.dll", "lib/net40/SharpDX.Direct3D9.pdb", "lib/net40/SharpDX.Direct3D9.xml", "lib/net45/SharpDX.Direct3D9.dll", "lib/net45/SharpDX.Direct3D9.pdb", "lib/net45/SharpDX.Direct3D9.xml", "lib/netstandard1.3/SharpDX.Direct3D9.dll", "lib/netstandard1.3/SharpDX.Direct3D9.pdb", "lib/netstandard1.3/SharpDX.Direct3D9.xml", "sharpdx.direct3d9.4.2.0.nupkg.sha512", "sharpdx.direct3d9.nuspec"]}, "SharpDX.DXGI/4.2.0": {"sha512": "UjKqkgWc8U+SP+j3LBzFP6OB6Ntapjih7Xo+g1rLcsGbIb5KwewBrBChaUu7sil8rWoeVU/k0EJd3SMN4VqNZw==", "type": "package", "path": "sharpdx.dxgi/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SharpDX.DXGI.dll", "lib/net40/SharpDX.DXGI.pdb", "lib/net40/SharpDX.DXGI.xml", "lib/net45/SharpDX.DXGI.dll", "lib/net45/SharpDX.DXGI.pdb", "lib/net45/SharpDX.DXGI.xml", "lib/netstandard1.1/SharpDX.DXGI.dll", "lib/netstandard1.1/SharpDX.DXGI.pdb", "lib/netstandard1.1/SharpDX.DXGI.xml", "lib/uap10.0/SharpDX.DXGI.dll", "lib/uap10.0/SharpDX.DXGI.pdb", "lib/uap10.0/SharpDX.DXGI.pri", "lib/uap10.0/SharpDX.DXGI.xml", "sharpdx.dxgi.4.2.0.nupkg.sha512", "sharpdx.dxgi.nuspec"]}, "SharpDX.Mathematics/4.2.0": {"sha512": "R2pcKLgdsP9p5WyTjHmGOZ0ka0zASAZYc6P4L6rSvjYhf6klGYbent7MiVwbkwkt9dD44p5brjy5IwAnVONWGw==", "type": "package", "path": "sharpdx.mathematics/4.2.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/SharpDX.Mathematics.dll", "lib/net40/SharpDX.Mathematics.pdb", "lib/net40/SharpDX.Mathematics.xml", "lib/net45/SharpDX.Mathematics.dll", "lib/net45/SharpDX.Mathematics.pdb", "lib/net45/SharpDX.Mathematics.xml", "lib/netstandard1.1/SharpDX.Mathematics.dll", "lib/netstandard1.1/SharpDX.Mathematics.pdb", "lib/netstandard1.1/SharpDX.Mathematics.xml", "lib/uap10.0/SharpDX.Mathematics.dll", "lib/uap10.0/SharpDX.Mathematics.pdb", "lib/uap10.0/SharpDX.Mathematics.pri", "lib/uap10.0/SharpDX.Mathematics.xml", "sharpdx.mathematics.4.2.0.nupkg.sha512", "sharpdx.mathematics.nuspec"]}, "SharpGen.Runtime/2.2.0-beta": {"sha512": "pqf/lAf4jy1iWqkm37JmhoQhBMPVudI/F9qp2zVvzjWAPeSggRIuxGMVEZQ4UQiqtJ1Rf/+j3MVAONGYyCEDzQ==", "type": "package", "path": "sharpgen.runtime/2.2.0-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/Mapping.xml", "build/SharpGen.Runtime.props", "buildMultiTargeting/SharpGen.Runtime.props", "lib/net461/SharpGen.Runtime.dll", "lib/net471/SharpGen.Runtime.dll", "lib/net7.0/SharpGen.Runtime.dll", "lib/net8.0/SharpGen.Runtime.dll", "lib/netcoreapp3.1/SharpGen.Runtime.dll", "lib/netstandard2.0/SharpGen.Runtime.dll", "lib/netstandard2.1/SharpGen.Runtime.dll", "sharpgen.runtime.2.2.0-beta.nupkg.sha512", "sharpgen.runtime.nuspec"]}, "SharpGen.Runtime.COM/2.2.0-beta": {"sha512": "4vsXC8ohyVslcUDVBoVXLDkjKprqujh3GWy+DqqULjyZ3GCx7nwRAV5DdrXZxX70iEiKyI3TxW3Qhf/oOXeC1Q==", "type": "package", "path": "sharpgen.runtime.com/2.2.0-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/SharpGen.Runtime.COM.BindMapping.xml", "build/SharpGen.Runtime.COM.props", "buildMultiTargeting/SharpGen.Runtime.COM.props", "lib/net461/SharpGen.Runtime.COM.dll", "lib/net471/SharpGen.Runtime.COM.dll", "lib/net7.0/SharpGen.Runtime.COM.dll", "lib/net8.0/SharpGen.Runtime.COM.dll", "lib/netcoreapp3.1/SharpGen.Runtime.COM.dll", "lib/netstandard2.0/SharpGen.Runtime.COM.dll", "lib/netstandard2.1/SharpGen.Runtime.COM.dll", "sharpgen.runtime.com.2.2.0-beta.nupkg.sha512", "sharpgen.runtime.com.nuspec"]}, "Svg/3.4.7": {"sha512": "Omez7ly5BGhg3OzdV+LHZ5sI0+JQ6hF7WVKUeyHw4jRvcEWNCPCf1MWMBaf+R0DRBSZHx5EUHwBTEF+2oYtsAw==", "type": "package", "path": "svg/3.4.7", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Svg.dll", "lib/net462/Svg.xml", "lib/net472/Svg.dll", "lib/net472/Svg.xml", "lib/net481/Svg.dll", "lib/net481/Svg.xml", "lib/net6.0/Svg.dll", "lib/net6.0/Svg.xml", "lib/net8.0/Svg.dll", "lib/net8.0/Svg.xml", "lib/netcoreapp3.1/Svg.dll", "lib/netcoreapp3.1/Svg.xml", "lib/netstandard2.0/Svg.dll", "lib/netstandard2.0/Svg.xml", "lib/netstandard2.1/Svg.dll", "lib/netstandard2.1/Svg.xml", "svg-logo-v.png", "svg.3.4.7.nupkg.sha512", "svg.nuspec"]}, "System.AppContext/4.3.0": {"sha512": "fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "type": "package", "path": "system.appcontext/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.3.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Buffers/4.5.0": {"sha512": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "type": "package", "path": "system.buffers/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.3.0": {"sha512": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "type": "package", "path": "system.collections.concurrent/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.3.0.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.Collections.NonGeneric/4.3.0": {"sha512": "prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "type": "package", "path": "system.collections.nongeneric/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.nongeneric.4.3.0.nupkg.sha512", "system.collections.nongeneric.nuspec"]}, "System.Collections.Specialized/4.3.0": {"sha512": "Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "type": "package", "path": "system.collections.specialized/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.Specialized.dll", "lib/netstandard1.3/System.Collections.Specialized.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.xml", "ref/netstandard1.3/de/System.Collections.Specialized.xml", "ref/netstandard1.3/es/System.Collections.Specialized.xml", "ref/netstandard1.3/fr/System.Collections.Specialized.xml", "ref/netstandard1.3/it/System.Collections.Specialized.xml", "ref/netstandard1.3/ja/System.Collections.Specialized.xml", "ref/netstandard1.3/ko/System.Collections.Specialized.xml", "ref/netstandard1.3/ru/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hans/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hant/System.Collections.Specialized.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.specialized.4.3.0.nupkg.sha512", "system.collections.specialized.nuspec"]}, "System.ComponentModel/4.3.0": {"sha512": "VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "type": "package", "path": "system.componentmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.4.3.0.nupkg.sha512", "system.componentmodel.nuspec"]}, "System.ComponentModel.Annotations/4.6.0": {"sha512": "pOd+UhZ3X8xfwKDlgAzowUJNjp8VYVmOHZm++vCd0kq1HZ0zK3mNo2yRXjYgv7Ik/Xi43fmJfND2PLEsQSALCg==", "type": "package", "path": "system.componentmodel.annotations/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.6.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Primitives/4.3.0": {"sha512": "j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "type": "package", "path": "system.componentmodel.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.Primitives.dll", "lib/netstandard1.0/System.ComponentModel.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/de/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/es/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/fr/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/it/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ja/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ko/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ru/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.primitives.4.3.0.nupkg.sha512", "system.componentmodel.primitives.nuspec"]}, "System.ComponentModel.TypeConverter/4.3.0": {"sha512": "16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "type": "package", "path": "system.componentmodel.typeconverter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.TypeConverter.dll", "lib/net462/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.0/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.5/System.ComponentModel.TypeConverter.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.TypeConverter.dll", "ref/net462/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.5/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "system.componentmodel.typeconverter.nuspec"]}, "System.Console/4.3.0": {"sha512": "DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "type": "package", "path": "system.console/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Console.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Console.dll", "ref/netstandard1.3/System.Console.dll", "ref/netstandard1.3/System.Console.xml", "ref/netstandard1.3/de/System.Console.xml", "ref/netstandard1.3/es/System.Console.xml", "ref/netstandard1.3/fr/System.Console.xml", "ref/netstandard1.3/it/System.Console.xml", "ref/netstandard1.3/ja/System.Console.xml", "ref/netstandard1.3/ko/System.Console.xml", "ref/netstandard1.3/ru/System.Console.xml", "ref/netstandard1.3/zh-hans/System.Console.xml", "ref/netstandard1.3/zh-hant/System.Console.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.console.4.3.0.nupkg.sha512", "system.console.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/4.3.0": {"sha512": "tD6kosZnTAGdrEa0tZSuFyunMbt/5KYDnHdndJYGqZoNy00XVXyACd5d6KnE1YgYv3ne2CjtAfNXo/fwEhnKUA==", "type": "package", "path": "system.diagnostics.diagnosticsource/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec"]}, "System.Diagnostics.Tools/4.3.0": {"sha512": "UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "type": "package", "path": "system.diagnostics.tools/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Tools.dll", "ref/netcore50/System.Diagnostics.Tools.xml", "ref/netcore50/de/System.Diagnostics.Tools.xml", "ref/netcore50/es/System.Diagnostics.Tools.xml", "ref/netcore50/fr/System.Diagnostics.Tools.xml", "ref/netcore50/it/System.Diagnostics.Tools.xml", "ref/netcore50/ja/System.Diagnostics.Tools.xml", "ref/netcore50/ko/System.Diagnostics.Tools.xml", "ref/netcore50/ru/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tools.xml", "ref/netstandard1.0/System.Diagnostics.Tools.dll", "ref/netstandard1.0/System.Diagnostics.Tools.xml", "ref/netstandard1.0/de/System.Diagnostics.Tools.xml", "ref/netstandard1.0/es/System.Diagnostics.Tools.xml", "ref/netstandard1.0/fr/System.Diagnostics.Tools.xml", "ref/netstandard1.0/it/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ja/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ko/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ru/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Tools.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tools.4.3.0.nupkg.sha512", "system.diagnostics.tools.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Drawing.Common/9.0.4": {"sha512": "SbtusMUT1bCxZ14904ZPo2GZyelze0rwUni9wXrp8KX9Zlsda8idqpxra1RBvOA85WM0wW+fCI4GLrlCTYiE6A==", "type": "package", "path": "system.drawing.common/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/net8.0/System.Private.Windows.Core.dll", "lib/net8.0/System.Private.Windows.Core.xml", "lib/net9.0/System.Drawing.Common.dll", "lib/net9.0/System.Drawing.Common.pdb", "lib/net9.0/System.Drawing.Common.xml", "lib/net9.0/System.Private.Windows.Core.dll", "lib/net9.0/System.Private.Windows.Core.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.9.0.4.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Primitives/4.3.0": {"sha512": "1QU/c35gwdhvj77fkScXQQbjiVAqIL3fEYn/19NE0CV/ic5TN5PyWAft8HsrbRd4SBLEoErNCkWSzMDc0MmbRw==", "type": "package", "path": "system.drawing.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Drawing.Primitives.dll", "lib/netstandard1.1/System.Drawing.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Drawing.Primitives.dll", "ref/netstandard1.1/System.Drawing.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.drawing.primitives.4.3.0.nupkg.sha512", "system.drawing.primitives.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Calendars/4.3.0": {"sha512": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "type": "package", "path": "system.globalization.calendars/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Calendars.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.xml", "ref/netstandard1.3/de/System.Globalization.Calendars.xml", "ref/netstandard1.3/es/System.Globalization.Calendars.xml", "ref/netstandard1.3/fr/System.Globalization.Calendars.xml", "ref/netstandard1.3/it/System.Globalization.Calendars.xml", "ref/netstandard1.3/ja/System.Globalization.Calendars.xml", "ref/netstandard1.3/ko/System.Globalization.Calendars.xml", "ref/netstandard1.3/ru/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Calendars.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.calendars.4.3.0.nupkg.sha512", "system.globalization.calendars.nuspec"]}, "System.Globalization.Extensions/4.3.0": {"sha512": "FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "type": "package", "path": "system.globalization.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll", "system.globalization.extensions.4.3.0.nupkg.sha512", "system.globalization.extensions.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Compression/4.3.0": {"sha512": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "type": "package", "path": "system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll", "system.io.compression.4.3.0.nupkg.sha512", "system.io.compression.nuspec"]}, "System.IO.Compression.ZipFile/4.3.0": {"sha512": "G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "type": "package", "path": "system.io.compression.zipfile/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.Compression.ZipFile.dll", "lib/netstandard1.3/System.IO.Compression.ZipFile.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/de/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/es/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/fr/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/it/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ja/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ko/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ru/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.ZipFile.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.compression.zipfile.4.3.0.nupkg.sha512", "system.io.compression.zipfile.nuspec"]}, "System.IO.FileSystem/4.3.0": {"sha512": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "type": "package", "path": "system.io.filesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.3.0.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Net.Http/4.3.4": {"sha512": "aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "type": "package", "path": "system.net.http/4.3.4", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/net46/System.Net.Http.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/net46/System.Net.Http.dll", "ref/netcore50/System.Net.Http.dll", "ref/netstandard1.1/System.Net.Http.dll", "ref/netstandard1.3/System.Net.Http.dll", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Http.dll", "runtimes/win/lib/net46/System.Net.Http.dll", "runtimes/win/lib/netcore50/System.Net.Http.dll", "runtimes/win/lib/netstandard1.3/System.Net.Http.dll", "system.net.http.4.3.4.nupkg.sha512", "system.net.http.nuspec"]}, "System.Net.Primitives/4.3.0": {"sha512": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "type": "package", "path": "system.net.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.primitives.4.3.0.nupkg.sha512", "system.net.primitives.nuspec"]}, "System.Net.Sockets/4.3.0": {"sha512": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "type": "package", "path": "system.net.sockets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Sockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.xml", "ref/netstandard1.3/de/System.Net.Sockets.xml", "ref/netstandard1.3/es/System.Net.Sockets.xml", "ref/netstandard1.3/fr/System.Net.Sockets.xml", "ref/netstandard1.3/it/System.Net.Sockets.xml", "ref/netstandard1.3/ja/System.Net.Sockets.xml", "ref/netstandard1.3/ko/System.Net.Sockets.xml", "ref/netstandard1.3/ru/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hans/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hant/System.Net.Sockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.sockets.4.3.0.nupkg.sha512", "system.net.sockets.nuspec"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Private.DataContractSerialization/4.3.0": {"sha512": "yDaJ2x3mMmjdZEDB4IbezSnCsnjQ4BxinKhRAaP6kEgL6Bb6jANWphs5SzyD8imqeC/3FxgsuXT6ykkiH1uUmA==", "type": "package", "path": "system.private.datacontractserialization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.3/System.Private.DataContractSerialization.dll", "ref/netstandard/_._", "runtimes/aot/lib/netcore50/System.Private.DataContractSerialization.dll", "system.private.datacontractserialization.4.3.0.nupkg.sha512", "system.private.datacontractserialization.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.1": {"sha512": "abhfv1dTK6NXOmu4bgHIONxHyEqFjW8HwXPmpY9gmll+ix9UNo4XDcmzJn6oLooftxNssVHdJC1pGT9jkSynQg==", "type": "package", "path": "system.runtime/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.1.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"sha512": "wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.5.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"sha512": "cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Runtime.Numerics/4.3.0": {"sha512": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "type": "package", "path": "system.runtime.numerics/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.numerics.4.3.0.nupkg.sha512", "system.runtime.numerics.nuspec"]}, "System.Runtime.Serialization.Primitives/4.3.0": {"sha512": "Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "type": "package", "path": "system.runtime.serialization.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Primitives.dll", "lib/netcore50/System.Runtime.Serialization.Primitives.dll", "lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/de/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/es/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/fr/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/it/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ja/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ko/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ru/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.Serialization.Primitives.dll", "system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "system.runtime.serialization.primitives.nuspec"]}, "System.Runtime.Serialization.Xml/4.3.0": {"sha512": "nUQx/5OVgrqEba3+j7OdiofvVq9koWZAC7Z3xGI8IIViZqApWnZ5+lLcwYgTlbkobrl/Rat+Jb8GeD4WQESD2A==", "type": "package", "path": "system.runtime.serialization.xml/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Xml.dll", "lib/netcore50/System.Runtime.Serialization.Xml.dll", "lib/netstandard1.3/System.Runtime.Serialization.Xml.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Xml.dll", "ref/netcore50/System.Runtime.Serialization.Xml.dll", "ref/netcore50/System.Runtime.Serialization.Xml.xml", "ref/netcore50/de/System.Runtime.Serialization.Xml.xml", "ref/netcore50/es/System.Runtime.Serialization.Xml.xml", "ref/netcore50/fr/System.Runtime.Serialization.Xml.xml", "ref/netcore50/it/System.Runtime.Serialization.Xml.xml", "ref/netcore50/ja/System.Runtime.Serialization.Xml.xml", "ref/netcore50/ko/System.Runtime.Serialization.Xml.xml", "ref/netcore50/ru/System.Runtime.Serialization.Xml.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Xml.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/System.Runtime.Serialization.Xml.dll", "ref/netstandard1.0/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/System.Runtime.Serialization.Xml.dll", "ref/netstandard1.3/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Xml.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Xml.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.serialization.xml.4.3.0.nupkg.sha512", "system.runtime.serialization.xml.nuspec"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Algorithms/4.3.0": {"sha512": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Cng/4.3.0": {"sha512": "03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "type": "package", "path": "system.security.cryptography.cng/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net463/System.Security.Cryptography.Cng.dll", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net463/System.Security.Cryptography.Cng.dll", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "system.security.cryptography.cng.4.3.0.nupkg.sha512", "system.security.cryptography.cng.nuspec"]}, "System.Security.Cryptography.Csp/4.3.0": {"sha512": "X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "type": "package", "path": "system.security.cryptography.csp/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Csp.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Csp.dll", "ref/netstandard1.3/System.Security.Cryptography.Csp.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "system.security.cryptography.csp.4.3.0.nupkg.sha512", "system.security.cryptography.csp.nuspec"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.OpenSsl/4.3.0": {"sha512": "h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "type": "package", "path": "system.security.cryptography.openssl/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "ref/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "system.security.cryptography.openssl.4.3.0.nupkg.sha512", "system.security.cryptography.openssl.nuspec"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.X509Certificates/4.3.0": {"sha512": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "type": "package", "path": "system.security.cryptography.x509certificates/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "system.security.cryptography.x509certificates.nuspec"]}, "System.Security.Permissions/4.7.0": {"sha512": "dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "type": "package", "path": "system.security.permissions/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Security.Permissions.dll", "lib/net461/System.Security.Permissions.xml", "lib/netcoreapp3.0/System.Security.Permissions.dll", "lib/netcoreapp3.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "ref/net461/System.Security.Permissions.dll", "ref/net461/System.Security.Permissions.xml", "ref/netcoreapp3.0/System.Security.Permissions.dll", "ref/netcoreapp3.0/System.Security.Permissions.xml", "ref/netstandard2.0/System.Security.Permissions.dll", "ref/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.4.7.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.Extensions/4.3.0": {"sha512": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "type": "package", "path": "system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.3.0.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.RegularExpressions/4.3.1": {"sha512": "N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "type": "package", "path": "system.text.regularexpressions/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netcoreapp1.1/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.3.1.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.3.0": {"sha512": "npvJkVKl5rKXrtl1Kkm6OhOUaYGEiF9wFbppFRWSMoApKzt2PiPHT2Bb8a5sAWxprvdOAtvaARS9QYMznEUtug==", "type": "package", "path": "system.threading.tasks.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "system.threading.tasks.extensions.4.3.0.nupkg.sha512", "system.threading.tasks.extensions.nuspec"]}, "System.Threading.Tasks.Parallel/4.3.0": {"sha512": "cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "type": "package", "path": "system.threading.tasks.parallel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.Tasks.Parallel.dll", "lib/netstandard1.3/System.Threading.Tasks.Parallel.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.Parallel.dll", "ref/netcore50/System.Threading.Tasks.Parallel.xml", "ref/netcore50/de/System.Threading.Tasks.Parallel.xml", "ref/netcore50/es/System.Threading.Tasks.Parallel.xml", "ref/netcore50/fr/System.Threading.Tasks.Parallel.xml", "ref/netcore50/it/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ja/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ko/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ru/System.Threading.Tasks.Parallel.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.Parallel.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/System.Threading.Tasks.Parallel.dll", "ref/netstandard1.1/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/de/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/es/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/fr/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/it/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ja/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ko/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ru/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/zh-hans/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/zh-hant/System.Threading.Tasks.Parallel.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.parallel.4.3.0.nupkg.sha512", "system.threading.tasks.parallel.nuspec"]}, "System.Threading.Timer/4.3.0": {"sha512": "Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "type": "package", "path": "system.threading.timer/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/_._", "lib/portable-net451+win81+wpa81/_._", "lib/win81/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/_._", "ref/netcore50/System.Threading.Timer.dll", "ref/netcore50/System.Threading.Timer.xml", "ref/netcore50/de/System.Threading.Timer.xml", "ref/netcore50/es/System.Threading.Timer.xml", "ref/netcore50/fr/System.Threading.Timer.xml", "ref/netcore50/it/System.Threading.Timer.xml", "ref/netcore50/ja/System.Threading.Timer.xml", "ref/netcore50/ko/System.Threading.Timer.xml", "ref/netcore50/ru/System.Threading.Timer.xml", "ref/netcore50/zh-hans/System.Threading.Timer.xml", "ref/netcore50/zh-hant/System.Threading.Timer.xml", "ref/netstandard1.2/System.Threading.Timer.dll", "ref/netstandard1.2/System.Threading.Timer.xml", "ref/netstandard1.2/de/System.Threading.Timer.xml", "ref/netstandard1.2/es/System.Threading.Timer.xml", "ref/netstandard1.2/fr/System.Threading.Timer.xml", "ref/netstandard1.2/it/System.Threading.Timer.xml", "ref/netstandard1.2/ja/System.Threading.Timer.xml", "ref/netstandard1.2/ko/System.Threading.Timer.xml", "ref/netstandard1.2/ru/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hans/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hant/System.Threading.Timer.xml", "ref/portable-net451+win81+wpa81/_._", "ref/win81/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.timer.4.3.0.nupkg.sha512", "system.threading.timer.nuspec"]}, "System.Windows.Extensions/4.7.0": {"sha512": "CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "type": "package", "path": "system.windows.extensions/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp3.0/System.Windows.Extensions.dll", "lib/netcoreapp3.0/System.Windows.Extensions.xml", "ref/netcoreapp3.0/System.Windows.Extensions.dll", "ref/netcoreapp3.0/System.Windows.Extensions.xml", "runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll", "runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.xml", "system.windows.extensions.4.7.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Xml.ReaderWriter/4.3.0": {"sha512": "GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "type": "package", "path": "system.xml.readerwriter/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Xml.ReaderWriter.dll", "lib/netcore50/System.Xml.ReaderWriter.dll", "lib/netstandard1.3/System.Xml.ReaderWriter.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.xml", "ref/netcore50/de/System.Xml.ReaderWriter.xml", "ref/netcore50/es/System.Xml.ReaderWriter.xml", "ref/netcore50/fr/System.Xml.ReaderWriter.xml", "ref/netcore50/it/System.Xml.ReaderWriter.xml", "ref/netcore50/ja/System.Xml.ReaderWriter.xml", "ref/netcore50/ko/System.Xml.ReaderWriter.xml", "ref/netcore50/ru/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hans/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/System.Xml.ReaderWriter.dll", "ref/netstandard1.0/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/System.Xml.ReaderWriter.dll", "ref/netstandard1.3/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hant/System.Xml.ReaderWriter.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.readerwriter.4.3.0.nupkg.sha512", "system.xml.readerwriter.nuspec"]}, "System.Xml.XDocument/4.3.0": {"sha512": "5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "type": "package", "path": "system.xml.xdocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XDocument.dll", "lib/netstandard1.3/System.Xml.XDocument.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XDocument.dll", "ref/netcore50/System.Xml.XDocument.xml", "ref/netcore50/de/System.Xml.XDocument.xml", "ref/netcore50/es/System.Xml.XDocument.xml", "ref/netcore50/fr/System.Xml.XDocument.xml", "ref/netcore50/it/System.Xml.XDocument.xml", "ref/netcore50/ja/System.Xml.XDocument.xml", "ref/netcore50/ko/System.Xml.XDocument.xml", "ref/netcore50/ru/System.Xml.XDocument.xml", "ref/netcore50/zh-hans/System.Xml.XDocument.xml", "ref/netcore50/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.0/System.Xml.XDocument.dll", "ref/netstandard1.0/System.Xml.XDocument.xml", "ref/netstandard1.0/de/System.Xml.XDocument.xml", "ref/netstandard1.0/es/System.Xml.XDocument.xml", "ref/netstandard1.0/fr/System.Xml.XDocument.xml", "ref/netstandard1.0/it/System.Xml.XDocument.xml", "ref/netstandard1.0/ja/System.Xml.XDocument.xml", "ref/netstandard1.0/ko/System.Xml.XDocument.xml", "ref/netstandard1.0/ru/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.3/System.Xml.XDocument.dll", "ref/netstandard1.3/System.Xml.XDocument.xml", "ref/netstandard1.3/de/System.Xml.XDocument.xml", "ref/netstandard1.3/es/System.Xml.XDocument.xml", "ref/netstandard1.3/fr/System.Xml.XDocument.xml", "ref/netstandard1.3/it/System.Xml.XDocument.xml", "ref/netstandard1.3/ja/System.Xml.XDocument.xml", "ref/netstandard1.3/ko/System.Xml.XDocument.xml", "ref/netstandard1.3/ru/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XDocument.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xdocument.4.3.0.nupkg.sha512", "system.xml.xdocument.nuspec"]}, "System.Xml.XmlDocument/4.3.0": {"sha512": "lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "type": "package", "path": "system.xml.xmldocument/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XmlDocument.dll", "lib/netstandard1.3/System.Xml.XmlDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.xml", "ref/netstandard1.3/de/System.Xml.XmlDocument.xml", "ref/netstandard1.3/es/System.Xml.XmlDocument.xml", "ref/netstandard1.3/fr/System.Xml.XmlDocument.xml", "ref/netstandard1.3/it/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ja/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ko/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ru/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xmldocument.4.3.0.nupkg.sha512", "system.xml.xmldocument.nuspec"]}, "System.Xml.XmlSerializer/4.3.0": {"sha512": "MYoTCP7EZ98RrANESW05J5ZwskKDoN0AuZ06ZflnowE50LTpbR5yRg3tHckTVm5j/m47stuGgCrCHWePyHS70Q==", "type": "package", "path": "system.xml.xmlserializer/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XmlSerializer.dll", "lib/netstandard1.3/System.Xml.XmlSerializer.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XmlSerializer.dll", "ref/netcore50/System.Xml.XmlSerializer.xml", "ref/netcore50/de/System.Xml.XmlSerializer.xml", "ref/netcore50/es/System.Xml.XmlSerializer.xml", "ref/netcore50/fr/System.Xml.XmlSerializer.xml", "ref/netcore50/it/System.Xml.XmlSerializer.xml", "ref/netcore50/ja/System.Xml.XmlSerializer.xml", "ref/netcore50/ko/System.Xml.XmlSerializer.xml", "ref/netcore50/ru/System.Xml.XmlSerializer.xml", "ref/netcore50/zh-hans/System.Xml.XmlSerializer.xml", "ref/netcore50/zh-hant/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/System.Xml.XmlSerializer.dll", "ref/netstandard1.0/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/de/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/es/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/fr/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/it/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ja/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ko/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/ru/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/zh-hans/System.Xml.XmlSerializer.xml", "ref/netstandard1.0/zh-hant/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/System.Xml.XmlSerializer.dll", "ref/netstandard1.3/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/de/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/es/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/fr/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/it/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ja/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ko/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/ru/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlSerializer.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlSerializer.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Xml.XmlSerializer.dll", "system.xml.xmlserializer.4.3.0.nupkg.sha512", "system.xml.xmlserializer.nuspec"]}, "Validation/2.5.42": {"sha512": "lzd+CAUssDzD1A3R+iqqgzRkkNtLpNNsund1aUXZP/sKD2nxnex6nQ55C8uWKEQjNYKe8/LpBp0HLRNuBsxKQQ==", "type": "package", "path": "validation/2.5.42", "files": [".nupkg.metadata", ".signature.p7s", "3rdPartyNotices.txt", "README.md", "lib/net45/Validation.dll", "lib/net45/Validation.xml", "lib/netstandard2.0/Validation.dll", "lib/netstandard2.0/Validation.xml", "validation.2.5.42.nupkg.sha512", "validation.nuspec"]}, "Vanara.Core/4.1.2": {"sha512": "6WPBAY7JLL8Lev8BNMnnzhu22ANBaxr3yvres3Vcjz2Whmn4ND3v4o2ZwXte5MdmgBKjm6g6gh0yoeFAZxuzLQ==", "type": "package", "path": "vanara.core/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.Core.dll", "lib/net48/Vanara.Core.xml", "lib/net48/fr/Vanara.Core.resources.dll", "lib/net5.0/Vanara.Core.dll", "lib/net5.0/Vanara.Core.xml", "lib/net5.0/fr/Vanara.Core.resources.dll", "lib/net6.0/Vanara.Core.dll", "lib/net6.0/Vanara.Core.xml", "lib/net6.0/fr/Vanara.Core.resources.dll", "lib/net7.0/Vanara.Core.dll", "lib/net7.0/Vanara.Core.xml", "lib/net7.0/fr/Vanara.Core.resources.dll", "lib/net8.0-windows7.0/Vanara.Core.dll", "lib/net8.0-windows7.0/Vanara.Core.xml", "lib/net8.0-windows7.0/fr/Vanara.Core.resources.dll", "lib/net9.0-windows7.0/Vanara.Core.dll", "lib/net9.0-windows7.0/Vanara.Core.xml", "lib/net9.0-windows7.0/fr/Vanara.Core.resources.dll", "lib/netcoreapp3.1/Vanara.Core.dll", "lib/netcoreapp3.1/Vanara.Core.xml", "lib/netcoreapp3.1/fr/Vanara.Core.resources.dll", "lib/netstandard2.0/Vanara.Core.dll", "lib/netstandard2.0/Vanara.Core.xml", "lib/netstandard2.0/fr/Vanara.Core.resources.dll", "pkgreadme.md", "vanara.core.4.1.2.nupkg.sha512", "vanara.core.nuspec"]}, "Vanara.PInvoke.Cryptography/4.1.2": {"sha512": "hIxXBA/lEU5CsVKAnc80LZhNOsMX37yhnHRh6J9+4BYvIReDWpaqz5WoYwXF/KEYwMjldykAf65sgbQLL9HocA==", "type": "package", "path": "vanara.pinvoke.cryptography/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Cryptography.dll", "lib/net48/Vanara.PInvoke.Cryptography.xml", "lib/net5.0/Vanara.PInvoke.Cryptography.dll", "lib/net5.0/Vanara.PInvoke.Cryptography.xml", "lib/net6.0/Vanara.PInvoke.Cryptography.dll", "lib/net6.0/Vanara.PInvoke.Cryptography.xml", "lib/net7.0/Vanara.PInvoke.Cryptography.dll", "lib/net7.0/Vanara.PInvoke.Cryptography.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Cryptography.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Cryptography.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Cryptography.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Cryptography.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Cryptography.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Cryptography.xml", "lib/netstandard2.0/Vanara.PInvoke.Cryptography.dll", "lib/netstandard2.0/Vanara.PInvoke.Cryptography.xml", "pkgreadme.md", "vanara.pinvoke.cryptography.4.1.2.nupkg.sha512", "vanara.pinvoke.cryptography.nuspec"]}, "Vanara.PInvoke.Gdi32/4.1.2": {"sha512": "O4LCYVvOogGBx8JZCPcH8l97Iy8qgQjPyX+UJVEv193aoKHeuXYxOxfMZD41TD08nkYmTOK04KnWKs8uOUI/DQ==", "type": "package", "path": "vanara.pinvoke.gdi32/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Gdi32.dll", "lib/net48/Vanara.PInvoke.Gdi32.xml", "lib/net5.0/Vanara.PInvoke.Gdi32.dll", "lib/net5.0/Vanara.PInvoke.Gdi32.xml", "lib/net6.0/Vanara.PInvoke.Gdi32.dll", "lib/net6.0/Vanara.PInvoke.Gdi32.xml", "lib/net7.0/Vanara.PInvoke.Gdi32.dll", "lib/net7.0/Vanara.PInvoke.Gdi32.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Gdi32.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Gdi32.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Gdi32.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Gdi32.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Gdi32.xml", "lib/netstandard2.0/Vanara.PInvoke.Gdi32.dll", "lib/netstandard2.0/Vanara.PInvoke.Gdi32.xml", "pkgreadme.md", "vanara.pinvoke.gdi32.4.1.2.nupkg.sha512", "vanara.pinvoke.gdi32.nuspec"]}, "Vanara.PInvoke.Kernel32/4.1.2": {"sha512": "anuovG4HCfRM449lr3p8xVfAOIHb4Spj4ThFsm/psyTGWYPlYa4/By61vzu41zhd8mnZ/ELzdCuj/VfZZmD5xQ==", "type": "package", "path": "vanara.pinvoke.kernel32/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Kernel32.dll", "lib/net48/Vanara.PInvoke.Kernel32.xml", "lib/net5.0/Vanara.PInvoke.Kernel32.dll", "lib/net5.0/Vanara.PInvoke.Kernel32.xml", "lib/net6.0/Vanara.PInvoke.Kernel32.dll", "lib/net6.0/Vanara.PInvoke.Kernel32.xml", "lib/net7.0/Vanara.PInvoke.Kernel32.dll", "lib/net7.0/Vanara.PInvoke.Kernel32.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Kernel32.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Kernel32.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Kernel32.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Kernel32.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Kernel32.xml", "lib/netstandard2.0/Vanara.PInvoke.Kernel32.dll", "lib/netstandard2.0/Vanara.PInvoke.Kernel32.xml", "pkgreadme.md", "vanara.pinvoke.kernel32.4.1.2.nupkg.sha512", "vanara.pinvoke.kernel32.nuspec"]}, "Vanara.PInvoke.Ole/4.1.2": {"sha512": "w4m9+VzxnRqTuyXGpxo/RTNMr34ekM/B21a+J35xRoN326PSghx1L7H78fEzdSoNtK5Jx1NvFe71wc0Y0GsXgw==", "type": "package", "path": "vanara.pinvoke.ole/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Ole.dll", "lib/net48/Vanara.PInvoke.Ole.xml", "lib/net5.0/Vanara.PInvoke.Ole.dll", "lib/net5.0/Vanara.PInvoke.Ole.xml", "lib/net6.0/Vanara.PInvoke.Ole.dll", "lib/net6.0/Vanara.PInvoke.Ole.xml", "lib/net7.0/Vanara.PInvoke.Ole.dll", "lib/net7.0/Vanara.PInvoke.Ole.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Ole.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Ole.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Ole.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Ole.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Ole.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Ole.xml", "lib/netstandard2.0/Vanara.PInvoke.Ole.dll", "lib/netstandard2.0/Vanara.PInvoke.Ole.xml", "pkgreadme.md", "vanara.pinvoke.ole.4.1.2.nupkg.sha512", "vanara.pinvoke.ole.nuspec"]}, "Vanara.PInvoke.Rpc/4.1.2": {"sha512": "QHhj3bk5AfkZ/35SgJMvXIy3+CCwNHIV6zAQWIJH1UVHfHX2PSF8mONHTmQ/ixaJam933lykFw9cDPCQd9txZQ==", "type": "package", "path": "vanara.pinvoke.rpc/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Rpc.dll", "lib/net48/Vanara.PInvoke.Rpc.xml", "lib/net5.0/Vanara.PInvoke.Rpc.dll", "lib/net5.0/Vanara.PInvoke.Rpc.xml", "lib/net6.0/Vanara.PInvoke.Rpc.dll", "lib/net6.0/Vanara.PInvoke.Rpc.xml", "lib/net7.0/Vanara.PInvoke.Rpc.dll", "lib/net7.0/Vanara.PInvoke.Rpc.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Rpc.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Rpc.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Rpc.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Rpc.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Rpc.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Rpc.xml", "lib/netstandard2.0/Vanara.PInvoke.Rpc.dll", "lib/netstandard2.0/Vanara.PInvoke.Rpc.xml", "pkgreadme.md", "vanara.pinvoke.rpc.4.1.2.nupkg.sha512", "vanara.pinvoke.rpc.nuspec"]}, "Vanara.PInvoke.Security/4.1.2": {"sha512": "dF8RT6hbCbkDQGTWB9114m9UvfzPZhH6kmwgxYbzaH7vEjkAlolHddqFrc9HTHlFTahbRcp50Iw3noEE6pPSmw==", "type": "package", "path": "vanara.pinvoke.security/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Security.dll", "lib/net48/Vanara.PInvoke.Security.xml", "lib/net5.0/Vanara.PInvoke.Security.dll", "lib/net5.0/Vanara.PInvoke.Security.xml", "lib/net6.0/Vanara.PInvoke.Security.dll", "lib/net6.0/Vanara.PInvoke.Security.xml", "lib/net7.0/Vanara.PInvoke.Security.dll", "lib/net7.0/Vanara.PInvoke.Security.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Security.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Security.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Security.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Security.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Security.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Security.xml", "lib/netstandard2.0/Vanara.PInvoke.Security.dll", "lib/netstandard2.0/Vanara.PInvoke.Security.xml", "pkgreadme.md", "vanara.pinvoke.security.4.1.2.nupkg.sha512", "vanara.pinvoke.security.nuspec"]}, "Vanara.PInvoke.Shared/4.1.2": {"sha512": "Q8RWA+mR8XOnFTj/a+HKgmMbw5y8+awy3QjqA2y8EhlIrposRE3jrR51a7f7uJpdhVAVJEKuW4gVXiq+U5v2Jg==", "type": "package", "path": "vanara.pinvoke.shared/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.Shared.dll", "lib/net48/Vanara.PInvoke.Shared.xml", "lib/net5.0/Vanara.PInvoke.Shared.dll", "lib/net5.0/Vanara.PInvoke.Shared.xml", "lib/net6.0/Vanara.PInvoke.Shared.dll", "lib/net6.0/Vanara.PInvoke.Shared.xml", "lib/net7.0/Vanara.PInvoke.Shared.dll", "lib/net7.0/Vanara.PInvoke.Shared.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.Shared.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.Shared.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.Shared.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.Shared.xml", "lib/netcoreapp3.1/Vanara.PInvoke.Shared.dll", "lib/netcoreapp3.1/Vanara.PInvoke.Shared.xml", "lib/netstandard2.0/Vanara.PInvoke.Shared.dll", "lib/netstandard2.0/Vanara.PInvoke.Shared.xml", "pkgreadme.md", "vanara.pinvoke.shared.4.1.2.nupkg.sha512", "vanara.pinvoke.shared.nuspec"]}, "Vanara.PInvoke.User32/4.1.2": {"sha512": "T46a4s74TnHVFfDndtgrBU2R62fo3+3YoUDmEjVgIlLixCBMyFWK7cCl+kMl0ekn0Jye/KjQbFgM1wXqjxPWvQ==", "type": "package", "path": "vanara.pinvoke.user32/4.1.2", "files": [".nupkg.metadata", ".signature.p7s", "Vanara64x64.png", "lib/net48/Vanara.PInvoke.User32.dll", "lib/net48/Vanara.PInvoke.User32.xml", "lib/net5.0/Vanara.PInvoke.User32.dll", "lib/net5.0/Vanara.PInvoke.User32.xml", "lib/net6.0/Vanara.PInvoke.User32.dll", "lib/net6.0/Vanara.PInvoke.User32.xml", "lib/net7.0/Vanara.PInvoke.User32.dll", "lib/net7.0/Vanara.PInvoke.User32.xml", "lib/net8.0-windows7.0/Vanara.PInvoke.User32.dll", "lib/net8.0-windows7.0/Vanara.PInvoke.User32.xml", "lib/net9.0-windows7.0/Vanara.PInvoke.User32.dll", "lib/net9.0-windows7.0/Vanara.PInvoke.User32.xml", "lib/netcoreapp3.1/Vanara.PInvoke.User32.dll", "lib/netcoreapp3.1/Vanara.PInvoke.User32.xml", "lib/netstandard2.0/Vanara.PInvoke.User32.dll", "lib/netstandard2.0/Vanara.PInvoke.User32.xml", "pkgreadme.md", "vanara.pinvoke.user32.4.1.2.nupkg.sha512", "vanara.pinvoke.user32.nuspec"]}, "Vortice.D3DCompiler/3.6.2": {"sha512": "h5PSVwQpgUs5+4QP5bJEuVyEUd0liz7pVcSayZ5JRlT8veGiaosK3EdFwGqZ+vnj+dkjlyS7+sA+18w+hB9fcA==", "type": "package", "path": "vortice.d3dcompiler/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.D3DCompiler.BindMapping.xml", "build/Vortice.D3DCompiler.props", "buildMultiTargeting/Vortice.D3DCompiler.props", "lib/net8.0/Vortice.D3DCompiler.dll", "lib/net8.0/Vortice.D3DCompiler.xml", "vortice.d3dcompiler.3.6.2.nupkg.sha512", "vortice.d3dcompiler.nuspec"]}, "Vortice.Direct2D1/3.6.2": {"sha512": "axuDhGNPkdLwSzlcUAWr/Ju6s2tvnVKxM4MSl9zYcPePiUOTsKF39oByBZSjXllkQY01WG2o4+p00TZz/dL57Q==", "type": "package", "path": "vortice.direct2d1/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.Direct2D1.BindMapping.xml", "build/Vortice.Direct2D1.props", "buildMultiTargeting/Vortice.Direct2D1.props", "lib/net8.0/Vortice.Direct2D1.dll", "lib/net8.0/Vortice.Direct2D1.xml", "vortice.direct2d1.3.6.2.nupkg.sha512", "vortice.direct2d1.nuspec"]}, "Vortice.Direct3D11/3.6.2": {"sha512": "+tXkwV4YVFgox3NjLGBOPmEHK8Jd/8ElI3X8aX7xq4JhsC3J9BqpUhpI+4LOKeCGN+XCORhyFq0ow+pB44YQTg==", "type": "package", "path": "vortice.direct3d11/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.Direct3D11.BindMapping.xml", "build/Vortice.Direct3D11.props", "buildMultiTargeting/Vortice.Direct3D11.props", "lib/net8.0/Vortice.Direct3D11.dll", "lib/net8.0/Vortice.Direct3D11.xml", "vortice.direct3d11.3.6.2.nupkg.sha512", "vortice.direct3d11.nuspec"]}, "Vortice.Direct3D12/3.6.2": {"sha512": "fr9VpWkTfH1Jio5jvP0WBGcoZo+2hy2DUVHTYQMLL95KsCTc9PsG0rdtToolaQvorKFbF6hY2npZRiTy5wnxMA==", "type": "package", "path": "vortice.direct3d12/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.Direct3D12.BindMapping.xml", "build/Vortice.Direct3D12.props", "buildMultiTargeting/Vortice.Direct3D12.props", "lib/net8.0/Vortice.Direct3D12.dll", "lib/net8.0/Vortice.Direct3D12.xml", "vortice.direct3d12.3.6.2.nupkg.sha512", "vortice.direct3d12.nuspec"]}, "Vortice.DirectML/3.6.2": {"sha512": "VKKEW2V4lbkVPcaV2xnuaHLmvIrHToshi89kNkA0htSc46suADMU2Jo6SFlgqzM/byWgRGmoalZDrS6hcMOHkQ==", "type": "package", "path": "vortice.directml/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DirectML.BindMapping.xml", "build/Vortice.DirectML.props", "buildMultiTargeting/Vortice.DirectML.props", "lib/net8.0/Vortice.DirectML.dll", "lib/net8.0/Vortice.DirectML.xml", "runtimes/LICENSE.txt", "runtimes/win-arm/DirectML.Debug.dll", "runtimes/win-arm/DirectML.dll", "runtimes/win-arm64/DirectML.Debug.dll", "runtimes/win-arm64/DirectML.dll", "runtimes/win-x64/DirectML.Debug.dll", "runtimes/win-x64/DirectML.dll", "runtimes/win-x86/DirectML.Debug.dll", "runtimes/win-x86/DirectML.dll", "vortice.directml.3.6.2.nupkg.sha512", "vortice.directml.nuspec"]}, "Vortice.DirectX/3.6.2": {"sha512": "pa/97U5IHascS/UJzMWFSKIs3wxh36zc8JpI+fzi/JLQk4wfTZur0n/Y9Lcp8i7hsf613Vu/6n6IImWSc9wupw==", "type": "package", "path": "vortice.directx/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DirectX.BindMapping.xml", "build/Vortice.DirectX.props", "buildMultiTargeting/Vortice.DirectX.props", "lib/net8.0-windows10.0.19041/Vortice.DirectX.dll", "lib/net8.0-windows10.0.19041/Vortice.DirectX.xml", "lib/net8.0/Vortice.DirectX.dll", "lib/net8.0/Vortice.DirectX.xml", "vortice.directx.3.6.2.nupkg.sha512", "vortice.directx.nuspec"]}, "Vortice.Dxc/3.6.2": {"sha512": "266EVwYCj1FU9eDRRnq05Ovu/32/V3ipbStYkmnS0rvVMUPio0okJ9DhfswVoFPJi1OLjAoP5Y9icQ9vmy3nzQ==", "type": "package", "path": "vortice.dxc/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.Dxc.BindMapping.xml", "build/Vortice.Dxc.props", "buildMultiTargeting/Vortice.Dxc.props", "lib/net8.0/Vortice.Dxc.dll", "lib/net8.0/Vortice.Dxc.xml", "vortice.dxc.3.6.2.nupkg.sha512", "vortice.dxc.nuspec"]}, "Vortice.Dxc.Native/1.0.2": {"sha512": "8deBQXEgEhbHvo6eQJyzHVKNja+yndSug6oFkf83aaBRr4GqbrlutgwyVsoUEDGCamvc6rEPDagkGeySuf2XfA==", "type": "package", "path": "vortice.dxc.native/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE-LLVM.txt", "LICENSE-MIT.txt", "LICENSE-MS.txt", "README.md", "build/net461/Vortice.Dxc.Native.targets", "lib/net461/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "runtimes/linux-x64/native/libdxcompiler.so", "runtimes/linux-x64/native/libdxil.so", "runtimes/win-arm64/native/dxcompiler.dll", "runtimes/win-arm64/native/dxil.dll", "runtimes/win-x64/native/dxcompiler.dll", "runtimes/win-x64/native/dxil.dll", "vortice.dxc.native.1.0.2.nupkg.sha512", "vortice.dxc.native.nuspec"]}, "Vortice.DXGI/3.6.2": {"sha512": "SPnBb1x3+CEqq7L5SEejElD2tMjkdE+bqN4c/eSNHj9WEpRPvAO0QFSEN8d9LiRDt9i9geD8m7E4s8sLMgz2qQ==", "type": "package", "path": "vortice.dxgi/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DXGI.BindMapping.xml", "build/Vortice.DXGI.props", "buildMultiTargeting/Vortice.DXGI.props", "lib/net8.0-windows10.0.19041/Vortice.DXGI.dll", "lib/net8.0-windows10.0.19041/Vortice.DXGI.xml", "lib/net8.0/Vortice.DXGI.dll", "lib/net8.0/Vortice.DXGI.xml", "vortice.dxgi.3.6.2.nupkg.sha512", "vortice.dxgi.nuspec"]}, "Vortice.Mathematics/1.9.3": {"sha512": "VzXWjzM4F5pK3gP3m0wv9LqhRrcmQebf+lK/gIPLZ+TPXTptMal74iGkQbivIJm9OwdMZdGOuk1dvwc2iJHUGQ==", "type": "package", "path": "vortice.mathematics/1.9.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.dll", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.pdb", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.xml", "lib/net8.0/Vortice.Mathematics.dll", "lib/net8.0/Vortice.Mathematics.pdb", "lib/net8.0/Vortice.Mathematics.xml", "lib/net9.0-windows10.0.19041/Vortice.Mathematics.dll", "lib/net9.0-windows10.0.19041/Vortice.Mathematics.pdb", "lib/net9.0-windows10.0.19041/Vortice.Mathematics.xml", "lib/net9.0/Vortice.Mathematics.dll", "lib/net9.0/Vortice.Mathematics.pdb", "lib/net9.0/Vortice.Mathematics.xml", "vortice.mathematics.1.9.3.nupkg.sha512", "vortice.mathematics.nuspec"]}, "Vortice.XAudio2/3.6.2": {"sha512": "i0ZNEolel3NdvgpSRvpBuJSiwAaMqqyRdvBK2GSv4r8wQuWDyBC7qwgCj2d1ae7d6Xytzqb2RNbEX3DTe/acJg==", "type": "package", "path": "vortice.xaudio2/3.6.2", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.XAudio2.BindMapping.xml", "build/Vortice.XAudio2.props", "buildMultiTargeting/Vortice.XAudio2.props", "lib/net8.0/Vortice.XAudio2.dll", "lib/net8.0/Vortice.XAudio2.xml", "vortice.xaudio2.3.6.2.nupkg.sha512", "vortice.xaudio2.nuspec"]}, "DirectX/1.0.0": {"type": "project", "path": "../../DirectX/DirectX.csproj", "msbuildProject": "../../DirectX/DirectX.csproj"}, "SharpSvg/1.0.0": {"type": "project", "path": "../../SharpSvg/SharpSvg.csproj", "msbuildProject": "../../SharpSvg/SharpSvg.csproj"}, "UserControls/1.0.0": {"type": "project", "path": "../../UserControls/UserControls.csproj", "msbuildProject": "../../UserControls/UserControls.csproj"}, "Utils/1.0.0": {"type": "project", "path": "../../Utils/Utils.csproj", "msbuildProject": "../../Utils/Utils.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["ComputeSharp >= 3.1.0", "ComputeSharp.D2D1 >= 3.1.0", "ComputeSharp.Dxc >= 3.1.0", "ConcurrencyVisualizer >= 3.0.0", "DirectX >= 1.0.0", "Lennox.NvEncSharp >= 2.0.0", "MediaFoundation.NetCore >= 2024.5.10", "Quamotion.TurboJpegWrapper >= 2.0.32", "SharpGen.Runtime.COM >= 2.2.0-beta", "System.Text.RegularExpressions >= 4.3.1", "UserControls >= 1.0.0", "Utils >= 1.0.0", "Vortice.D3DCompiler >= 3.6.2", "Vortice.DXGI >= 3.6.2", "Vortice.Direct2D1 >= 3.6.2", "Vortice.Direct3D11 >= 3.6.2", "Vortice.Direct3D12 >= 3.6.2", "Vortice.DirectML >= 3.6.2", "Vortice.DirectX >= 3.6.2", "Vortice.Dxc >= 3.6.2", "Vortice.Mathematics >= 1.9.3"]}, "packageFolders": {"F:\\NugetPackages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondSghm\\TestSuppressionFondSghm.csproj", "projectName": "TestSuppressionFondSghm", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondSghm\\TestSuppressionFondSghm.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\TestSuppressionFondSghm\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows7.0", "projectReferences": {"F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\DirectX\\DirectX.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\UserControls\\UserControls.csproj"}, "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj": {"projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Utils\\Utils.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows7.0", "dependencies": {"ComputeSharp": {"target": "Package", "version": "[3.1.0, )"}, "ComputeSharp.D2D1": {"target": "Package", "version": "[3.1.0, )"}, "ComputeSharp.Dxc": {"target": "Package", "version": "[3.1.0, )"}, "ConcurrencyVisualizer": {"target": "Package", "version": "[3.0.0, )"}, "Lennox.NvEncSharp": {"target": "Package", "version": "[2.0.0, )"}, "MediaFoundation.NetCore": {"target": "Package", "version": "[2024.5.10, )"}, "Quamotion.TurboJpegWrapper": {"target": "Package", "version": "[2.0.32, )"}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.2.0-beta, )"}, "System.Text.RegularExpressions": {"target": "Package", "version": "[4.3.1, )"}, "Vortice.D3DCompiler": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DXGI": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct2D1": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D11": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Direct3D12": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectML": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.DirectX": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Dxc": {"target": "Package", "version": "[3.6.2, )"}, "Vortice.Mathematics": {"target": "Package", "version": "[1.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F2693F-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "ComputeSharp"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Lennox.NvEncSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/lennox.nvencsharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'lennox.nvencsharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26915-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "Lennox.NvEncSharp"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'Lennox.NvEncSharp' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/lennox.nvencsharp/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'lennox.nvencsharp'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: E8F26915-28C1-4E5B-8B11-88510A1D79A7)).", "libraryId": "Lennox.NvEncSharp"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.Dxc' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.dxc/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.dxc'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1DE268D2-FFDB-4B00-99A7-EE1AAA4280FF)).", "libraryId": "ComputeSharp.Dxc"}, {"code": "NU1301", "level": "Error", "message": "Échec de la récupération des informations sur 'ComputeSharp.D2D1' à partir de la source distante 'https://aiinfra.pkgs.visualstudio.com/2692857e-05ef-43b4-ba9c-ccf1c22c437c/_packaging/7982ae20-ed19-4a35-a362-a96ac99897b7/nuget/v3/flat2/computesharp.d2d1/index.json'.\r\n  Response status code does not indicate success: 401 (Unauthorized - No local versions of package 'computesharp.d2d1'; please provide authentication to access versions from upstream that have not yet been saved to your feed. (DevOps Activity ID: 1EC10FD6-DD9F-47F3-95B1-0A83CD8EB88B)).", "libraryId": "ComputeSharp.D2D1"}, {"code": "NU1605", "level": "Error", "message": "Avertissement comme erreur : Passage à une version antérieure du package détecté : ComputeSharp de 3.2.0 à 3.1.0. Référencez le package directement à partir du projet pour sélectionner une version différente. \r\n TestSuppressionFondSghm -> DirectX -> ComputeSharp (>= 3.2.0) \r\n TestSuppressionFondSghm -> ComputeSharp (>= 3.1.0)", "libraryId": "ComputeSharp", "targetGraphs": ["net8.0-windows7.0"]}, {"code": "NU1605", "level": "Error", "message": "Avertissement comme erreur : Passage à une version antérieure du package détecté : ComputeSharp.D2D1 de 3.2.0 à 3.1.0. Réf<PERSON>ren<PERSON>z le package directement à partir du projet pour sélectionner une version différente. \r\n TestSuppressionFondSghm -> DirectX -> ComputeSharp.D2D1 (>= 3.2.0) \r\n TestSuppressionFondSghm -> ComputeSharp.D2D1 (>= 3.1.0)", "libraryId": "ComputeSharp.D2D1", "targetGraphs": ["net8.0-windows7.0"]}, {"code": "NU1605", "level": "Error", "message": "Avertissement comme erreur : Passage à une version antérieure du package détecté : ComputeSharp.Dxc de 3.2.0 à 3.1.0. Référencez le package directement à partir du projet pour sélectionner une version différente. \r\n TestSuppressionFondSghm -> DirectX -> ComputeSharp.Dxc (>= 3.2.0) \r\n TestSuppressionFondSghm -> ComputeSharp.Dxc (>= 3.1.0)", "libraryId": "ComputeSharp.Dxc", "targetGraphs": ["net8.0-windows7.0"]}]}
{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AndroidWebCamClient/1.0.0": {"dependencies": {"Ceras": "4.1.7", "Microsoft.VisualStudio.Threading": "17.10.48", "PrecisionTimer.NET": "*******", "Vanara.PInvoke.Kernel32": "4.0.3", "Vanara.PInvoke.User32": "4.0.3"}, "runtime": {"AndroidWebCamClient.dll": {}}}, "Ceras/4.1.7": {"dependencies": {"System.Buffers": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "runtime": {"lib/netstandard2.0/Ceras.dll": {"assemblyVersion": "4.1.7.0", "fileVersion": "4.1.7.0"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.VisualStudio.Threading/17.10.48": {"dependencies": {"Microsoft.VisualStudio.Threading.Analyzers": "17.10.48", "Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "17.10.0.0", "fileVersion": "17.10.48.7872"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Threading.Analyzers/17.10.48": {}, "Microsoft.VisualStudio.Validation/17.8.8": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.8.0.0", "fileVersion": "17.8.8.15457"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "PrecisionTimer.NET/*******": {"runtime": {"lib/netstandard2.0/PrecisionTimer.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "Vanara.Core/4.0.3": {"runtime": {"lib/net7.0/Vanara.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "resources": {"lib/net7.0/fr/Vanara.Core.resources.dll": {"locale": "fr"}}}, "Vanara.PInvoke.Gdi32/4.0.3": {"dependencies": {"Vanara.Core": "4.0.3", "Vanara.PInvoke.Shared": "4.0.3"}, "runtime": {"lib/net7.0/Vanara.PInvoke.Gdi32.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vanara.PInvoke.Kernel32/4.0.3": {"dependencies": {"Vanara.Core": "4.0.3", "Vanara.PInvoke.Shared": "4.0.3"}, "runtime": {"lib/net7.0/Vanara.PInvoke.Kernel32.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vanara.PInvoke.Shared/4.0.3": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "Vanara.Core": "4.0.3"}, "runtime": {"lib/net7.0/Vanara.PInvoke.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Vanara.PInvoke.User32/4.0.3": {"dependencies": {"Vanara.Core": "4.0.3", "Vanara.PInvoke.Gdi32": "4.0.3", "Vanara.PInvoke.Kernel32": "4.0.3", "Vanara.PInvoke.Shared": "4.0.3"}, "runtime": {"lib/net7.0/Vanara.PInvoke.User32.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AndroidWebCamClient/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Ceras/4.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-5hKNZAySyylTKELWfnLAFDG9A23Osp0Yqonpnmv2/MPqcVJjMC6/N/5EKJHYmMaw2xnLL8kiQaCe6Fu+tjNG/w==", "path": "ceras/4.1.7", "hashPath": "ceras.4.1.7.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Threading/17.10.48": {"type": "package", "serviceable": true, "sha512": "sha512-7onkbbE0AOAhxKe+ZAa2NMzo4R5G4qypZmNIE0GhBohT/tl6e5aLnLx4Gg6trf6SUn3DfLRowMtNe5Q+PmhKgQ==", "path": "microsoft.visualstudio.threading/17.10.48", "hashPath": "microsoft.visualstudio.threading.17.10.48.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Analyzers/17.10.48": {"type": "package", "serviceable": true, "sha512": "sha512-xwvwT91oqFjLgQykUp6y/JPYxz8LchbfJKrLVatfczWddXKng8DAo8RiiIodt+pRdsVXP9Ud02GtJoY7ifdXPQ==", "path": "microsoft.visualstudio.threading.analyzers/17.10.48", "hashPath": "microsoft.visualstudio.threading.analyzers.17.10.48.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "path": "microsoft.visualstudio.validation/17.8.8", "hashPath": "microsoft.visualstudio.validation.17.8.8.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "PrecisionTimer.NET/*******": {"type": "package", "serviceable": true, "sha512": "sha512-gFOgq5SfGgkmR7GVqv9YM7oWhMXQS5R3XO3oD0Rzp0hZmkpJkufuR4KowtgosrXOz56gD6fXA2SRc0Vr/h7O/w==", "path": "precisiontimer.net/*******", "hashPath": "precisiontimer.net.*******.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "Vanara.Core/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yPCYBz5qlT0TcwzJt/mmjGlYLJg4KXKUpWlt7dQt46QOMPGXyMj3GBcY0R1oyC9lq5iCR8xdmKx3HfKt7n13Nw==", "path": "vanara.core/4.0.3", "hashPath": "vanara.core.4.0.3.nupkg.sha512"}, "Vanara.PInvoke.Gdi32/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-t5nCJn+KymOTMgI9F0lr38lcmTPK2uJkt0UqxvcO7L/zm+z+hP4xo6XDWzraQX8ANcPy+yOmkjpvQyzZBx5WOQ==", "path": "vanara.pinvoke.gdi32/4.0.3", "hashPath": "vanara.pinvoke.gdi32.4.0.3.nupkg.sha512"}, "Vanara.PInvoke.Kernel32/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W3v4EnfsfLIGHiZ0yfppA12rkKal7K6KFlsxsKKCPGb8R2ivh4IsgIUBaGtssdPCaDfqwRZugExBBUgI/uABsw==", "path": "vanara.pinvoke.kernel32/4.0.3", "hashPath": "vanara.pinvoke.kernel32.4.0.3.nupkg.sha512"}, "Vanara.PInvoke.Shared/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-2rm9grMSC+H0jnpldL9Btcm2T/QwOfQGYgms3+K45CnIqT7h4oC7sSBReYpO/X2Oo5GLxBXn+QW7bE991hdnkw==", "path": "vanara.pinvoke.shared/4.0.3", "hashPath": "vanara.pinvoke.shared.4.0.3.nupkg.sha512"}, "Vanara.PInvoke.User32/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ESMwjZ5Ym/reI9z5W1ntgElsyEORGp53OKyN/yVMj1IRv4npaxdzRX442hbLsCocWyO3OAe2la1I1YJrW67Fhg==", "path": "vanara.pinvoke.user32/4.0.3", "hashPath": "vanara.pinvoke.user32.4.0.3.nupkg.sha512"}}}
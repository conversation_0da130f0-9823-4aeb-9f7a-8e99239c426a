#define D2D_INPUT_COUNT 2
#define D2D_INPUT0_COMPLEX
#define D2D_INPUT1_COMPLEX
#define D2D_REQUIRES_SCENE_POSITION
#include <d2d1effecthelpers.hlsli>

// BT.601 (Y [16 .. 235], U/V [16 .. 240]) with linear, full-range RGB output.
// Input YUV must be first subtracted by (0.0625, 0.5, 0.5).
// See : https://docs.microsoft.com/en-us/windows/win32/medfound/recommended-8-bit-yuv-formats-for-video-rendering#nv12
static const float3x3 yuvCoef =
{
    1.164383f, 1.164383f, 1.164383f,
	0.000000f, -0.391762f, 2.017232f,
	1.596027f, -0.812968f, 0.000000f
};

cbuffer constants : register(b0)
{
}

D2D_PS_ENTRY(main)
{
    float2 coord = D2DGetScenePosition().xy;

    // See : https://docs.microsoft.com/en-us/windows/win32/medfound/recommended-8-bit-yuv-formats-for-video-rendering#nv12
    float y = D2DSampleInputAtPosition(0, coord).x;
    float2 CbCr = D2DSampleInputAtPosition(1, coord / 2).xy;
    float3 cde = float3(y, CbCr);
    cde -= float3(0.0625f, 0.5f, 0.5f);
    float3 rgb = mul(cde, yuvCoef);
    float4 result = float4(saturate(rgb), 1);
    return result;
}

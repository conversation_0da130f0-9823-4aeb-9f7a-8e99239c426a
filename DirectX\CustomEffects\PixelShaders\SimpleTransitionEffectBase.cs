﻿using System;
using Vortice;
using Vortice.Direct2D1;

namespace DirectX
{
    [CustomEffect(2)]
    public class SimpleEffetEffectBase : PVShaderBase
    {
        protected int OutputWidthInternal, OutputHeightInternal;

        public SimpleEffetEffectBase(Guid guid) : base(guid)
        {
            InputCount = 2;
        }

        public override RawRect MapInvalidRect(uint inputIndex, RawRect invalidInputRect)
        {
            return new RawRect(int.MinValue, int.MinValue, int.MaxValue, int.MaxValue);
        }

        public override void MapInputRectsToOutputRect(RawRect[] inputRects, RawRect[] inputOpaqueSubRects, out RawRect outputRect, out RawRect outputOpaqueSubRect)
        {
            // NB: Il est nécessaire de fournir les dimensions exactes de sortie, car si l'input est une CommandList, elle pourrait débuter dans le négatif et se terminer au-delà des bounds de sortie si elle n'est pas limitée au cadre du contenant.
            outputRect = new(0, 0, OutputWidthInternal, OutputHeightInternal);
            outputOpaqueSubRect = default;
        }
        public override void MapOutputRectToInputRects(RawRect outputRect, RawRect[] inputRects)
        {
            for (int i = 0; i < inputRects.Length; i++)
                inputRects[i] = outputRect;
        }
    }
}

﻿using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AndroidWebCamClient
{
    public struct WebCamParameters
    {
        public string CameraId { get; set; }
        public int CameraWidth { get; set; }
        public int CameraHeight { get; set; }
        public int JpegWidth { get; set; }
        public int JpegHeight { get; set; }
        public int JpegQuality { get; set; }
        public bool Preview { get; set; }
        public bool FixedWhiteBalance { get; set; }
        public float ZoomFactor { get; set; }
        public float FrameRate { get; set; }
        public int IFrameInterval { get; set; }
        public int BitRate { get; set; }
        public int MaximumMinutesBetweenKeepAlives { get; set; }

        public string Serialize()
        {
            return JsonSerializer.Serialize(this);
        }
        public static WebCamParameters Deserialize(string json)
        {
            return JsonSerializer.Deserialize<WebCamParameters>(json);
        }
        public byte[] SerializeToBytes()
        {
            return JsonSerializer.SerializeToUtf8Bytes(this);
        }
        public static WebCamParameters Deserialize(byte[] json)
        {
            return JsonSerializer.Deserialize<WebCamParameters>(json);
        }
    }
}

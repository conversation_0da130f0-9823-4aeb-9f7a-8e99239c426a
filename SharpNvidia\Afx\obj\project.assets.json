{"version": 3, "targets": {"net8.0": {"SharpGen.Runtime/2.1.2-beta": {"type": "package", "compile": {"lib/net8.0/SharpGen.Runtime.dll": {}}, "runtime": {"lib/net8.0/SharpGen.Runtime.dll": {}}, "build": {"build/SharpGen.Runtime.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/SharpGen.Runtime.props": {}}}, "SharpGen.Runtime.COM/2.1.2-beta": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta"}, "compile": {"lib/net8.0/SharpGen.Runtime.COM.dll": {}}, "runtime": {"lib/net8.0/SharpGen.Runtime.COM.dll": {}}, "build": {"build/SharpGen.Runtime.COM.props": {}}, "buildMultiTargeting": {"buildMultiTargeting/SharpGen.Runtime.COM.props": {}}}}}, "libraries": {"SharpGen.Runtime/2.1.2-beta": {"sha512": "nqZAjfEG1jX1ivvdZLsi6Pkt0DiOJyuOgRgldNFsmjXFPhxUbXQibofLSwuDZidL2kkmtTF8qLoRIeqeVdXgYw==", "type": "package", "path": "sharpgen.runtime/2.1.2-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/Mapping.xml", "build/SharpGen.Runtime.props", "buildMultiTargeting/SharpGen.Runtime.props", "lib/net461/SharpGen.Runtime.dll", "lib/net471/SharpGen.Runtime.dll", "lib/net7.0/SharpGen.Runtime.dll", "lib/net8.0/SharpGen.Runtime.dll", "lib/netcoreapp3.1/SharpGen.Runtime.dll", "lib/netstandard2.0/SharpGen.Runtime.dll", "lib/netstandard2.1/SharpGen.Runtime.dll", "sharpgen.runtime.2.1.2-beta.nupkg.sha512", "sharpgen.runtime.nuspec"]}, "SharpGen.Runtime.COM/2.1.2-beta": {"sha512": "HBCrb6HfnUWx9v5/GjJeBr5DuodZLnHlFQQYXPrQs1Hbe1c6Wd0uCXf+SJp4hW8fQNxjXEu0FgiyHGlA/SRzRw==", "type": "package", "path": "sharpgen.runtime.com/2.1.2-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/SharpGen.Runtime.COM.BindMapping.xml", "build/SharpGen.Runtime.COM.props", "buildMultiTargeting/SharpGen.Runtime.COM.props", "lib/net461/SharpGen.Runtime.COM.dll", "lib/net471/SharpGen.Runtime.COM.dll", "lib/net7.0/SharpGen.Runtime.COM.dll", "lib/net8.0/SharpGen.Runtime.COM.dll", "lib/netcoreapp3.1/SharpGen.Runtime.COM.dll", "lib/netstandard2.0/SharpGen.Runtime.COM.dll", "lib/netstandard2.1/SharpGen.Runtime.COM.dll", "sharpgen.runtime.com.2.1.2-beta.nupkg.sha512", "sharpgen.runtime.com.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["SharpGen.Runtime >= 2.1.2-beta", "SharpGen.Runtime.COM >= 2.1.2-beta"]}, "packageFolders": {"F:\\NugetPackages": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.4-beta", "restore": {"projectUniqueName": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj", "projectName": "Afx", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\Afx.csproj", "packagesPath": "F:\\NugetPackages", "outputPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\SharpNvidia\\Afx\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/ORT-Nightly/nuget/v3/index.json": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"SharpGen.Runtime": {"target": "Package", "version": "[2.1.2-beta, )", "autoReferenced": true}, "SharpGen.Runtime.COM": {"target": "Package", "version": "[2.1.2-beta, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}
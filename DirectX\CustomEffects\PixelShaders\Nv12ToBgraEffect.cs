﻿using System;
using System.Runtime.InteropServices;
using Vortice;
using Vortice.Direct2D1;

namespace DirectX;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct Nv12ToBgraEffectParameters
{
}

[CustomEffect(["Source1", "Source2"], null, "A shader", "PSB", "PSB")]
public class Nv12ToBgraEffect : PVShaderBase
{
    static readonly Guid s_sGuid = Guid.NewGuid();
    public Nv12ToBgraEffect() : base(s_sGuid)
    {
        InputCount = 2;
    }

    public Nv12ToBgraEffectParameters cBuffer;

    public override void PrepareForRender(ChangeType changeType) //Passes the struct to Gpu. Without this, it would be useless.
    {
        dInfo?.SetPixelShaderConstantBuffer(cBuffer);
    }
    public override RawRect MapInvalidRect(uint inputIndex, RawRect invalidInputRect)
    {
        return new RawRect(int.MinValue, int.MinValue, int.MaxValue, int.MaxValue);
    }
    public override void MapInputRectsToOutputRect(RawRect[] inputRects, RawRect[] inputOpaqueSubRects, out RawRect outputRect, out RawRect outputOpaqueSubRect)
    {
        outputRect = inputRects[0];
        outputOpaqueSubRect = default;
    }
    public override void MapOutputRectToInputRects(RawRect outputRect, RawRect[] inputRects)
    {
        inputRects[0] = outputRect;
        inputRects[1] = new(outputRect.Left / 2, outputRect.Top / 2, outputRect.Right / 2, outputRect.Bottom / 2);
    }
}

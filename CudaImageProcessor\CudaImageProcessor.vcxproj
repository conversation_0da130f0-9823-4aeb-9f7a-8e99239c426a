<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F3DEF7DF-BC86-81D8-17F0-************}</ProjectGuid>
    <RootNamespace>CudaImageProcessor</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 12.9.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>IMAGEMATTING_EXPORTS;WIN32;WIN64;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)packages\Microsoft.ML.OnnxRuntime.Gpu.Windows.1.21.2\buildTransitive\native\include;$(WindowsSDK_IncludePath);C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include;$(ProjectDir)include;$(ProjectDir)ffmpeg-master-latest-win64-gpl-shared\include;$(ProjectDir)src\internal;C:\Program Files\NVIDIA GPU Computing Toolkit\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <AdditionalLibraryDirectories>$(SolutionDir)packages\Microsoft.ML.OnnxRuntime.Gpu.Windows.1.21.2\runtimes\win-x64\native;$(WindowsSDK_LibraryPath)\x64;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64;C:\Program Files\NVIDIA\CUDNN\v9.10\lib\x64;$(ProjectDir)ffmpeg-master-latest-win64-gpl-shared\lib;C:\Program Files\NVIDIA GPU Computing Toolkit\TensorRT\lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>cudart_static.lib;cublas.lib;cudnn.lib;nvonnxparser_10.lib;nvinfer_10.lib;onnxruntime_providers_cuda.lib;onnxruntime.lib;d3d12.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalOptions>/NODEFAULTLIB:LIBCMT %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
    </CudaCompile>
    <PostBuildEvent>
      <Command>xcopy "$(ProjectDir)bin\*.dll" "$(TargetDir)" /D /Y
xcopy "$(ProjectDir)bin\*.pdb" "$(TargetDir)" /D /Y
xcopy "$(TargetDir)*.dll" "$(SolutionDir)Tests\TestSuppressionFondConsole\bin\x64\Debug\net8.0\" /D /Y
xcopy "$(TargetDir)*.pdb" "$(SolutionDir)Tests\TestSuppressionFondConsole\bin\x64\Debug\net8.0\" /D /Y
echo Touch triggered &gt; "$(SolutionDir)DependencyTrigger.txt"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <PreprocessorDefinitions>IMAGEMATTING_EXPORTS;WIN32;WIN64;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PrecompiledHeaderFile>Not using</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(SolutionDir)packages\Microsoft.ML.OnnxRuntime.Gpu.Windows.1.21.2\buildTransitive\native\include;$(WindowsSDK_IncludePath);C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include;$(ProjectDir)include;$(ProjectDir)ffmpeg-master-latest-win64-gpl-shared\include;$(ProjectDir)src\internal;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IMAGEMATTINGLIBRARY_EXPORTS;WIN32;WIN64;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <SubSystem>Console</SubSystem>
      <AdditionalLibraryDirectories>$(SolutionDir)packages\Microsoft.ML.OnnxRuntime.Gpu.Windows.1.21.2\runtimes\win-x64\native;$(WindowsSDK_LibraryPath)\x64;$(CUDA_PATH)\lib\x64;C:\Program Files\NVIDIA\CUDNN\v9.10\lib\x64;$(ProjectDir)ffmpeg-master-latest-win64-gpl-shared\lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>cudart_static.lib;cublas.lib;cudnn.lib;onnxruntime_providers_cuda.lib;onnxruntime.lib;d3d12.lib;dxgi.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
    </CudaCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="include\framework.h" />
    <ClInclude Include="include\ImageMatting.h" />
    <ClInclude Include="src\d3dx12.h" />
    <ClInclude Include="src\HeadDetectionKernels.h" />
    <ClInclude Include="src\HeadDetection_Onnx.h" />
    <ClInclude Include="src\Helpers.h" />
    <ClInclude Include="src\lodepng.h" />
    <ClInclude Include="src\MattingKernels.h" />
    <ClInclude Include="src\StringUtils.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\dllmain.cpp" />
    <ClCompile Include="src\HeadDetection_Onnx.cpp" />
    <ClCompile Include="src\Helpers.cpp" />
    <ClCompile Include="src\ImageMatting_Onnx.cpp" />
    <ClCompile Include="src\lodepng.cpp" />
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="bin\onnxruntime.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_cuda.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_shared.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_tensorrt.dll">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="bin\onnxruntime.lib">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_cuda.lib">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_shared.lib">
      <FileType>Document</FileType>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_tensorrt.lib">
      <FileType>Document</FileType>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <CudaCompile Include="src\HeadDetectionKernels.cu" />
    <CudaCompile Include="src\MattingKernels.cu" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 12.9.targets" />
  </ImportGroup>
</Project>
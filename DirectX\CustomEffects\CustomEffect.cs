﻿using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using Vortice.Direct2D1;

namespace DirectX;

public class CustomEffect : ID2D1Effect
{
    public static void RegisterAllCustomEffects(ID2D1Factory7 factory2D)
    {
        // Register all custom effects.
        DirectoryInfo directoryInfo = new(DxUtils.PixelShaderDirectory);
        foreach (FileInfo file in directoryInfo.EnumerateFiles("*Effect.cs"))
        {
            string stringType = "DirectX." + file.Name.Split('.')[0];
            Type type = Type.GetType(stringType);
            typeof(ID2D1Factory7)
                .GetMethod("RegisterEffect", [])
                .MakeGenericMethod(type)
                .Invoke(factory2D, null);
            //Trace.WriteDebug($"{stringType}.");
        }
    }

    public static void UnregisterAllCustomEffects(ID2D1Factory7 factory2D)
    {
        // Unregister all custom effects.
        DirectoryInfo directoryInfo = new(DxUtils.PixelShaderDirectory);
        foreach (FileInfo file in directoryInfo.EnumerateFiles("*Effect.cs"))
        {
            string stringType = "DirectX." + file.Name.Split('.')[0];
            Type type = Type.GetType(stringType);
            factory2D.UnregisterEffect(type.GUID);
        }
    }

    public Type ShaderType;
    public CustomEffect(ID2D1DeviceContext context, Type T)
       : base(context.CreateEffect(T.GUID))
    {
        ShaderType = T;
    }

    public CustomEffect(ID2D1EffectContext context, Type T)
        : base(context.CreateEffect(T.GUID))
    {
        ShaderType = T;
    }
}

#include "Header1.hlsli"
cbuffer constants : register(b0)
{
    CONSTANTBUFFER_STANDARD_FIELDS

    int endx;
    int endy;
}

#define COMPLEXINPUTS
#include "Header2.hlsli"

#define PI 3.14159265358979323
#define POW2(X) X*X
#define POW3(X) X*X*X

float Rand(float2 v)
{
    return fract(sin(dot(v.xy, float2(12.9898, 78.233))) * 43758.5453);
}
float2 Rotate(float2 v, float a)
{
    float sina, cosa;
    sincos(a, sina, cosa);
    float2x2 rm = float2x2(cosa, -sina, sina, cosa);
    return mul(rm, v);
}
float CosInterpolation(float x)
{
    return -cos(x * PI) / 2. + .5;
}
float4 effet(float2 uv)
{
    float2 p = uv.xy / float2(1.0, 1.0).xy - .5;
    float2 rp = p;
    float rpr = (progress * 2.-1.);
    float z = -(rpr * rpr * 2.) + 3.;
    float az = abs(z);
    rp *= az;
    rp += mix(float2(.5, .5), float2(float(endx) + .5, float(endy) + .5), POW2(CosInterpolation(progress)));
    float2 mrp = rp % 1.;
    float2 crp = rp;
    bool onEnd = int(floor(crp.x)) == endx && int(floor(crp.y)) == endy;
    if (!onEnd)
    {
        float ang = float(int(Rand(floor(crp)) * 4.)) * .5 * PI;
        mrp = float2(.5, .5) + Rotate(mrp - float2(.5, .5), ang);
    }
    if (onEnd || Rand(floor(crp)) > .5)
    {
        return getToColor(mrp);
    }
    else
    {
        return getFromColor(mrp);
    }
}

endCode

﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Numerics;
using Vortice;
using Vortice.Direct2D1;
/// <summary>
/// See: https://github.com/MWstudios/Ensoftener/wiki
/// </summary>
namespace DirectX;

public enum SamplingFilter { Bilinear, Point }
/// <summary>A more or less functional shader effect inherited from <see cref="CustomEffectBase"/>. You can modify its input count, constant buffer, its nodes,
/// splittable data stream designed for passing vertices and the shader's semantics.</summary>
/// <remarks>The effect consists of a vertex shader and a pixel shader. The vertex shader stage deforms the bitmap via a triangular mesh and the pixel shader stage
/// can create additional color filters on top of the deformed triangles. Either can be turned off in the constructor, but disabling both may result in errors.
/// <br/>The vertex shader is still WIP - 100% functionality isn't guaranteed.</remarks>
[CustomEffect(["Source"], null, "A shader that inherits from PVShaderBase", "PVSB inheritors", "Ensoftener")]
public class PVShaderBase : CustomEffectBase, ID2D1DrawTransform
{
    #region Taken from Global in Ensoftener.
    /// <summary>A .cso file that will be loaded by every <b>pixel or compute shader</b> created from now on.
    /// One shader class can have different pixel shaders for every instance.</summary>
    public static string ShaderFilePath { get; set; }
    public string ShaderFile => Path.Combine(ShaderFilePath, this.GetType().Name + ".cso");
    /// <summary>A .cso file that will be loaded by every <b>vertex shader</b> created from now on.
    /// One shader class can have different vertex shaders for every instance.</summary>
    public static string VertexShaderFile { get; set; }
    #endregion

    static readonly List<object> s_instances = [];

    readonly Guid _psGUID, _vsGUID, _vbGUID;
    public ID2D1DrawInfo dInfo;
    public ID2D1VertexBuffer vertexBuffer;
    public ID2D1TransformGraph transformGraph;
    public bool vertexShader, pixelShader;
    string _psPath, _vsPath;
    public byte[] psFile, vsFile;
    public ID2D1EffectContext effectContext;
    public virtual ID2D1VertexBuffer GetID2D1VertexBuffer(VertexUsage vertexUsage)
    {
        //InitializeID2D1VertexBuffer(effectContext);
        // Updating geometry every time the effect is rendered can be costly, so it is 
        // recommended that vertex buffer remain static if possible (which it is in this sample effect).
        (byte[], int) buffer = VsInBuffer;
        // The GUID is optional, and is provided here to register the geometry globally.
        // As mentioned above, this avoids duplication if multiple versions of the effect are created.
        InputElementDescription[] ie = InputElementDescriptions;
        ID2D1VertexBuffer vb = effectContext.CreateVertexBuffer(
            new(ie.Length, vertexUsage, buffer.Item1, buffer.Item1.Length), _vbGUID,
            new(vsFile, buffer.Item2, ie));
        return vb;
    }
    public string PixelShaderFilePath { get => _psPath; set { _psPath = value; psFile = File.ReadAllBytes(value); } }
    public string VertexShaderFilePath { get => _vsPath; set { _vsPath = value; vsFile = File.ReadAllBytes(value); } }
    public SamplingFilter ScaleDownSampling { get; set; } = SamplingFilter.Point;
    public SamplingFilter ScaleUpSampling { get; set; } = SamplingFilter.Bilinear;
    public SamplingFilter MipmapSampling { get; set; } = SamplingFilter.Point;
    public bool AnisotropicFiltering { get; set; } = false;
    //ID2D1EffectContext _effectContext;
    /// <summary>In itializes a new custom effect.</summary>
    /// <param name="psGuid">The GUID of the effect. Each GUID has one pixel shader assigned to it,
    /// hence the <see cref="CloneablePixelShader"/>'s ability to have different pixel shaders.</param>
    /// <param name="vsGuid">The GUID of the vertex shader.</param>
    /// <param name="vbGuid">The GUID of the vertex buffer. The vertex buffer contains values that will be passed to the shader, not the shader itself.</param>
    /// <param name="usePS">Enable the pixel shader stage in this effect.</param>
    /// <param name="useVS">Enable the vertex shader stage in this effect.</param>
    public PVShaderBase(Guid? psGuid = null, Guid? vsGuid = null, Guid? vbGuid = null, bool usePS = true, bool useVS = false)
    {
        pixelShader = usePS;
        vertexShader = useVS;
        _psGUID = psGuid ?? Guid.Empty;
        _vsGUID = vsGuid ?? Guid.Empty;
        _vbGUID = vbGuid ?? Guid.Empty;
        s_instances.Add(this);
    }
    /// <summary>The amount of vertices processed by the vertex shader. The amount must be a multiple of 3, as every 3 vertices form a single face.</summary>
    public uint VertexCount { get; set; }
    public uint InputCount { get; set; } = 1;

    public uint GetInputCount() { return InputCount; }

    public override void Initialize(ID2D1EffectContext eC, ID2D1TransformGraph tg)
    {
        //WARNING : as soon as ID2D1TransformGraph.SetSingleTransformNode is called it chain calls the SetID2D1DrawInfo via a callback.
        //Unfortunately this is too early because the code below within this method is used to populate stateful data needed by the SetID2D1DrawInfo call. 
        //transformGraph.SetSingleTransformNode(this);
        effectContext = eC;
        if (pixelShader)
        {
            PixelShaderFilePath = ShaderFile;
            effectContext.LoadPixelShader(_psGUID, psFile, (uint)psFile.Length);
        }
        if (vertexShader)
        {
            VertexShaderFilePath = VertexShaderFile; effectContext.LoadVertexShader(_vsGUID, vsFile, (uint)vsFile.Length);
            vertexBuffer = effectContext.FindVertexBuffer(_vbGUID);
            if (vertexBuffer == null) ReloadID2D1VertexBuffer(true);
        }
        PrepareForRender(ChangeType.Properties | ChangeType.Context);
        SetGraph(tg);
    }
    /// <summary>A stream of structs assigned to semantics in vertex shaders. Streams can be created off of arrays via
    /// <see cref="DataStream.Create{T}(T[], bool, bool, int, bool)"/> (set <b>canRead</b> and <b>canWrite</b> to true!).
    /// The extra int indicates the size of each struct.</summary>
    /// <remarks>A vertex buffer in vertex shaders works differently than a constant buffer in pixel shaders (if you're looking for an eqiuvalent, that's <b>b1</b>).
    /// Here, each vertex will get one struct in the array and recieve it via semantics in its main method. <see cref="InputElementDescriptions"/> decides what parts
    /// of the struct will be assigned to each semantic.<br/><br/>While the struct can be literally anything, certain restrictions apply:
    /// <list type="number"><item>The struct must contain only value types (such as <see cref="Vector2"/>).</item>
    /// <item>All structs must be the same size, because the stream progresses by a fixed integer (second output) and would become offset.</item></list></remarks>
    public virtual (byte[], int) VsInBuffer => (null, 0);
    /// <summary>Returns an array of semantics that will be assigned to a part of each struct from <see cref="VsInBuffer"/>. The offset of an element specifies
    /// the offset of a struct's s region that's assigned, in bytes. The format specifies what type will be assigned to the semantic.</summary>
    /// <remarks>For example, if <see cref="VsInBuffer"/> contains structs with 8 floats each and there's an
    /// <see cref="InputElementDescription"/> named "ABC" with an offset of 16 and format of <see cref="SharpDX.DXGI.Format.R32G32B32_Float"/>,
    /// the vertex shader will recieve the struct's 5th, 6th and 7th float as a float3 under the ABC semantic.</remarks>
    public virtual InputElementDescription[] InputElementDescriptions => Array.Empty<InputElementDescription>();
    public override void SetGraph(ID2D1TransformGraph tg)
    {
        transformGraph?.Dispose();
        transformGraph = tg;
        InputCount = tg.InputCount;
        tg.SetSingleTransformNode(this);
    }
    public Filter GetSampling => AnisotropicFiltering ? Filter.Anisotropic : ScaleDownSampling == SamplingFilter.Bilinear ?
        ScaleUpSampling == SamplingFilter.Bilinear ?
            MipmapSampling == SamplingFilter.Bilinear ? Filter.MinMagMipLinear : Filter.MinMagLinearMipPoint :
            MipmapSampling == SamplingFilter.Bilinear ? Filter.MinLinearMagPointMipLinear : Filter.MinLinearMagMipPoint :
        ScaleUpSampling == SamplingFilter.Bilinear ?
            MipmapSampling == SamplingFilter.Bilinear ? Filter.MinPointMagMipLinear : Filter.MinPointMagLinearMipPoint :
            MipmapSampling == SamplingFilter.Bilinear ? Filter.MinMagPointMipLinear : Filter.MinMagMipPoint;
    public void SetDrawInfo(ID2D1DrawInfo drawInfo)
    {
        dInfo?.Dispose();
        dInfo = drawInfo;
        if (pixelShader)
        {
            dInfo.SetPixelShader(_psGUID, PixelOptions.None);
            dInfo.SetOutputBuffer(BufferPrecision.PerChannel8Unorm, ChannelDepth.Four);
            InputDescription inputDescription = new()
            {
                Filter = GetSampling,
                LevelOfDetailCount = 0
            };
            for (uint i = 0; i < InputCount; i++)
                dInfo.SetInputDescription(i, inputDescription);
        }
        if (vertexShader)
            dInfo.SetVertexProcessing(vertexBuffer, VertexOptions.UseDepthBuffer, null, new VertexRange(0, VertexCount), _vsGUID);
    }
    /// <summary>Updates the vertex buffer according to <see cref="VsInBuffer"/>.</summary>
    /// <param name="sameShader">Reloads the same .cso file as before (otherwise uses <see cref="Global.VertexShaderFile"/>.</param>
    public void ReloadID2D1VertexBuffer(bool sameShader = true)
    {
        if (!sameShader) VertexShaderFilePath = VertexShaderFile;
        vertexBuffer?.Dispose(); //vbGUID = Guid.NewGuid();
        vertexBuffer = GetID2D1VertexBuffer(VertexUsage.Static);
    }
    public virtual RawRect MapInvalidRect(uint inputIndex, RawRect invalidInputRect) => invalidInputRect;
    public virtual void MapInputRectsToOutputRect(RawRect[] inputRects, RawRect[] inputOpaqueSubRects, out RawRect outputRect, out RawRect outputOpaqueSubRect)
    { outputRect = inputRects[0]; ; outputOpaqueSubRect = default; }
    public virtual void MapOutputRectToInputRects(RawRect outputRect, RawRect[] inputRects)
    {
        for (int i = 0; i < inputRects.Length; i++) inputRects[i] = 
                new(outputRect.Left, outputRect.Top, outputRect.Right, outputRect.Bottom);
    }
    public new virtual void Dispose()
    {
        base.Dispose();
        vertexBuffer?.Dispose();
        if (s_instances.Contains(this)) s_instances.Remove(this);
        GC.SuppressFinalize(this);
    }
    ~PVShaderBase() => Dispose();
}
/// <summary>A shader with no constant buffer, no inside effects and 1 texture input.
/// Its GUID is different for every instance, which means it can load a different shader every time.</summary>
public class CloneablePixelShader : PVShaderBase { public CloneablePixelShader() : base(Guid.NewGuid()) { } }

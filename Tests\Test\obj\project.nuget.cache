{"version": 2, "dgSpecHash": "p90eIgBuZaI=", "success": false, "projectFilePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Test\\TestHelix.csproj", "expectedPackageFiles": ["F:\\NugetPackages\\assimpnet\\5.0.0-beta1\\assimpnet.5.0.0-beta1.nupkg.sha512", "F:\\NugetPackages\\avalonedit\\6.3.1.120\\avalonedit.6.3.1.120.nupkg.sha512", "F:\\NugetPackages\\ceras\\4.1.7\\ceras.4.1.7.nupkg.sha512", "F:\\NugetPackages\\computesharp\\3.2.0\\computesharp.3.2.0.nupkg.sha512", "F:\\NugetPackages\\computesharp.core\\3.2.0\\computesharp.core.3.2.0.nupkg.sha512", "F:\\NugetPackages\\computesharp.d2d1\\3.2.0\\computesharp.d2d1.3.2.0.nupkg.sha512", "F:\\NugetPackages\\computesharp.dxc\\3.2.0\\computesharp.dxc.3.2.0.nupkg.sha512", "F:\\NugetPackages\\concurrencyvisualizer\\3.0.0\\concurrencyvisualizer.3.0.0.nupkg.sha512", "F:\\NugetPackages\\controlzex\\7.0.0\\controlzex.7.0.0.nupkg.sha512", "F:\\NugetPackages\\cyotek.drawing.bitmapfont\\2.0.0\\cyotek.drawing.bitmapfont.2.0.0.nupkg.sha512", "F:\\NugetPackages\\directshowlib.standard\\2.1.0\\directshowlib.standard.2.1.0.nupkg.sha512", "F:\\NugetPackages\\dirkster.avalondock\\4.72.1\\dirkster.avalondock.4.72.1.nupkg.sha512", "F:\\NugetPackages\\dirkster.avalondock.themes.vs2013\\4.72.1\\dirkster.avalondock.themes.vs2013.4.72.1.nupkg.sha512", "F:\\NugetPackages\\documentformat.openxml\\3.3.0\\documentformat.openxml.3.3.0.nupkg.sha512", "F:\\NugetPackages\\documentformat.openxml.framework\\3.3.0\\documentformat.openxml.framework.3.3.0.nupkg.sha512", "F:\\NugetPackages\\documentformat.openxml.linq\\3.3.0\\documentformat.openxml.linq.3.3.0.nupkg.sha512", "F:\\NugetPackages\\dotnext\\5.21.0\\dotnext.5.21.0.nupkg.sha512", "F:\\NugetPackages\\dotnext.unsafe\\5.21.0\\dotnext.unsafe.5.21.0.nupkg.sha512", "F:\\NugetPackages\\excss\\4.3.0\\excss.4.3.0.nupkg.sha512", "F:\\NugetPackages\\fasterkv.cache.core\\1.0.2\\fasterkv.cache.core.1.0.2.nupkg.sha512", "F:\\NugetPackages\\fizzler\\1.3.1\\fizzler.1.3.1.nupkg.sha512", "F:\\NugetPackages\\fluent.ribbon\\11.0.0\\fluent.ribbon.11.0.0.nupkg.sha512", "F:\\NugetPackages\\harfbuzzsharp\\8.3.0.1\\harfbuzzsharp.8.3.0.1.nupkg.sha512", "F:\\NugetPackages\\harfbuzzsharp.nativeassets.macos\\8.3.0.1\\harfbuzzsharp.nativeassets.macos.8.3.0.1.nupkg.sha512", "F:\\NugetPackages\\harfbuzzsharp.nativeassets.win32\\8.3.0.1\\harfbuzzsharp.nativeassets.win32.8.3.0.1.nupkg.sha512", "F:\\NugetPackages\\helixtoolkit.sharpdx.assimp\\2.27.0\\helixtoolkit.sharpdx.assimp.2.27.0.nupkg.sha512", "F:\\NugetPackages\\helixtoolkit.sharpdx.core.wpf\\2.26.0\\helixtoolkit.sharpdx.core.wpf.2.26.0.nupkg.sha512", "F:\\NugetPackages\\hg.nwaves\\0.9.6\\hg.nwaves.0.9.6.nupkg.sha512", "F:\\NugetPackages\\holotrack.directshow\\1.0.0\\holotrack.directshow.1.0.0.nupkg.sha512", "F:\\NugetPackages\\ironcompress\\1.6.3\\ironcompress.1.6.3.nupkg.sha512", "F:\\NugetPackages\\jetbrains.annotations\\2024.3.0\\jetbrains.annotations.2024.3.0.nupkg.sha512", "F:\\NugetPackages\\magick.net-q16-anycpu\\14.5.0\\magick.net-q16-anycpu.14.5.0.nupkg.sha512", "F:\\NugetPackages\\magick.net.core\\14.5.0\\magick.net.core.14.5.0.nupkg.sha512", "F:\\NugetPackages\\magick.net.systemdrawing\\8.0.5\\magick.net.systemdrawing.8.0.5.nupkg.sha512", "F:\\NugetPackages\\mediafoundation.netcore\\2024.5.10\\mediafoundation.netcore.2024.5.10.nupkg.sha512", "F:\\NugetPackages\\microsoft-windowsapicodepack-core\\1.1.5\\microsoft-windowsapicodepack-core.1.1.5.nupkg.sha512", "F:\\NugetPackages\\microsoft-windowsapicodepack-shell\\1.1.5\\microsoft-windowsapicodepack-shell.1.1.5.nupkg.sha512", "F:\\NugetPackages\\microsoft.ai.directml\\1.15.4\\microsoft.ai.directml.1.15.4.nupkg.sha512", "F:\\NugetPackages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.bcl.cryptography\\9.0.4\\microsoft.bcl.cryptography.9.0.4.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.dependencyinjection\\7.0.0\\microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.dependencyinjection.abstractions\\7.0.0\\microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.logging\\7.0.0\\microsoft.extensions.logging.7.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.logging.abstractions\\7.0.0\\microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.options\\7.0.0\\microsoft.extensions.options.7.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.extensions.primitives\\7.0.0\\microsoft.extensions.primitives.7.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.faster.core\\2.1.0\\microsoft.faster.core.2.1.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.ml.onnxruntime.directml\\1.21.1\\microsoft.ml.onnxruntime.directml.1.21.1.nupkg.sha512", "F:\\NugetPackages\\microsoft.ml.onnxruntime.managed\\1.21.1\\microsoft.ml.onnxruntime.managed.1.21.1.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.platforms\\1.1.1\\microsoft.netcore.platforms.1.1.1.nupkg.sha512", "F:\\NugetPackages\\microsoft.netcore.targets\\1.1.3\\microsoft.netcore.targets.1.1.3.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.threading\\17.13.61\\microsoft.visualstudio.threading.17.13.61.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.threading.analyzers\\17.13.61\\microsoft.visualstudio.threading.analyzers.17.13.61.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.threading.only\\17.13.61\\microsoft.visualstudio.threading.only.17.13.61.nupkg.sha512", "F:\\NugetPackages\\microsoft.visualstudio.validation\\17.8.8\\microsoft.visualstudio.validation.17.8.8.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "F:\\NugetPackages\\microsoft.win32.systemevents\\9.0.4\\microsoft.win32.systemevents.9.0.4.nupkg.sha512", "F:\\NugetPackages\\microsoft.xaml.behaviors.wpf\\1.1.77\\microsoft.xaml.behaviors.wpf.1.1.77.nupkg.sha512", "F:\\NugetPackages\\modernwpfui\\0.9.6\\modernwpfui.0.9.6.nupkg.sha512", "F:\\NugetPackages\\naudio\\2.2.1\\naudio.2.2.1.nupkg.sha512", "F:\\NugetPackages\\naudio.asio\\2.2.1\\naudio.asio.2.2.1.nupkg.sha512", "F:\\NugetPackages\\naudio.core\\2.2.1\\naudio.core.2.2.1.nupkg.sha512", "F:\\NugetPackages\\naudio.midi\\2.2.1\\naudio.midi.2.2.1.nupkg.sha512", "F:\\NugetPackages\\naudio.wasapi\\2.2.1\\naudio.wasapi.2.2.1.nupkg.sha512", "F:\\NugetPackages\\naudio.winforms\\2.2.1\\naudio.winforms.2.2.1.nupkg.sha512", "F:\\NugetPackages\\naudio.winmm\\2.2.1\\naudio.winmm.2.2.1.nupkg.sha512", "F:\\NugetPackages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "F:\\NugetPackages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "F:\\NugetPackages\\nito.asyncex.context\\5.1.2\\nito.asyncex.context.5.1.2.nupkg.sha512", "F:\\NugetPackages\\nito.asyncex.tasks\\5.1.2\\nito.asyncex.tasks.5.1.2.nupkg.sha512", "F:\\NugetPackages\\nito.disposables\\2.2.1\\nito.disposables.2.2.1.nupkg.sha512", "F:\\NugetPackages\\precisiontimer.net\\2.4.0.4\\precisiontimer.net.2.4.0.4.nupkg.sha512", "F:\\NugetPackages\\propertytools\\3.1.0\\propertytools.3.1.0.nupkg.sha512", "F:\\NugetPackages\\propertytools.wpf\\3.1.0\\propertytools.wpf.3.1.0.nupkg.sha512", "F:\\NugetPackages\\quamotion.turbojpegwrapper\\2.0.32\\quamotion.turbojpegwrapper.2.0.32.nupkg.sha512", "F:\\NugetPackages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "F:\\NugetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.2\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512", "F:\\NugetPackages\\shapecrawler\\0.66.0\\shapecrawler.0.66.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx\\4.2.0\\sharpdx.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.d3dcompiler\\4.2.0\\sharpdx.d3dcompiler.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.direct2d1\\4.2.0\\sharpdx.direct2d1.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.direct3d11\\4.2.0\\sharpdx.direct3d11.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.direct3d9\\4.2.0\\sharpdx.direct3d9.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.dxgi\\4.2.0\\sharpdx.dxgi.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpdx.mathematics\\4.2.0\\sharpdx.mathematics.4.2.0.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime\\2.2.0-beta\\sharpgen.runtime.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\sharpgen.runtime.com\\2.2.0-beta\\sharpgen.runtime.com.2.2.0-beta.nupkg.sha512", "F:\\NugetPackages\\skiasharp\\3.116.1\\skiasharp.3.116.1.nupkg.sha512", "F:\\NugetPackages\\skiasharp.nativeassets.linux\\3.116.1\\skiasharp.nativeassets.linux.3.116.1.nupkg.sha512", "F:\\NugetPackages\\skiasharp.nativeassets.macos\\3.116.1\\skiasharp.nativeassets.macos.3.116.1.nupkg.sha512", "F:\\NugetPackages\\skiasharp.nativeassets.win32\\3.116.1\\skiasharp.nativeassets.win32.3.116.1.nupkg.sha512", "F:\\NugetPackages\\snappier\\1.1.6\\snappier.1.1.6.nupkg.sha512", "F:\\NugetPackages\\spire.presentation\\10.4.2\\spire.presentation.10.4.2.nupkg.sha512", "F:\\NugetPackages\\svg\\3.4.7\\svg.3.4.7.nupkg.sha512", "F:\\NugetPackages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "F:\\NugetPackages\\system.codedom\\9.0.4\\system.codedom.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.collections.immutable\\9.0.3\\system.collections.immutable.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.collections.nongeneric\\4.3.0\\system.collections.nongeneric.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.collections.specialized\\4.3.0\\system.collections.specialized.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel.annotations\\4.6.0\\system.componentmodel.annotations.4.6.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.componentmodel.typeconverter\\4.3.0\\system.componentmodel.typeconverter.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.diagnosticsource\\4.3.0\\system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.drawing.common\\9.0.4\\system.drawing.common.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.drawing.primitives\\4.3.0\\system.drawing.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.formats.asn1\\9.0.4\\system.formats.asn1.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.formats.nrbf\\9.0.3\\system.formats.nrbf.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.interactive.async\\6.0.1\\system.interactive.async.6.0.1.nupkg.sha512", "F:\\NugetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.io.hashing\\8.0.0\\system.io.hashing.8.0.0.nupkg.sha512", "F:\\NugetPackages\\system.io.packaging\\8.0.1\\system.io.packaging.8.0.1.nupkg.sha512", "F:\\NugetPackages\\system.io.pipelines\\9.0.3\\system.io.pipelines.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.linq.async\\6.0.1\\system.linq.async.6.0.1.nupkg.sha512", "F:\\NugetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.management\\9.0.4\\system.management.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "F:\\NugetPackages\\system.net.http\\4.3.4\\system.net.http.4.3.4.nupkg.sha512", "F:\\NugetPackages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.numerics.tensors\\9.0.0\\system.numerics.tensors.9.0.0.nupkg.sha512", "F:\\NugetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.private.datacontractserialization\\4.3.0\\system.private.datacontractserialization.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.metadata\\9.0.3\\system.reflection.metadata.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.resources.extensions\\9.0.3\\system.resources.extensions.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime\\4.3.1\\system.runtime.4.3.1.nupkg.sha512", "F:\\NugetPackages\\system.runtime.compilerservices.unsafe\\6.1.2\\system.runtime.compilerservices.unsafe.6.1.2.nupkg.sha512", "F:\\NugetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.serialization.primitives\\4.3.0\\system.runtime.serialization.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.runtime.serialization.xml\\4.3.0\\system.runtime.serialization.xml.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.cng\\4.3.0\\system.security.cryptography.cng.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.pkcs\\9.0.4\\system.security.cryptography.pkcs.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.security.cryptography.xml\\9.0.4\\system.security.cryptography.xml.9.0.4.nupkg.sha512", "F:\\NugetPackages\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "F:\\NugetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.text.encodings.web\\9.0.3\\system.text.encodings.web.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.text.json\\9.0.3\\system.text.json.9.0.3.nupkg.sha512", "F:\\NugetPackages\\system.text.regularexpressions\\4.3.1\\system.text.regularexpressions.4.3.1.nupkg.sha512", "F:\\NugetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.tasks.parallel\\4.3.0\\system.threading.tasks.parallel.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.xmldocument\\4.3.0\\system.xml.xmldocument.4.3.0.nupkg.sha512", "F:\\NugetPackages\\system.xml.xmlserializer\\4.3.0\\system.xml.xmlserializer.4.3.0.nupkg.sha512", "F:\\NugetPackages\\validation\\2.5.42\\validation.2.5.42.nupkg.sha512", "F:\\NugetPackages\\vanara.core\\4.1.2\\vanara.core.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.cryptography\\4.1.2\\vanara.pinvoke.cryptography.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.gdi32\\4.1.2\\vanara.pinvoke.gdi32.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.kernel32\\4.1.2\\vanara.pinvoke.kernel32.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.ole\\4.1.2\\vanara.pinvoke.ole.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.rpc\\4.1.2\\vanara.pinvoke.rpc.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.security\\4.1.2\\vanara.pinvoke.security.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.shared\\4.1.2\\vanara.pinvoke.shared.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vanara.pinvoke.user32\\4.1.2\\vanara.pinvoke.user32.4.1.2.nupkg.sha512", "F:\\NugetPackages\\vortice.d3dcompiler\\3.6.2\\vortice.d3dcompiler.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct2d1\\3.6.2\\vortice.direct2d1.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d11\\3.6.2\\vortice.direct3d11.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.direct3d12\\3.6.2\\vortice.direct3d12.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directml\\3.6.2\\vortice.directml.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.directx\\3.6.2\\vortice.directx.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc\\3.6.2\\vortice.dxc.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxc.native\\1.0.2\\vortice.dxc.native.1.0.2.nupkg.sha512", "F:\\NugetPackages\\vortice.dxgi\\3.6.2\\vortice.dxgi.3.6.2.nupkg.sha512", "F:\\NugetPackages\\vortice.mathematics\\1.9.3\\vortice.mathematics.1.9.3.nupkg.sha512", "F:\\NugetPackages\\vortice.xaudio2\\3.6.2\\vortice.xaudio2.3.6.2.nupkg.sha512", "F:\\NugetPackages\\wpfscreenhelper\\2.1.1\\wpfscreenhelper.2.1.1.nupkg.sha512", "F:\\NugetPackages\\zstdsharp.port\\0.8.1\\zstdsharp.port.0.8.1.nupkg.sha512"], "logs": [{"code": "NU1107", "level": "Error", "message": "Conflit de version détecté pour HelixToolkit. Installez/référencez HelixToolkit 2.27.0 directement au projet TestHelix pour résoudre ce problème. \r\n TestHelix -> DirectX -> HelixToolkit (>= 2.27.0) \r\n TestHelix -> HelixToolkit.SharpDX.Core.Wpf 2.26.0 -> HelixToolkit (= 2.26.0).", "projectPath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Test\\TestHelix.csproj", "filePath": "F:\\Catechese\\EditeurAudioVideo\\EditeurAudioVideo\\Tests\\Test\\TestHelix.csproj", "libraryId": "HelixToolkit", "targetGraphs": ["net8.0-windows7.0"]}]}
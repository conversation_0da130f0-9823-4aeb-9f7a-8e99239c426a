﻿using System;
using System.Collections.Generic;

namespace Commun
{
    /// <summary>
    /// Gère les ressources libérables.
    /// PRINCIPE:
    /// Les ressources ne sont libérées que par le thread DirectX.
    /// Ainsi, une fonction qui accède à une ressource peut être certaine que celle-ci ne sera pas libérée dans le délais
    /// de DuréeConservationSupplémentaire si elle l'a ajournée avant. 
    /// </summary>

    public delegate void LibérerRessourceLibérable();
    public delegate long CalculerMémoireRequiseRessourceLibérable();

    public class RessourceLibérable : IComparable<RessourceLibérable>
    {
        public int CompareTo(RessourceLibérable other)
        {
            int a = _tempsExpiration.CompareTo(other._tempsExpiration);
            return a;
        }


        internal readonly LibérerRessourceLibérable _libérerRessourceLibérable;
        internal readonly CalculerMémoireRequiseRessourceLibérable CalculerMémoireRequiseRessourceLibérable;
        internal readonly int _délaisExpiration;
        internal DateTime _tempsExpiration;
        internal bool _libérée;

        class RessourceLibérableÀDisposer
        {
            public List<RessourceLibérable> LesRessourcesLibérables;
            public DateTime TempsLibération;
            public RessourceLibérableÀDisposer(List<RessourceLibérable> ressourcesLibérables, DateTime tempsLibération)
            {
                LesRessourcesLibérables = ressourcesLibérables;
                TempsLibération = tempsLibération;
            }
        }

        /// <summary>
        /// Créer une ressource qui sera automatiquement libérée après un certain nombre de millisecondes.
        /// Peut être appelée à partir de n'importe quel thread.
        /// </summary>
        /// <param name="délaisExpiration">En millisecondes</param>
        /// <param name="libérerRessourceLibérable"></param>
        /// <param name="calculerMémoireRequiseRessourceLibérable"></param>
        internal RessourceLibérable(int délaisExpiration,
                                  LibérerRessourceLibérable libérerRessourceLibérable,
                                  CalculerMémoireRequiseRessourceLibérable calculerMémoireRequiseRessourceLibérable)
        {
            _délaisExpiration = délaisExpiration;
            _tempsExpiration = DateTime.Now + TimeSpan.FromMilliseconds(délaisExpiration);
            _libérerRessourceLibérable = libérerRessourceLibérable;
            CalculerMémoireRequiseRessourceLibérable = calculerMémoireRequiseRessourceLibérable;
        }
        /// <summary>
        /// Calcule la mémoire requise par le tampon de cette ressource libérable.
        /// Doit pouvoir être appelé de n'importe quel thread...
        /// </summary>
        /// <returns>Mémoire requise en bytes</returns>
        public long CalculerMémoireRequise()
        {
            return CalculerMémoireRequiseRessourceLibérable();
        }
    }
}

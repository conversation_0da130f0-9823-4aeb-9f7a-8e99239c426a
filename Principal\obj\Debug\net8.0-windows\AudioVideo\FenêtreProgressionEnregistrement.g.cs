﻿#pragma checksum "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "358EF459E35A04D0D8ED9552A2AD7F61CEF73F8D"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Principal {
    
    
    /// <summary>
    /// FenêtreProgressionEnregistrement
    /// </summary>
    public partial class FenêtreProgressionEnregistrement : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockMirophone;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockCaméra;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockDurée;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label LabelAudioCompressionLag;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockAudioCompressionLag;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label LabelVideoCompressionLag;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockVideoCompressionLag;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label LabelVideoTransmissionLag;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockVideoTransmissionLag;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockVolume;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BorderAvertissement;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockAvertissement;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonArrêter;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Principal;component/audiovideo/fen%c3%aatreprogressionenregistrement.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 6 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
            ((Principal.FenêtreProgressionEnregistrement)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.Window_Closing);
            
            #line default
            #line hidden
            
            #line 6 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
            ((Principal.FenêtreProgressionEnregistrement)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.Window_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TextBlockMirophone = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TextBlockCaméra = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TextBlockDurée = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.LabelAudioCompressionLag = ((System.Windows.Controls.Label)(target));
            return;
            case 6:
            this.TextBlockAudioCompressionLag = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.LabelVideoCompressionLag = ((System.Windows.Controls.Label)(target));
            return;
            case 8:
            this.TextBlockVideoCompressionLag = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LabelVideoTransmissionLag = ((System.Windows.Controls.Label)(target));
            return;
            case 10:
            this.TextBlockVideoTransmissionLag = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TextBlockVolume = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.BorderAvertissement = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.TextBlockAvertissement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ButtonArrêter = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\AudioVideo\FenêtreProgressionEnregistrement.xaml"
            this.ButtonArrêter.Click += new System.Windows.RoutedEventHandler(this.ButtonArrêter_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}


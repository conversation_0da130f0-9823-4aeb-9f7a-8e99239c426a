﻿#pragma checksum "..\..\..\..\Éditeur\Éditeur.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "668799DD4854D80BD2E0E7AC4FF66E2C034A2ED6"
//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

using AvalonDock;
using AvalonDock.Controls;
using AvalonDock.Converters;
using AvalonDock.Layout;
using AvalonDock.Themes;
using DirectX;
using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using Principal;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Principal {
    
    
    /// <summary>
    /// Éditeur
    /// </summary>
    public partial class Éditeur : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 95 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid GridGraphique;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StackPanelCentre;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SliderStepX;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ModernWpf.Controls.NumberBox NumberBoxMillisecondes;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSecondes;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ModernWpf.Controls.NumberBox NumberBoxIndexFrame;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalImages;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ModernWpf.Controls.NumberBox NumberBoxIndexSample;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalÉchantillons;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockCorrectionsEffectuées;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockInformations;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TextBlockDebug;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel StackPanelDessinAudio;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label LabelZoomY;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SliderZoomY;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Éditeur\Éditeur.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal DirectX.DirectXControl LeContrôle;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Principal;component/%c3%89diteur/%c3%89diteur.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Éditeur\Éditeur.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 10 "..\..\..\..\Éditeur\Éditeur.xaml"
            ((Principal.Éditeur)(target)).Loaded += new System.Windows.RoutedEventHandler(this.UserControl_Loaded);
            
            #line default
            #line hidden
            
            #line 10 "..\..\..\..\Éditeur\Éditeur.xaml"
            ((Principal.Éditeur)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.UserControl_Unloaded);
            
            #line default
            #line hidden
            
            #line 11 "..\..\..\..\Éditeur\Éditeur.xaml"
            ((Principal.Éditeur)(target)).GotFocus += new System.Windows.RoutedEventHandler(this.UserControl_GotFocus);
            
            #line default
            #line hidden
            
            #line 12 "..\..\..\..\Éditeur\Éditeur.xaml"
            ((Principal.Éditeur)(target)).SizeChanged += new System.Windows.SizeChangedEventHandler(this.UserControl_SizeChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 36 "..\..\..\..\Éditeur\Éditeur.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.LierPointsBezier_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 37 "..\..\..\..\Éditeur\Éditeur.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.DélierPointsBezier_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.GridGraphique = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.StackPanelCentre = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.SliderStepX = ((System.Windows.Controls.Slider)(target));
            return;
            case 7:
            this.NumberBoxMillisecondes = ((ModernWpf.Controls.NumberBox)(target));
            return;
            case 8:
            this.TotalSecondes = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.NumberBoxIndexFrame = ((ModernWpf.Controls.NumberBox)(target));
            return;
            case 10:
            this.TotalImages = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.NumberBoxIndexSample = ((ModernWpf.Controls.NumberBox)(target));
            return;
            case 12:
            this.TotalÉchantillons = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TextBlockCorrectionsEffectuées = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TextBlockInformations = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TextBlockDebug = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.StackPanelDessinAudio = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.LabelZoomY = ((System.Windows.Controls.Label)(target));
            return;
            case 18:
            this.SliderZoomY = ((System.Windows.Controls.Slider)(target));
            return;
            case 19:
            this.LeContrôle = ((DirectX.DirectXControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}


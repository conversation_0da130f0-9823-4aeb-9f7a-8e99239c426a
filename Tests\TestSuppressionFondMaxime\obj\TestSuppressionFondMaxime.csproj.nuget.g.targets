﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)\microsoft.extensions.logging.abstractions\6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.extensions.logging.abstractions\6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)\computesharp.core\3.2.0\buildTransitive\ComputeSharp.Core.targets" Condition="Exists('$(NuGetPackageRoot)\computesharp.core\3.2.0\buildTransitive\ComputeSharp.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)\computesharp.d2d1\3.2.0\buildTransitive\ComputeSharp.D2D1.targets" Condition="Exists('$(NuGetPackageRoot)\computesharp.d2d1\3.2.0\buildTransitive\ComputeSharp.D2D1.targets')" />
  </ImportGroup>
</Project>
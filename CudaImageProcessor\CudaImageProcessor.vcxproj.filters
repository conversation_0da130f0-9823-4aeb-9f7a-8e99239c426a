﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="src\ImageMatting_Onnx.cpp" />
    <ClCompile Include="src\Helpers.cpp" />
    <ClCompile Include="src\lodepng.cpp" />
    <ClCompile Include="src\HeadDetection_Onnx.cpp" />
    <ClCompile Include="src\dllmain.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\framework.h" />
    <ClInclude Include="include\ImageMatting.h" />
    <ClInclude Include="src\d3dx12.h" />
    <ClInclude Include="src\lodepng.h" />
    <ClInclude Include="src\Helpers.h" />
    <ClInclude Include="src\StringUtils.h" />
    <ClInclude Include="src\HeadDetection_Onnx.h" />
    <ClInclude Include="src\HeadDetectionKernels.h" />
    <ClInclude Include="src\MattingKernels.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Libs">
      <UniqueIdentifier>{652be1da-b851-4f5a-9036-81060ee94b97}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <CopyFileToFolders Include="bin\onnxruntime.dll">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime.lib">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_cuda.dll">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_cuda.lib">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_shared.dll">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_shared.lib">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_tensorrt.dll">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
    <CopyFileToFolders Include="bin\onnxruntime_providers_tensorrt.lib">
      <Filter>Libs</Filter>
    </CopyFileToFolders>
  </ItemGroup>
  <ItemGroup>
    <CudaCompile Include="src\HeadDetectionKernels.cu" />
    <CudaCompile Include="src\MattingKernels.cu" />
  </ItemGroup>
</Project>
// Define the compute shader
static const int RepeatShort = 1, RepeatLong = 254;

cbuffer Constants : register(b0)
{
    int width;
    int height;
};

StructuredBuffer<int> columnHeaders : register(t0);
StructuredBuffer<int> input : register(t1);
RWTexture2D<float> output : register(u0);

[numthreads(1, 1, 1)]
void CSMain(uint3 DTid : SV_DispatchThreadID)
{
    int x = DTid.x;
    if (x >= width)
        return;
   
    //for (int yy = 0; yy < 100; yy++)
    //    output[uint2(x, yy)] = 120 / 255.0;// input[yy] / 255.0;
    //return;
    
    int y = 0;
    int previous = 0;
    int indexCompressed = (x == 0) ? 0 : columnHeaders[x - 1];

    while (indexCompressed < columnHeaders[x])
    {
        int value = input[indexCompressed++];
        if (value == RepeatShort)
        {
            int count = input[indexCompressed++];
            for (int i = 0; i < count; i++)
                output[uint2(x, y++)] = previous / 255.0;
        }
        else if (value == RepeatLong)
        {
            int count = input[indexCompressed++] * 256 + input[indexCompressed++];
            for (int i = 0; i < count; i++)
                output[uint2(x, y++)] = previous / 255.0;
        }
        else
            output[uint2(x, y++)] = value / 255.0;
        previous = value;
    }

    while (y < height)
    {
        output[uint2(x, y++)] = previous / 255.0;
    }
}

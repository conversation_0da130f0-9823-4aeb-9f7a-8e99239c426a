﻿<UserControl x:Class="AnglePickerControl.View.Controls.AnglePicker"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:AnglePickerControl.View.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800" d:DataContext="{d:DesignInstance local:AnglePicker}">

    <Grid x:Name="TopContainer">
        <Viewbox>
            <Canvas Name="MainCanvas" Width="100" Height="100" Background="{Binding BackgroundOuter}">
                <Ellipse Name="OuterEllipse" Width="100" Height="100" Margin="0"
                         Stroke="{Binding StrokeCircle}"
                         StrokeThickness="2"
                         Fill="{Binding BackgroundInner}" 
                         MouseDown="AnglePickerEllipse_OnMouseDown"
                         MouseMove="OuterEllipse_OnMouseMove"></Ellipse>
                <Line Name="AngleLine" X1="50" Y1="50" X2="98" Y2="50" Stroke="{Binding StrokeAngleLine}" StrokeThickness="3">
                    <Line.RenderTransform>
                        <RotateTransform Angle="{Binding ReverseAngle}" 
                                     CenterX="50"
                                     CenterY="50"></RotateTransform>
                    </Line.RenderTransform>
                </Line>
                <Ellipse Name="CenterEllipse" Width="6" Height="6" Stroke="{Binding StrokeCircle}" Fill="{Binding StrokeCircle}" Margin="47,47,0,0"></Ellipse>
            </Canvas>
        </Viewbox>
    </Grid>
</UserControl>

﻿using System;
using System.Numerics;
using System.Runtime.InteropServices;
using Vortice;
using Vortice.Direct2D1;
using Vortice.Mathematics;

namespace DirectX;

[StructLayout(LayoutKind.Sequential, Pack = 4)]
public struct OffsetEffectParameters
{
    public Int2 Offsets;
}

[CustomEffect(["Source1"], null, "A shader", "PSB", "PSB")]
public class OffsetEffect : PVShaderBase
{
    static readonly Guid sGuid = Guid.NewGuid();
    public OffsetEffect() : base(sGuid)
    {
        InputCount = 1;
    }

    public OffsetEffectParameters cBuffer;

    ID2D1OffsetTransform _offsetTransform;

    public override void SetGraph(ID2D1TransformGraph tg)
    {
        transformGraph?.Dispose();
        transformGraph = tg;
        InputCount = tg.InputCount;
        // We put a temporary offset, not zero because it would bug.
        _offsetTransform = effectContext.CreateOffsetTransform(new Int2(10, 10));
        cBuffer.Offsets = new(1000000, 1000000); // Pour nous assurer que Direct2D détecte que ces valeurs ont changé et qu'il appelle le setter de Offsets.
        tg.SetSingleTransformNode(_offsetTransform);
    }

    [CustomEffectProperty(PropertyType.IUnknown, 0)]
    public Vector2 Offsets
    {
        get => new(cBuffer.Offsets.X, cBuffer.Offsets.Y);
        set
        {
            // Il faut passer d'un format à l'autre, car Vortice n'inclut pas Int2 dans son énumération.
            Int2 offsetsInt2 = new((int)(value.X + 0.5f), (int)(value.Y + 0.5f));
            cBuffer.Offsets = offsetsInt2;
            _offsetTransform.Offset = offsetsInt2;
        }
    }
    public override void PrepareForRender(ChangeType changeType) //Passes the struct to Gpu. Without this, it would be useless.
    {
        dInfo?.SetPixelShaderConstantBuffer(cBuffer);
    }
    public override RawRect MapInvalidRect(uint inputIndex, RawRect invalidInputRect)
    {
        return new RawRect(int.MinValue, int.MinValue, int.MaxValue, int.MaxValue);
    }
    public override void MapInputRectsToOutputRect(RawRect[] inputRects, RawRect[] inputOpaqueSubRects, out RawRect outputRect, out RawRect outputOpaqueSubRect)
    {
        outputRect = new(
            (int)(inputRects[0].Left + Offsets.X + 0.5f),
            (int)(inputRects[0].Top + Offsets.Y + 0.5f),
            (int)(inputRects[0].Right + 2 * Offsets.X + 0.5f),
            (int)(inputRects[0].Bottom + 2 * Offsets.Y + 0.5f)
            );
        outputOpaqueSubRect = new(
            (int)(inputOpaqueSubRects[0].Left + Offsets.X + 0.5f),
            (int)(inputOpaqueSubRects[0].Top + Offsets.Y + 0.5f),
            (int)(inputOpaqueSubRects[0].Right + 2 * Offsets.X + 0.5f),
            (int)(inputOpaqueSubRects[0].Bottom + 2 * Offsets.Y + 0.5f)
            );
    }
    public override void MapOutputRectToInputRects(RawRect outputRect, RawRect[] inputRects)
    {
        inputRects[0] = new(int.MinValue, int.MinValue, int.MaxValue, int.MaxValue);
        //inputRects[0] = new(left, top, right, bottom);
    }
}
